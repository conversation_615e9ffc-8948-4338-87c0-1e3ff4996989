{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\apply.vue", "mtime": 1754142645182}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demand", "require", "_team", "_data", "name", "data", "form", "planTitle", "vehicleType", "vehicleModel", "demandUnit", "demandQuantity", "demandStartTime", "demandEndTime", "usagePurpose", "teamId", "applicant", "applicantPhone", "remark", "submitLoading", "vehicleTypeOptions", "vehicleModelOptions", "teamOptions", "rules", "required", "message", "trigger", "min", "max", "type", "pattern", "created", "loadOptions", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "vehicleTypeRes", "teamRes", "_t", "w", "_context", "p", "n", "getDicts", "v", "getTeamOptions", "$modal", "msgError", "a", "handleVehicleTypeChange", "value", "_this2", "_callee2", "modelRes", "_t2", "_context2", "console", "warn", "handleTeamChange", "selectedTeam", "find", "team", "<PERSON><PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "submitForm", "_this3", "$refs", "validate", "valid", "Date", "submitDemand", "then", "response", "msgSuccess", "goBack", "catch", "saveDraft", "_this4", "draftData", "_objectSpread2", "approvalStatus", "<PERSON><PERSON><PERSON><PERSON>", "resetForm", "resetFields", "$router", "push"], "sources": ["src/views/vehicle/demand/apply.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">车辆需求计划申请</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\" size=\"medium\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"计划标题\" prop=\"planTitle\">\n              <el-input v-model=\"form.planTitle\" placeholder=\"请输入计划标题\" maxlength=\"100\" show-word-limit />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求单位\" prop=\"demandUnit\">\n              <el-input v-model=\"form.demandUnit\" placeholder=\"请输入需求单位\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\" @change=\"handleVehicleTypeChange\">\n                <el-option\n                  v-for=\"type in vehicleTypeOptions\"\n                  :key=\"type.dictValue\"\n                  :label=\"type.dictLabel\"\n                  :value=\"type.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-select v-model=\"form.vehicleModel\" placeholder=\"请选择车辆型号\" :disabled=\"!form.vehicleType\">\n                <el-option\n                  v-for=\"model in vehicleModelOptions\"\n                  :key=\"model.dictValue\"\n                  :label=\"model.dictLabel\"\n                  :value=\"model.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"队伍信息\" prop=\"teamId\">\n              <el-select v-model=\"form.teamId\" placeholder=\"请选择队伍\" @change=\"handleTeamChange\">\n                <el-option\n                  v-for=\"team in teamOptions\"\n                  :key=\"team.teamId\"\n                  :label=\"team.teamName\"\n                  :value=\"team.teamId\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请人\" prop=\"applicant\">\n              <el-input v-model=\"form.applicant\" placeholder=\"请输入申请人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"applicantPhone\">\n              <el-input v-model=\"form.applicantPhone\" placeholder=\"请输入联系电话\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求数量\" prop=\"demandQuantity\">\n              <el-input-number v-model=\"form.demandQuantity\" :min=\"1\" :max=\"100\" placeholder=\"请输入需求数量\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求开始时间\" prop=\"demandStartTime\">\n              <el-date-picker\n                v-model=\"form.demandStartTime\"\n                type=\"datetime\"\n                placeholder=\"请选择需求开始时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求结束时间\" prop=\"demandEndTime\">\n              <el-date-picker\n                v-model=\"form.demandEndTime\"\n                type=\"datetime\"\n                placeholder=\"请选择需求结束时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"用途说明\" prop=\"usagePurpose\">\n          <el-input\n            v-model=\"form.usagePurpose\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请详细说明车辆用途\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息\"\n            maxlength=\"200\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">\n            <i class=\"el-icon-check\"></i> 提交申请\n          </el-button>\n          <el-button @click=\"resetForm\">\n            <i class=\"el-icon-refresh-left\"></i> 重置\n          </el-button>\n          <el-button @click=\"saveDraft\">\n            <i class=\"el-icon-document\"></i> 保存草稿\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { submitDemand, addDemand } from \"@/api/vehicle/demand\";\nimport { getTeamOptions } from \"@/api/vehicle/team\";\nimport { getDicts } from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"DemandApply\",\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planTitle: '',\n        vehicleType: '',\n        vehicleModel: '',\n        demandUnit: '',\n        demandQuantity: 1,\n        demandStartTime: '',\n        demandEndTime: '',\n        usagePurpose: '',\n        teamId: null,\n        applicant: '',\n        applicantPhone: '',\n        remark: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 选项数据\n      vehicleTypeOptions: [],\n      vehicleModelOptions: [],\n      teamOptions: [],\n      // 表单验证规则\n      rules: {\n        planTitle: [\n          { required: true, message: \"计划标题不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: \"长度在 2 到 100 个字符\", trigger: \"blur\" }\n        ],\n        vehicleType: [\n          { required: true, message: \"请选择车辆类型\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"请选择车辆型号\", trigger: \"change\" }\n        ],\n        demandUnit: [\n          { required: true, message: \"需求单位不能为空\", trigger: \"blur\" }\n        ],\n        demandQuantity: [\n          { required: true, message: \"需求数量不能为空\", trigger: \"blur\" },\n          { type: 'number', min: 1, message: \"需求数量至少为1\", trigger: \"blur\" }\n        ],\n        demandStartTime: [\n          { required: true, message: \"请选择需求开始时间\", trigger: \"change\" }\n        ],\n        demandEndTime: [\n          { required: true, message: \"请选择需求结束时间\", trigger: \"change\" }\n        ],\n        usagePurpose: [\n          { required: true, message: \"用途说明不能为空\", trigger: \"blur\" },\n          { min: 10, max: 500, message: \"长度在 10 到 500 个字符\", trigger: \"blur\" }\n        ],\n        teamId: [\n          { required: true, message: \"请选择队伍\", trigger: \"change\" }\n        ],\n        applicant: [\n          { required: true, message: \"申请人不能为空\", trigger: \"blur\" }\n        ],\n        applicantPhone: [\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadOptions();\n  },\n  methods: {\n    /** 加载选项数据 */\n    async loadOptions() {\n      try {\n        // 加载车辆类型字典\n        const vehicleTypeRes = await getDicts(\"vehicle_type\");\n        this.vehicleTypeOptions = vehicleTypeRes.data;\n        \n        // 加载队伍选项\n        const teamRes = await getTeamOptions();\n        this.teamOptions = teamRes.data;\n      } catch (error) {\n        this.$modal.msgError(\"加载选项数据失败\");\n      }\n    },\n    \n    /** 车辆类型变化处理 */\n    async handleVehicleTypeChange(value) {\n      this.form.vehicleModel = '';\n      this.vehicleModelOptions = [];\n      \n      if (value) {\n        try {\n          // 根据车辆类型加载对应的型号\n          const modelRes = await getDicts(\"vehicle_model_\" + value);\n          this.vehicleModelOptions = modelRes.data;\n        } catch (error) {\n          console.warn(\"加载车辆型号失败:\", error);\n        }\n      }\n    },\n    \n    /** 队伍变化处理 */\n    handleTeamChange(teamId) {\n      const selectedTeam = this.teamOptions.find(team => team.teamId === teamId);\n      if (selectedTeam) {\n        this.form.applicant = selectedTeam.teamLeader;\n        this.form.applicantPhone = selectedTeam.leaderPhone;\n      }\n    },\n    \n    /** 提交表单 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 验证时间范围\n          if (new Date(this.form.demandEndTime) <= new Date(this.form.demandStartTime)) {\n            this.$modal.msgError(\"需求结束时间必须大于开始时间\");\n            return;\n          }\n          \n          this.submitLoading = true;\n          submitDemand(this.form).then(response => {\n            this.$modal.msgSuccess(\"需求计划申请提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 保存草稿 */\n    saveDraft() {\n      const draftData = { ...this.form, approvalStatus: 'draft' };\n      addDemand(draftData).then(response => {\n        this.$modal.msgSuccess(\"草稿保存成功\");\n      });\n    },\n    \n    /** 重置表单 */\n    resetForm() {\n      this.$refs[\"form\"].resetFields();\n      this.vehicleModelOptions = [];\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/demand');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-form-item {\n  margin-bottom: 22px;\n}\n\n.el-button {\n  margin-right: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;AAgJA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,aAAA;QACAC,YAAA;QACAC,MAAA;QACAC,SAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACA;MACAC,aAAA;MACA;MACAC,kBAAA;MACAC,mBAAA;MACAC,WAAA;MACA;MACAC,KAAA;QACAhB,SAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,WAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,YAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,UAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,cAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,IAAA;UAAAF,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,eAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,aAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,YAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,MAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,SAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,cAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,cAAA,EAAAC,OAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAC,cAAA;YAAA;cAAAP,cAAA,GAAAI,QAAA,CAAAI,CAAA;cACAd,KAAA,CAAAd,kBAAA,GAAAoB,cAAA,CAAAnC,IAAA;;cAEA;cAAAuC,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAG,oBAAA;YAAA;cAAAR,OAAA,GAAAG,QAAA,CAAAI,CAAA;cACAd,KAAA,CAAAZ,WAAA,GAAAmB,OAAA,CAAApC,IAAA;cAAAuC,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAd,KAAA,CAAAgB,MAAA,CAAAC,QAAA;YAAA;cAAA,OAAAP,QAAA,CAAAQ,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEA,eACAc,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAApB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAkB,SAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA;QAAA,WAAArB,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAgB,SAAA;UAAA,kBAAAA,SAAA,CAAAd,CAAA,GAAAc,SAAA,CAAAb,CAAA;YAAA;cACAS,MAAA,CAAAjD,IAAA,CAAAG,YAAA;cACA8C,MAAA,CAAAlC,mBAAA;cAAA,KAEAiC,KAAA;gBAAAK,SAAA,CAAAb,CAAA;gBAAA;cAAA;cAAAa,SAAA,CAAAd,CAAA;cAAAc,SAAA,CAAAb,CAAA;cAAA,OAGA,IAAAC,cAAA,qBAAAO,KAAA;YAAA;cAAAG,QAAA,GAAAE,SAAA,CAAAX,CAAA;cACAO,MAAA,CAAAlC,mBAAA,GAAAoC,QAAA,CAAApD,IAAA;cAAAsD,SAAA,CAAAb,CAAA;cAAA;YAAA;cAAAa,SAAA,CAAAd,CAAA;cAAAa,GAAA,GAAAC,SAAA,CAAAX,CAAA;cAEAY,OAAA,CAAAC,IAAA,cAAAH,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAP,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAGA;IAEA,aACAM,gBAAA,WAAAA,iBAAA/C,MAAA;MACA,IAAAgD,YAAA,QAAAzC,WAAA,CAAA0C,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlD,MAAA,KAAAA,MAAA;MAAA;MACA,IAAAgD,YAAA;QACA,KAAAzD,IAAA,CAAAU,SAAA,GAAA+C,YAAA,CAAAG,UAAA;QACA,KAAA5D,IAAA,CAAAW,cAAA,GAAA8C,YAAA,CAAAI,WAAA;MACA;IACA;IAEA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,QAAAC,IAAA,CAAAJ,MAAA,CAAA/D,IAAA,CAAAO,aAAA,SAAA4D,IAAA,CAAAJ,MAAA,CAAA/D,IAAA,CAAAM,eAAA;YACAyD,MAAA,CAAAnB,MAAA,CAAAC,QAAA;YACA;UACA;UAEAkB,MAAA,CAAAlD,aAAA;UACA,IAAAuD,oBAAA,EAAAL,MAAA,CAAA/D,IAAA,EAAAqE,IAAA,WAAAC,QAAA;YACAP,MAAA,CAAAnB,MAAA,CAAA2B,UAAA;YACAR,MAAA,CAAAS,MAAA;UACA,GAAAC,KAAA;YACAV,MAAA,CAAAlD,aAAA;UACA;QACA;MACA;IACA;IAEA,WACA6D,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,OAAAC,cAAA,CAAA/C,OAAA,MAAA+C,cAAA,CAAA/C,OAAA,WAAA9B,IAAA;QAAA8E,cAAA;MAAA;MACA,IAAAC,iBAAA,EAAAH,SAAA,EAAAP,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAA/B,MAAA,CAAA2B,UAAA;MACA;IACA;IAEA,WACAS,SAAA,WAAAA,UAAA;MACA,KAAAhB,KAAA,SAAAiB,WAAA;MACA,KAAAlE,mBAAA;IACA;IAEA,WACAyD,MAAA,WAAAA,OAAA;MACA,KAAAU,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}