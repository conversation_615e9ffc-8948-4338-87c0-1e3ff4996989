{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue?vue&type=template&id=fd59f1fa&scoped=true", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue", "mtime": 1754139905404}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754135854671}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDwhLS0g5p+l6K+i5p2h5Lu2IC0tPgogIDxlbC1mb3JtIDptb2RlbD0icXVlcnlQYXJhbXMiIHJlZj0icXVlcnlGb3JtIiBzaXplPSJzbWFsbCIgOmlubGluZT0idHJ1ZSIgdi1zaG93PSJzaG93U2VhcmNoIiBsYWJlbC13aWR0aD0iNjhweCI+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlvIDlp4vml6XmnJ8iIHByb3A9InN0YXJ0RGF0ZSI+CiAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLnN0YXJ0RGF0ZSIKICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nlvIDlp4vml6XmnJ8iCiAgICAgICAgdmFsdWUtZm9ybWF0PSJ5eXl5LU1NLWRkIj4KICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57uT5p2f5pel5pyfIiBwcm9wPSJlbmREYXRlIj4KICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuZW5kRGF0ZSIKICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nnu5PmnZ/ml6XmnJ8iCiAgICAgICAgdmFsdWUtZm9ybWF0PSJ5eXl5LU1NLWRkIj4KICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6L2m6L6G57G75Z6LIiBwcm9wPSJ2ZWhpY2xlVHlwZSI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMudmVoaWNsZVR5cGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6novabovobnsbvlnosiIGNsZWFyYWJsZT4KICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICB2LWZvcj0iZGljdCBpbiBkaWN0LnR5cGUudmVoaWNsZV90eXBlIgogICAgICAgICAgOmtleT0iZGljdC52YWx1ZSIKICAgICAgICAgIDpsYWJlbD0iZGljdC5sYWJlbCIKICAgICAgICAgIDp2YWx1ZT0iZGljdC52YWx1ZSIKICAgICAgICAvPgogICAgICA8L2VsLXNlbGVjdD4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbT4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXNlYXJjaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVRdWVyeSI+5pCc57SiPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc2V0UXVlcnkiPumHjee9rjwvZWwtYnV0dG9uPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgPC9lbC1mb3JtPgoKICA8IS0tIOe7n+iuoeWNoeeJhyAtLT4KICA8ZWwtcm93IDpndXR0ZXI9IjIwIiBjbGFzcz0ibWIyMCI+CiAgICA8ZWwtY29sIDpzcGFuPSI2Ij4KICAgICAgPGVsLWNhcmQgY2xhc3M9ImJveC1jYXJkIj4KICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWNhcmQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1pY29uIiBzdHlsZT0iYmFja2dyb3VuZDogIzQwOUVGRjsiPgogICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi10cnVjayIgc3R5bGU9ImNvbG9yOiB3aGl0ZTsgZm9udC1zaXplOiAyNHB4OyI+PC9pPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWNvbnRlbnQiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LW51bWJlciI+e3sgb3ZlcmFsbFN0YXRzLnRvdGFsVmVoaWNsZXMgfHwgMCB9fTwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWxhYmVsIj7ovabovobmgLvmlbA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWNhcmQ+CiAgICA8L2VsLWNvbD4KICAgIDxlbC1jb2wgOnNwYW49IjYiPgogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgICAgIDxkaXYgY2xhc3M9InN0YXQtY2FyZCI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWljb24iIHN0eWxlPSJiYWNrZ3JvdW5kOiAjNjdDMjNBOyI+CiAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLXMtb3JkZXIiIHN0eWxlPSJjb2xvcjogd2hpdGU7IGZvbnQtc2l6ZTogMjRweDsiPjwvaT4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1jb250ZW50Ij4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1udW1iZXIiPnt7IG92ZXJhbGxTdGF0cy50b3RhbE9yZGVycyB8fCAwIH19PC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtbGFiZWwiPuiuouWNleaAu+aVsDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iNiI+CiAgICAgIDxlbC1jYXJkIGNsYXNzPSJib3gtY2FyZCI+CiAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1jYXJkIj4KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtaWNvbiIgc3R5bGU9ImJhY2tncm91bmQ6ICNFNkEyM0M7Ij4KICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tdGltZSIgc3R5bGU9ImNvbG9yOiB3aGl0ZTsgZm9udC1zaXplOiAyNHB4OyI+PC9pPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWNvbnRlbnQiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LW51bWJlciI+e3sgb3ZlcmFsbFN0YXRzLnRvdGFsV29ya0hvdXJzIHx8IDAgfX08L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1sYWJlbCI+5oC75bel5L2c5pe26ZW/PC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9lbC1jYXJkPgogICAgPC9lbC1jb2w+CiAgICA8ZWwtY29sIDpzcGFuPSI2Ij4KICAgICAgPGVsLWNhcmQgY2xhc3M9ImJveC1jYXJkIj4KICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0LWNhcmQiPgogICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdC1pY29uIiBzdHlsZT0iYmFja2dyb3VuZDogI0Y1NkM2QzsiPgogICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi13YXJuaW5nIiBzdHlsZT0iY29sb3I6IHdoaXRlOyBmb250LXNpemU6IDI0cHg7Ij48L2k+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtY29udGVudCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtbnVtYmVyIj57eyBvdmVyYWxsU3RhdHMudXRpbGl6YXRpb25SYXRlIHx8IDAgfX0lPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXQtbGFiZWwiPui9pui+huWIqeeUqOeOhzwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogIDwvZWwtcm93PgoKICA8IS0tIOWbvuihqOWMuuWfnyAtLT4KICA8ZWwtcm93IDpndXR0ZXI9IjIwIj4KICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgPGVsLWNhcmQgY2xhc3M9ImJveC1jYXJkIj4KICAgICAgICA8ZGl2IHNsb3Q9ImhlYWRlciIgY2xhc3M9ImNsZWFyZml4Ij4KICAgICAgICAgIDxzcGFuPui9pui+huexu+Wei+e7n+iuoTwvc3Bhbj4KICAgICAgICAgIDxlbC1idXR0b24gc3R5bGU9ImZsb2F0OiByaWdodDsgcGFkZGluZzogM3B4IDAiIHR5cGU9InRleHQiIEBjbGljaz0icmVmcmVzaFZlaGljbGVUeXBlQ2hhcnQiPuWIt+aWsDwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgaWQ9InZlaGljbGVUeXBlQ2hhcnQiIHN0eWxlPSJoZWlnaHQ6IDMwMHB4OyI+PC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6Zif5LyN55So6L2m57uf6K6hPC9zcGFuPgogICAgICAgICAgPGVsLWJ1dHRvbiBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyBwYWRkaW5nOiAzcHggMCIgdHlwZT0idGV4dCIgQGNsaWNrPSJyZWZyZXNoVGVhbUNoYXJ0Ij7liLfmlrA8L2VsLWJ1dHRvbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGlkPSJ0ZWFtQ2hhcnQiIHN0eWxlPSJoZWlnaHQ6IDMwMHB4OyI+PC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogIDwvZWwtcm93PgoKICA8ZWwtcm93IDpndXR0ZXI9IjIwIiBzdHlsZT0ibWFyZ2luLXRvcDogMjBweDsiPgogICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+5pe26Ze06LaL5Yq/57uf6K6hPC9zcGFuPgogICAgICAgICAgPGVsLWJ1dHRvbi1ncm91cCBzdHlsZT0iZmxvYXQ6IHJpZ2h0OyI+CiAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ibWluaSIgQGNsaWNrPSJjaGFuZ2VUaW1lUmFuZ2UoJ2RheScpIiA6dHlwZT0idGltZVJhbmdlID09PSAnZGF5JyA/ICdwcmltYXJ5JyA6ICcnIj7mjInlpKk8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJtaW5pIiBAY2xpY2s9ImNoYW5nZVRpbWVSYW5nZSgnbW9udGgnKSIgOnR5cGU9InRpbWVSYW5nZSA9PT0gJ21vbnRoJyA/ICdwcmltYXJ5JyA6ICcnIj7mjInmnIg8L2VsLWJ1dHRvbj4KICAgICAgICAgIDwvZWwtYnV0dG9uLWdyb3VwPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgaWQ9InRyZW5kQ2hhcnQiIHN0eWxlPSJoZWlnaHQ6IDQwMHB4OyI+PC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogIDwvZWwtcm93PgoKICA8IS0tIOaOkuihjOamnCAtLT4KICA8ZWwtcm93IDpndXR0ZXI9IjIwIiBzdHlsZT0ibWFyZ2luLXRvcDogMjBweDsiPgogICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6L2m6L6G5L2/55So5o6S6KGM5qacPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC10YWJsZSA6ZGF0YT0idmVoaWNsZVJhbmtpbmciIHN0eWxlPSJ3aWR0aDogMTAwJSIgc2l6ZT0ic21hbGwiPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJyYW5rIiBsYWJlbD0i5o6S5ZCNIiB3aWR0aD0iNjAiIGFsaWduPSJjZW50ZXIiIC8+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InZlaGljbGVNb2RlbCIgbGFiZWw9Iui9pui+huWei+WPtyIgLz4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0idXNhZ2VDb3VudCIgbGFiZWw9IuS9v+eUqOasoeaVsCIgYWxpZ249ImNlbnRlciIgLz4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0id29ya0hvdXJzIiBsYWJlbD0i5bel5L2c5pe26ZW/IiBhbGlnbj0iY2VudGVyIiAvPgogICAgICAgIDwvZWwtdGFibGU+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6Zif5LyN55So6L2m5o6S6KGM5qacPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC10YWJsZSA6ZGF0YT0idGVhbVJhbmtpbmciIHN0eWxlPSJ3aWR0aDogMTAwJSIgc2l6ZT0ic21hbGwiPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJyYW5rIiBsYWJlbD0i5o6S5ZCNIiB3aWR0aD0iNjAiIGFsaWduPSJjZW50ZXIiIC8+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InRlYW1OYW1lIiBsYWJlbD0i6Zif5LyN5ZCN56ewIiAvPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJvcmRlckNvdW50IiBsYWJlbD0i6K6i5Y2V5pWw6YePIiBhbGlnbj0iY2VudGVyIiAvPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJ3b3JrSG91cnMiIGxhYmVsPSLlt6XkvZzml7bplb8iIGFsaWduPSJjZW50ZXIiIC8+CiAgICAgICAgPC9lbC10YWJsZT4KICAgICAgPC9lbC1jYXJkPgogICAgPC9lbC1jb2w+CiAgPC9lbC1yb3c+CgogIDwhLS0g5a+85Ye65oyJ6ZKuIC0tPgogIDxlbC1yb3cgc3R5bGU9Im1hcmdpbi10b3A6IDIwcHg7Ij4KICAgIDxlbC1jb2wgOnNwYW49IjI0IiBzdHlsZT0idGV4dC1hbGlnbjogY2VudGVyOyI+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgaWNvbj0iZWwtaWNvbi1kb3dubG9hZCIgQGNsaWNrPSJoYW5kbGVFeHBvcnQiPuWvvOWHuue7n+iuoeaKpeihqDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InN1Y2Nlc3MiIGljb249ImVsLWljb24tcmVmcmVzaCIgQGNsaWNrPSJoYW5kbGVSZWZyZXNoQWxsIj7liLfmlrDmiYDmnInmlbDmja48L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPgogIDwvZWwtcm93Pgo8L2Rpdj4K"}, null]}