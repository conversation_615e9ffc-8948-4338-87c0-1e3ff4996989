-- 车辆管理系统菜单配置脚本（最终修正版本）
-- 解决了列数匹配问题，使用最简单可靠的语法

-- ========================================
-- 第一步：创建主菜单
-- ========================================
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('车辆管理', 0, 5, 'vehicle', NULL, 1, 0, 'M', '0', '0', '', 'tool', 'admin', now(), '车辆管理系统');

-- ========================================
-- 第二步：获取主菜单ID并创建子菜单
-- ========================================
SET @vehicle_main_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '车辆管理' AND parent_id = 0);

-- 车辆信息管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('车辆信息', @vehicle_main_id, 1, 'info', 'vehicle/info/index', 1, 0, 'C', '0', '0', 'vehicle:info:list', 'car', 'admin', now(), '车辆信息管理');

-- 用车申请管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('用车申请', @vehicle_main_id, 2, 'application', 'vehicle/application/index', 1, 0, 'C', '0', '0', 'vehicle:application:list', 'form', 'admin', now(), '用车申请管理');

-- 用车订单管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('用车订单', @vehicle_main_id, 3, 'order', 'vehicle/order/index', 1, 0, 'C', '0', '0', 'vehicle:order:list', 'shopping', 'admin', now(), '用车订单管理');

-- 队伍信息管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('队伍信息', @vehicle_main_id, 4, 'team', 'vehicle/team/index', 1, 0, 'C', '0', '0', 'vehicle:team:list', 'peoples', 'admin', now(), '队伍信息管理');

-- 维修记录管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('维修记录', @vehicle_main_id, 5, 'maintenance', 'vehicle/maintenance/index', 1, 0, 'C', '0', '0', 'vehicle:maintenance:list', 'build', 'admin', now(), '车辆维修记录管理');

-- 违章记录管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('违章记录', @vehicle_main_id, 6, 'violation', 'vehicle/violation/index', 1, 0, 'C', '0', '0', 'vehicle:violation:list', 'example', 'admin', now(), '车辆违章记录管理');

-- 需求计划管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES ('需求计划', @vehicle_main_id, 7, 'demand', 'vehicle/demand/index', 1, 0, 'C', '0', '0', 'vehicle:demand:list', 'list', 'admin', now(), '车辆需求计划管理');

-- ========================================
-- 第三步：添加按钮权限
-- ========================================

-- 获取各个子菜单的ID
SET @info_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:info:list');
SET @app_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:application:list');
SET @order_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:order:list');
SET @team_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:team:list');
SET @maint_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:maintenance:list');
SET @viol_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:violation:list');
SET @demand_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'vehicle:demand:list');

-- 车辆信息按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('车辆查询', @info_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:info:query', '#', 'admin', now(), ''),
('车辆新增', @info_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:info:add', '#', 'admin', now(), ''),
('车辆修改', @info_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:info:edit', '#', 'admin', now(), ''),
('车辆删除', @info_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:info:remove', '#', 'admin', now(), ''),
('车辆导出', @info_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:info:export', '#', 'admin', now(), '');

-- 用车申请按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('申请查询', @app_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:query', '#', 'admin', now(), ''),
('申请新增', @app_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:add', '#', 'admin', now(), ''),
('申请修改', @app_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:edit', '#', 'admin', now(), ''),
('申请删除', @app_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:remove', '#', 'admin', now(), ''),
('申请审批', @app_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:approve', '#', 'admin', now(), ''),
('车辆调度', @app_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:dispatch', '#', 'admin', now(), ''),
('申请导出', @app_menu_id, 7, '', '', 1, 0, 'F', '0', '0', 'vehicle:application:export', '#', 'admin', now(), '');

-- 用车订单按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('订单查询', @order_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:order:query', '#', 'admin', now(), ''),
('订单确认', @order_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:order:confirm', '#', 'admin', now(), ''),
('订单退回', @order_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:order:reject', '#', 'admin', now(), ''),
('费用计算', @order_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:order:calculate', '#', 'admin', now(), ''),
('订单导出', @order_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:order:export', '#', 'admin', now(), '');

-- 队伍信息按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('队伍查询', @team_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:team:query', '#', 'admin', now(), ''),
('队伍新增', @team_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:team:add', '#', 'admin', now(), ''),
('队伍修改', @team_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:team:edit', '#', 'admin', now(), ''),
('队伍删除', @team_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:team:remove', '#', 'admin', now(), ''),
('队伍导出', @team_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:team:export', '#', 'admin', now(), '');

-- 维修记录按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('维修查询', @maint_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:maintenance:query', '#', 'admin', now(), ''),
('维修新增', @maint_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:maintenance:add', '#', 'admin', now(), ''),
('维修修改', @maint_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:maintenance:edit', '#', 'admin', now(), ''),
('维修删除', @maint_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:maintenance:remove', '#', 'admin', now(), ''),
('维修导出', @maint_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:maintenance:export', '#', 'admin', now(), '');

-- 违章记录按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('违章查询', @viol_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:query', '#', 'admin', now(), ''),
('违章新增', @viol_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:add', '#', 'admin', now(), ''),
('违章修改', @viol_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:edit', '#', 'admin', now(), ''),
('违章删除', @viol_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:remove', '#', 'admin', now(), ''),
('违章处理', @viol_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:process', '#', 'admin', now(), ''),
('违章导出', @viol_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'vehicle:violation:export', '#', 'admin', now(), '');

-- 需求计划按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('需求查询', @demand_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:query', '#', 'admin', now(), ''),
('需求新增', @demand_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:add', '#', 'admin', now(), ''),
('需求修改', @demand_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:edit', '#', 'admin', now(), ''),
('需求删除', @demand_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:remove', '#', 'admin', now(), ''),
('需求审批', @demand_menu_id, 5, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:approve', '#', 'admin', now(), ''),
('需求导出', @demand_menu_id, 6, '', '', 1, 0, 'F', '0', '0', 'vehicle:demand:export', '#', 'admin', now(), '');

-- ========================================
-- 第四步：为admin角色分配权限
-- ========================================
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'vehicle:%' OR menu_name = '车辆管理';

-- ========================================
-- 完成
-- ========================================
COMMIT;
SELECT '车辆管理系统菜单配置完成！请重启后端服务并刷新前端页面。' AS message;
