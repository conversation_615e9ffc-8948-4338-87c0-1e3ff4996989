{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\index.vue", "mtime": 1754142701700}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZW1hbmQsIGdldERlbWFuZCwgZGVsRGVtYW5kLCBnZXRNeURlbWFuZHMgfSBmcm9tICJAL2FwaS92ZWhpY2xlL2RlbWFuZCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRlbWFuZCIsCiAgZGljdHM6IFsndmVoaWNsZV90eXBlJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDpnIDmsYLorqHliJLooajmoLzmlbDmja4KICAgICAgZGVtYW5kTGlzdDogW10sCiAgICAgIC8vIOaYr+WQpuaYvuekuuivpuaDheW8ueWHuuWxggogICAgICBkZXRhaWxPcGVuOiBmYWxzZSwKICAgICAgLy8g6K+m5oOF6KGo5Y2V5Y+C5pWwCiAgICAgIGRldGFpbEZvcm06IHt9LAogICAgICAvLyDnlLPor7fml7bpl7Tml7bpl7TojIPlm7QKICAgICAgZGF0ZXJhbmdlQ3JlYXRlVGltZTogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBsYW5UaXRsZTogbnVsbCwKICAgICAgICB2ZWhpY2xlVHlwZTogbnVsbCwKICAgICAgICBhcHByb3ZhbFN0YXR1czogbnVsbAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoumcgOaxguiuoeWIkuWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMgPSB7fTsKICAgICAgaWYgKG51bGwgIT0gdGhpcy5kYXRlcmFuZ2VDcmVhdGVUaW1lICYmICcnICE9IHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJiZWdpbkNyZWF0ZVRpbWUiXSA9IHRoaXMuZGF0ZXJhbmdlQ3JlYXRlVGltZVswXTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siZW5kQ3JlYXRlVGltZSJdID0gdGhpcy5kYXRlcmFuZ2VDcmVhdGVUaW1lWzFdOwogICAgICB9CiAgICAgIGxpc3REZW1hbmQodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZW1hbmRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVyYW5nZUNyZWF0ZVRpbWUgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucGxhbklkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy92ZWhpY2xlL2RlbWFuZC9hcHBseScpOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgY29uc3QgcGxhbklkID0gcm93LnBsYW5JZCB8fCB0aGlzLmlkc1swXTsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goJy92ZWhpY2xlL2RlbWFuZC9lZGl0LycgKyBwbGFuSWQpOwogICAgfSwKICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIGdldERlbWFuZChyb3cucGxhbklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRldGFpbEZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlrqHmibnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFwcHJvdmUocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvdmVoaWNsZS9kZW1hbmQvYXBwcm92ZS8nICsgcm93LnBsYW5JZCk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBwbGFuSWRzID0gcm93LnBsYW5JZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6ZyA5rGC6K6h5YiS57yW5Y+35Li6IicgKyBwbGFuSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxEZW1hbmQocGxhbklkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOaIkeeahOeUs+ivtyAqLwogICAgaGFuZGxlTXlQbGFucygpIHsKICAgICAgZ2V0TXlEZW1hbmRzKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZW1hbmRMaXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS5sZW5ndGg7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5bey5YiH5o2i5Yiw5oiR55qE55Sz6K+3Iik7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgndmVoaWNsZS9kZW1hbmQvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGRlbWFuZF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCiAgICAvKiog6I635Y+W54q25oCB5qCH562+57G75Z6LICovCiAgICBnZXRTdGF0dXNUYWdUeXBlKHN0YXR1cykgewogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7CiAgICAgICAgJ2RyYWZ0JzogJ2luZm8nLAogICAgICAgICdwZW5kaW5nX2xldmVsMSc6ICd3YXJuaW5nJywKICAgICAgICAncGVuZGluZ19sZXZlbDInOiAnd2FybmluZycsIAogICAgICAgICdwZW5kaW5nX2xldmVsMyc6ICd3YXJuaW5nJywKICAgICAgICAnYXBwcm92ZWQnOiAnc3VjY2VzcycsCiAgICAgICAgJ3JlamVjdGVkJzogJ2RhbmdlcicKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJzsKICAgIH0sCiAgICAvKiog6I635Y+W54q25oCB5paH5pysICovCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgewogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7CiAgICAgICAgJ2RyYWZ0JzogJ+iNieeovycsCiAgICAgICAgJ3BlbmRpbmdfbGV2ZWwxJzogJ+W+hemhueebruiwg+W6puWupOWuoeaJuScsCiAgICAgICAgJ3BlbmRpbmdfbGV2ZWwyJzogJ+W+heacuuaisOS4u+euoeWuoeaJuScsCiAgICAgICAgJ3BlbmRpbmdfbGV2ZWwzJzogJ+W+hee7j+iQpemDqOmXqOWuoeaJuScsCiAgICAgICAgJ2FwcHJvdmVkJzogJ+W3sumAmui/hycsCiAgICAgICAgJ3JlamVjdGVkJzogJ+W3suaLkue7nScKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOwogICAgfSwKICAgIC8qKiDliKTmlq3mmK/lkKblj6/ku6XlrqHmibkgKi8KICAgIGNhbkFwcHJvdmUoc3RhdHVzKSB7CiAgICAgIHJldHVybiBbJ3BlbmRpbmdfbGV2ZWwxJywgJ3BlbmRpbmdfbGV2ZWwyJywgJ3BlbmRpbmdfbGV2ZWwzJ10uaW5jbHVkZXMoc3RhdHVzKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/demand", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"计划标题\" prop=\"planTitle\">\n        <el-input\n          v-model=\"queryParams.planTitle\"\n          placeholder=\"请输入计划标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"审批状态\" prop=\"approvalStatus\">\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择审批状态\" clearable>\n          <el-option label=\"草稿\" value=\"draft\"></el-option>\n          <el-option label=\"待项目调度室审批\" value=\"pending_level1\"></el-option>\n          <el-option label=\"待机械主管审批\" value=\"pending_level2\"></el-option>\n          <el-option label=\"待经营部门审批\" value=\"pending_level3\"></el-option>\n          <el-option label=\"已通过\" value=\"approved\"></el-option>\n          <el-option label=\"已拒绝\" value=\"rejected\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"申请时间\">\n        <el-date-picker\n          v-model=\"daterangeCreateTime\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:demand:add']\"\n        >新增申请</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:demand:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:demand:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:demand:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-view\"\n          size=\"mini\"\n          @click=\"handleMyPlans\"\n          v-hasPermi=\"['vehicle:demand:list']\"\n        >我的申请</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"demandList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"计划ID\" align=\"center\" prop=\"planId\" width=\"80\" />\n      <el-table-column label=\"计划标题\" align=\"center\" prop=\"planTitle\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleType }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.vehicleModel }}</div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"需求单位\" align=\"center\" prop=\"demandUnit\" />\n      <el-table-column label=\"需求数量\" align=\"center\" prop=\"demandQuantity\" width=\"80\" />\n      <el-table-column label=\"需求时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <div>{{ parseTime(scope.row.demandStartTime, '{m}-{d} {h}:{i}') }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            至 {{ parseTime(scope.row.demandEndTime, '{m}-{d} {h}:{i}') }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicant\" width=\"100\" />\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"getStatusTagType(scope.row.approvalStatus)\"\n            size=\"mini\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:query']\"\n          >查看</el-button>\n          <el-button\n            v-if=\"scope.row.approvalStatus === 'draft'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:edit']\"\n          >修改</el-button>\n          <el-button\n            v-if=\"canApprove(scope.row.approvalStatus)\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleApprove(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:approve']\"\n          >审批</el-button>\n          <el-button\n            v-if=\"scope.row.approvalStatus === 'draft'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看详情对话框 -->\n    <el-dialog title=\"需求计划详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"计划标题\" :span=\"2\">{{ detailForm.planTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆类型\">{{ detailForm.vehicleType }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆型号\">{{ detailForm.vehicleModel }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求单位\">{{ detailForm.demandUnit }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求数量\">{{ detailForm.demandQuantity }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求开始时间\">{{ parseTime(detailForm.demandStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求结束时间\">{{ parseTime(detailForm.demandEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请人\">{{ detailForm.applicant }}</el-descriptions-item>\n        <el-descriptions-item label=\"联系电话\">{{ detailForm.applicantPhone }}</el-descriptions-item>\n        <el-descriptions-item label=\"审批状态\">\n          <el-tag :type=\"getStatusTagType(detailForm.approvalStatus)\">\n            {{ getStatusText(detailForm.approvalStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"申请时间\">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用途说明\" :span=\"2\">{{ detailForm.usagePurpose }}</el-descriptions-item>\n        <el-descriptions-item v-if=\"detailForm.approvalComments\" label=\"审批意见\" :span=\"2\">\n          {{ detailForm.approvalComments }}\n        </el-descriptions-item>\n        <el-descriptions-item v-if=\"detailForm.remark\" label=\"备注\" :span=\"2\">{{ detailForm.remark }}</el-descriptions-item>\n      </el-descriptions>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listDemand, getDemand, delDemand, getMyDemands } from \"@/api/vehicle/demand\";\n\nexport default {\n  name: \"Demand\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 需求计划表格数据\n      demandList: [],\n      // 是否显示详情弹出层\n      detailOpen: false,\n      // 详情表单参数\n      detailForm: {},\n      // 申请时间时间范围\n      daterangeCreateTime: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        planTitle: null,\n        vehicleType: null,\n        approvalStatus: null\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询需求计划列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {\n        this.queryParams.params[\"beginCreateTime\"] = this.daterangeCreateTime[0];\n        this.queryParams.params[\"endCreateTime\"] = this.daterangeCreateTime[1];\n      }\n      listDemand(this.queryParams).then(response => {\n        this.demandList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.daterangeCreateTime = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.planId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.$router.push('/vehicle/demand/apply');\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const planId = row.planId || this.ids[0];\n      this.$router.push('/vehicle/demand/edit/' + planId);\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      getDemand(row.planId).then(response => {\n        this.detailForm = response.data;\n        this.detailOpen = true;\n      });\n    },\n    /** 审批按钮操作 */\n    handleApprove(row) {\n      this.$router.push('/vehicle/demand/approve/' + row.planId);\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const planIds = row.planId || this.ids;\n      this.$modal.confirm('是否确认删除需求计划编号为\"' + planIds + '\"的数据项？').then(function() {\n        return delDemand(planIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 我的申请 */\n    handleMyPlans() {\n      getMyDemands().then(response => {\n        this.demandList = response.data;\n        this.total = response.data.length;\n        this.$modal.msgSuccess(\"已切换到我的申请\");\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/demand/export', {\n        ...this.queryParams\n      }, `demand_${new Date().getTime()}.xlsx`)\n    },\n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'draft': 'info',\n        'pending_level1': 'warning',\n        'pending_level2': 'warning', \n        'pending_level3': 'warning',\n        'approved': 'success',\n        'rejected': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'draft': '草稿',\n        'pending_level1': '待项目调度室审批',\n        'pending_level2': '待机械主管审批',\n        'pending_level3': '待经营部门审批',\n        'approved': '已通过',\n        'rejected': '已拒绝'\n      };\n      return statusMap[status] || '未知';\n    },\n    /** 判断是否可以审批 */\n    canApprove(status) {\n      return ['pending_level1', 'pending_level2', 'pending_level3'].includes(status);\n    }\n  }\n};\n</script>\n"]}]}