package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleApplication;
import com.ruoyi.vehicle.service.IVehicleApplicationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 机械用车申请Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/application")
public class VehicleApplicationController extends BaseController
{
    @Autowired
    private IVehicleApplicationService vehicleApplicationService;

    /**
     * 查询机械用车申请列表
     */
    @RequiresPermissions("vehicle:application:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleApplication vehicleApplication)
    {
        startPage();
        List<VehicleApplication> list = vehicleApplicationService.selectVehicleApplicationList(vehicleApplication);
        return getDataTable(list);
    }

    /**
     * 导出机械用车申请列表
     */
    @RequiresPermissions("vehicle:application:export")
    @Log(title = "机械用车申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleApplication vehicleApplication)
    {
        List<VehicleApplication> list = vehicleApplicationService.selectVehicleApplicationList(vehicleApplication);
        ExcelUtil<VehicleApplication> util = new ExcelUtil<VehicleApplication>(VehicleApplication.class);
        util.exportExcel(response, list, "机械用车申请数据");
    }

    /**
     * 获取机械用车申请详细信息
     */
    @RequiresPermissions("vehicle:application:query")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(vehicleApplicationService.selectVehicleApplicationByApplicationId(applicationId));
    }

    /**
     * 新增机械用车申请
     */
    @RequiresPermissions("vehicle:application:add")
    @Log(title = "机械用车申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleApplication vehicleApplication)
    {
        vehicleApplication.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleApplicationService.insertVehicleApplication(vehicleApplication));
    }

    /**
     * 修改机械用车申请
     */
    @RequiresPermissions("vehicle:application:edit")
    @Log(title = "机械用车申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleApplication vehicleApplication)
    {
        vehicleApplication.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleApplicationService.updateVehicleApplication(vehicleApplication));
    }

    /**
     * 删除机械用车申请
     */
    @RequiresPermissions("vehicle:application:remove")
    @Log(title = "机械用车申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(vehicleApplicationService.deleteVehicleApplicationByApplicationIds(applicationIds));
    }

    /**
     * 提交用车申请
     */
    @RequiresPermissions("vehicle:application:submit")
    @Log(title = "提交用车申请", businessType = BusinessType.UPDATE)
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody VehicleApplication vehicleApplication)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleApplicationService.submitApplication(vehicleApplication, operName));
    }

    /**
     * 审批用车申请
     */
    @RequiresPermissions("vehicle:application:approve")
    @Log(title = "审批用车申请", businessType = BusinessType.UPDATE)
    @PutMapping("/approve/{applicationId}")
    public AjaxResult approve(@PathVariable Long applicationId, @RequestBody VehicleApplication vehicleApplication)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleApplicationService.approveApplication(
            applicationId, 
            vehicleApplication.getApprovalStatus(), 
            vehicleApplication.getRemark(), 
            operName
        ));
    }

    /**
     * 分配车辆和司机
     */
    @RequiresPermissions("vehicle:application:assign")
    @Log(title = "分配车辆和司机", businessType = BusinessType.UPDATE)
    @PutMapping("/assign/{applicationId}")
    public AjaxResult assign(@PathVariable Long applicationId, @RequestBody VehicleApplication vehicleApplication)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleApplicationService.assignVehicleAndDriver(
            applicationId, 
            vehicleApplication.getAssignedVehicleId(), 
            vehicleApplication.getAssignedDriver(), 
            operName
        ));
    }

    /**
     * 根据审批状态查询申请列表
     */
    @RequiresPermissions("vehicle:application:list")
    @GetMapping("/status/{approvalStatus}")
    public AjaxResult getByStatus(@PathVariable String approvalStatus)
    {
        List<VehicleApplication> list = vehicleApplicationService.selectVehicleApplicationByStatus(approvalStatus);
        return success(list);
    }

    /**
     * 查询待调度的申请列表
     */
    @RequiresPermissions("vehicle:application:list")
    @GetMapping("/pending-schedule")
    public AjaxResult getPendingSchedule()
    {
        List<VehicleApplication> list = vehicleApplicationService.selectPendingScheduleApplications();
        return success(list);
    }

    /**
     * 批量审批申请
     */
    @RequiresPermissions("vehicle:application:approve")
    @Log(title = "批量审批申请", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-approve")
    public AjaxResult batchApprove(@RequestBody VehicleApplication vehicleApplication)
    {
        String operName = SecurityUtils.getUsername();
        Long[] applicationIds = vehicleApplication.getApplicationId() != null ? 
            new Long[]{vehicleApplication.getApplicationId()} : new Long[0];
        
        int result = vehicleApplicationService.batchApproveApplications(
            applicationIds, 
            vehicleApplication.getApprovalStatus(), 
            vehicleApplication.getRemark(), 
            operName
        );
        
        return success("批量审批完成，成功处理 " + result + " 条申请");
    }
}
