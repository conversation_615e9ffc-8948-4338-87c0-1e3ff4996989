{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\maintenance.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\maintenance.js", "mtime": 1754141789966}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listMaintenance", "query", "request", "url", "method", "params", "getMaintenance", "maintenanceId", "addMaintenance", "data", "updateMaintenance", "delMaintenance", "getMaintenanceByVehicleId", "vehicleId", "getMaintenanceByStatus", "status", "getCostStatistics", "getFrequencyStatistics"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/maintenance.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询车辆维修记录列表\nexport function listMaintenance(query) {\n  return request({\n    url: '/vehicle/maintenance/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询车辆维修记录详细\nexport function getMaintenance(maintenanceId) {\n  return request({\n    url: '/vehicle/maintenance/' + maintenanceId,\n    method: 'get'\n  })\n}\n\n// 新增车辆维修记录\nexport function addMaintenance(data) {\n  return request({\n    url: '/vehicle/maintenance',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改车辆维修记录\nexport function updateMaintenance(data) {\n  return request({\n    url: '/vehicle/maintenance',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除车辆维修记录\nexport function delMaintenance(maintenanceId) {\n  return request({\n    url: '/vehicle/maintenance/' + maintenanceId,\n    method: 'delete'\n  })\n}\n\n// 根据车辆ID查询维修记录\nexport function getMaintenanceByVehicleId(vehicleId) {\n  return request({\n    url: '/vehicle/maintenance/vehicle/' + vehicleId,\n    method: 'get'\n  })\n}\n\n// 根据维修状态查询维修记录\nexport function getMaintenanceByStatus(status) {\n  return request({\n    url: '/vehicle/maintenance/status/' + status,\n    method: 'get'\n  })\n}\n\n// 获取维修费用统计\nexport function getCostStatistics() {\n  return request({\n    url: '/vehicle/maintenance/cost-statistics',\n    method: 'get'\n  })\n}\n\n// 获取维修频次统计\nexport function getFrequencyStatistics() {\n  return request({\n    url: '/vehicle/maintenance/frequency-statistics',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,aAAa;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,aAAa;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,yBAAyBA,CAACC,SAAS,EAAE;EACnD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGU,SAAS;IAChDT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGY,MAAM;IAC5CX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,sBAAsBA,CAAA,EAAG;EACvC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}