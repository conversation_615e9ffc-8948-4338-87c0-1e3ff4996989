package com.ruoyi.vehicle.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.service.IVehicleStatisticsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;

/**
 * 车辆台班统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/statistics")
public class VehicleStatisticsController extends BaseController
{
    @Autowired
    private IVehicleStatisticsService vehicleStatisticsService;

    /**
     * 获取车辆总体统计信息
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/overall")
    public AjaxResult getOverallStatistics()
    {
        Map<String, Object> statistics = vehicleStatisticsService.getVehicleOverallStatistics();
        return success(statistics);
    }

    /**
     * 按车辆类型统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/by-vehicle-type")
    public AjaxResult getStatisticsByVehicleType(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getStatisticsByVehicleType(startDate, endDate);
        return success(statistics);
    }

    /**
     * 按队伍统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/by-team")
    public AjaxResult getStatisticsByTeam(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getStatisticsByTeam(startDate, endDate);
        return success(statistics);
    }

    /**
     * 按天统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/by-day")
    public AjaxResult getStatisticsByDay(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getStatisticsByDay(startDate, endDate);
        return success(statistics);
    }

    /**
     * 按月统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/by-month")
    public AjaxResult getStatisticsByMonth(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getStatisticsByMonth(startDate, endDate);
        return success(statistics);
    }

    /**
     * 车辆利用率统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/utilization")
    public AjaxResult getUtilizationStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getVehicleUtilizationStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 台班工作量统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/workload")
    public AjaxResult getWorkloadStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String vehicleType,
            @RequestParam(required = false) Long teamId)
    {
        Map<String, Object> statistics = vehicleStatisticsService.getShiftWorkloadStatistics(startDate, endDate, vehicleType, teamId);
        return success(statistics);
    }

    /**
     * 违章记录统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/violation")
    public AjaxResult getViolationStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> statistics = vehicleStatisticsService.getViolationStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 维修记录统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/maintenance")
    public AjaxResult getMaintenanceStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> statistics = vehicleStatisticsService.getMaintenanceStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 订单状态统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/order-status")
    public AjaxResult getOrderStatusStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> statistics = vehicleStatisticsService.getOrderStatusStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 审批效率统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/approval-efficiency")
    public AjaxResult getApprovalEfficiencyStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> statistics = vehicleStatisticsService.getApprovalEfficiencyStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 车辆使用排行榜
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/vehicle-ranking")
    public AjaxResult getVehicleRanking(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(defaultValue = "10") Integer limit)
    {
        List<Map<String, Object>> ranking = vehicleStatisticsService.getVehicleRankingByUsage(startDate, endDate, limit);
        return success(ranking);
    }

    /**
     * 队伍用车排行榜
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/team-ranking")
    public AjaxResult getTeamRanking(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(defaultValue = "10") Integer limit)
    {
        List<Map<String, Object>> ranking = vehicleStatisticsService.getTeamRankingByUsage(startDate, endDate, limit);
        return success(ranking);
    }

    /**
     * 司机工作量统计
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/driver-workload")
    public AjaxResult getDriverWorkloadStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> statistics = vehicleStatisticsService.getDriverWorkloadStatistics(startDate, endDate);
        return success(statistics);
    }

    /**
     * 生成统计报表
     */
    @RequiresPermissions("vehicle:statistics:report")
    @PostMapping("/report")
    public AjaxResult generateReport(@RequestBody Map<String, Object> params)
    {
        String reportType = (String) params.get("reportType");
        Date startDate = (Date) params.get("startDate");
        Date endDate = (Date) params.get("endDate");
        
        Map<String, Object> report = vehicleStatisticsService.generateStatisticsReport(reportType, startDate, endDate, params);
        return success(report);
    }

    /**
     * 导出统计数据
     */
    @RequiresPermissions("vehicle:statistics:export")
    @Log(title = "导出统计数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void exportStatistics(HttpServletResponse response, @RequestBody Map<String, Object> params)
    {
        String reportType = (String) params.get("reportType");
        Date startDate = (Date) params.get("startDate");
        Date endDate = (Date) params.get("endDate");
        
        List<Map<String, Object>> data = vehicleStatisticsService.exportStatisticsData(reportType, startDate, endDate, params);
        
        // TODO: 实现Excel导出逻辑
        // ExcelUtil<Map<String, Object>> util = new ExcelUtil<>(Map.class);
        // util.exportExcel(response, data, "统计数据");
    }

    /**
     * 获取实时统计数据（Dashboard用）
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/realtime")
    public AjaxResult getRealTimeStatistics()
    {
        Map<String, Object> statistics = vehicleStatisticsService.getRealTimeStatistics();
        return success(statistics);
    }

    /**
     * 获取预警统计数据
     */
    @RequiresPermissions("vehicle:statistics:list")
    @GetMapping("/warning")
    public AjaxResult getWarningStatistics()
    {
        Map<String, Object> statistics = vehicleStatisticsService.getWarningStatistics();
        return success(statistics);
    }
}
