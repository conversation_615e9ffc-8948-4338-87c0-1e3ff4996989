{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\demand.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\demand.js", "mtime": 1754141804387}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDemand", "query", "request", "url", "method", "params", "<PERSON><PERSON><PERSON><PERSON>", "planId", "<PERSON><PERSON><PERSON><PERSON>", "data", "updateDemand", "<PERSON><PERSON><PERSON><PERSON>", "submitDemand", "<PERSON><PERSON><PERSON><PERSON>", "getDemandByStatus", "approvalStatus", "getDemandByTeamId", "teamId", "getMyDemands", "getPendingApprovalDemands", "batchApproveDemands"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/demand.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询车辆需求计划列表\nexport function listDemand(query) {\n  return request({\n    url: '/vehicle/demand/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询车辆需求计划详细\nexport function getDemand(planId) {\n  return request({\n    url: '/vehicle/demand/' + planId,\n    method: 'get'\n  })\n}\n\n// 新增车辆需求计划\nexport function addDemand(data) {\n  return request({\n    url: '/vehicle/demand',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改车辆需求计划\nexport function updateDemand(data) {\n  return request({\n    url: '/vehicle/demand',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除车辆需求计划\nexport function delDemand(planId) {\n  return request({\n    url: '/vehicle/demand/' + planId,\n    method: 'delete'\n  })\n}\n\n// 提交需求计划\nexport function submitDemand(data) {\n  return request({\n    url: '/vehicle/demand/submit',\n    method: 'post',\n    data: data\n  })\n}\n\n// 审批需求计划\nexport function approveDemand(planId, data) {\n  return request({\n    url: '/vehicle/demand/approve/' + planId,\n    method: 'put',\n    data: data\n  })\n}\n\n// 根据审批状态查询计划列表\nexport function getDemandByStatus(approvalStatus) {\n  return request({\n    url: '/vehicle/demand/status/' + approvalStatus,\n    method: 'get'\n  })\n}\n\n// 根据队伍ID查询计划列表\nexport function getDemandByTeamId(teamId) {\n  return request({\n    url: '/vehicle/demand/team/' + teamId,\n    method: 'get'\n  })\n}\n\n// 查询我的需求计划\nexport function getMyDemands() {\n  return request({\n    url: '/vehicle/demand/my-plans',\n    method: 'get'\n  })\n}\n\n// 查询待审批的计划列表\nexport function getPendingApprovalDemands() {\n  return request({\n    url: '/vehicle/demand/pending-approval',\n    method: 'get'\n  })\n}\n\n// 批量审批需求计划\nexport function batchApproveDemands(data) {\n  return request({\n    url: '/vehicle/demand/batch-approve',\n    method: 'put',\n    data: data\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,MAAM,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,MAAM;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,MAAM,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,MAAM;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACH,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACN,MAAM,EAAEE,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,MAAM;IACxCH,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACC,cAAc,EAAE;EAChD,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGY,cAAc;IAC/CX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,iBAAiBA,CAACC,MAAM,EAAE;EACxC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGc,MAAM;IACrCb,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,IAAAjB,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,mBAAmBA,CAACX,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}