<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车辆ID" prop="vehicleId">
        <el-select v-model="queryParams.vehicleId" placeholder="请选择车辆" clearable>
          <el-option
            v-for="vehicle in vehicleOptions"
            :key="vehicle.vehicleId"
            :label="vehicle.vehicleModel"
            :value="vehicle.vehicleId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维修类型" prop="maintenanceType">
        <el-select v-model="queryParams.maintenanceType" placeholder="请选择维修类型" clearable>
          <el-option label="定期保养" value="定期保养"></el-option>
          <el-option label="故障维修" value="故障维修"></el-option>
          <el-option label="大修" value="大修"></el-option>
          <el-option label="其他" value="其他"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维修状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择维修状态" clearable>
          <el-option label="待维修" value="pending"></el-option>
          <el-option label="维修中" value="repairing"></el-option>
          <el-option label="已完成" value="completed"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="维修日期">
        <el-date-picker
          v-model="daterangeMaintenanceDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vehicle:maintenance:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vehicle:maintenance:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vehicle:maintenance:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:maintenance:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-data"
          size="mini"
          @click="handleStatistics"
          v-hasPermi="['vehicle:maintenance:list']"
        >费用统计</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="maintenanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="维修ID" align="center" prop="maintenanceId" />
      <el-table-column label="车辆信息" align="center" prop="vehicleInfo" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
          <div style="color: #909399; font-size: 12px;">
            {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="维修类型" align="center" prop="maintenanceType" />
      <el-table-column label="维修日期" align="center" prop="maintenanceDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.maintenanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="故障描述" align="center" prop="faultDescription" :show-overflow-tooltip="true" />
      <el-table-column label="维修费用" align="center" prop="maintenanceCost">
        <template slot-scope="scope">
          <span style="color: #E6A23C;">¥{{ scope.row.maintenanceCost }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维修状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.maintenance_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['vehicle:maintenance:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:maintenance:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:maintenance:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改维修记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select v-model="form.vehicleId" placeholder="请选择车辆">
                <el-option
                  v-for="vehicle in vehicleOptions"
                  :key="vehicle.vehicleId"
                  :label="vehicle.vehicleModel"
                  :value="vehicle.vehicleId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修类型" prop="maintenanceType">
              <el-select v-model="form.maintenanceType" placeholder="请选择维修类型">
                <el-option label="定期保养" value="定期保养"></el-option>
                <el-option label="故障维修" value="故障维修"></el-option>
                <el-option label="大修" value="大修"></el-option>
                <el-option label="其他" value="其他"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="维修日期" prop="maintenanceDate">
              <el-date-picker clearable
                v-model="form.maintenanceDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择维修日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修费用" prop="maintenanceCost">
              <el-input v-model="form.maintenanceCost" placeholder="请输入维修费用" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="故障描述" prop="faultDescription">
          <el-input v-model="form.faultDescription" type="textarea" placeholder="请输入故障描述" />
        </el-form-item>
        <el-form-item label="维修内容" prop="maintenanceContent">
          <el-input v-model="form.maintenanceContent" type="textarea" placeholder="请输入维修内容" />
        </el-form-item>
        <el-form-item label="维修人员" prop="maintenancePerson">
          <el-input v-model="form.maintenancePerson" placeholder="请输入维修人员" />
        </el-form-item>
        <el-form-item label="维修状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="pending">待维修</el-radio>
            <el-radio label="repairing">维修中</el-radio>
            <el-radio label="completed">已完成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 维修记录详情对话框 -->
    <el-dialog title="维修记录详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="车辆信息">
          {{ detailForm.vehicleInfo ? detailForm.vehicleInfo.vehicleModel : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="维修类型">{{ detailForm.maintenanceType }}</el-descriptions-item>
        <el-descriptions-item label="维修日期">{{ detailForm.maintenanceDate }}</el-descriptions-item>
        <el-descriptions-item label="维修费用">¥{{ detailForm.maintenanceCost }}</el-descriptions-item>
        <el-descriptions-item label="维修人员">{{ detailForm.maintenancePerson }}</el-descriptions-item>
        <el-descriptions-item label="维修状态">
          <dict-tag :options="dict.type.maintenance_status" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="故障描述" :span="2">{{ detailForm.faultDescription }}</el-descriptions-item>
        <el-descriptions-item label="维修内容" :span="2">{{ detailForm.maintenanceContent }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listMaintenance, getMaintenance, delMaintenance, addMaintenance, updateMaintenance } from "@/api/vehicle/maintenance";
import { listInfo } from "@/api/vehicle/info";

export default {
  name: "Maintenance",
  dicts: ['maintenance_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 维修记录表格数据
      maintenanceList: [],
      // 车辆选项
      vehicleOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 维修日期时间范围
      daterangeMaintenanceDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleId: null,
        maintenanceType: null,
        maintenanceDate: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单参数
      detailForm: {},
      // 表单校验
      rules: {
        vehicleId: [
          { required: true, message: "车辆不能为空", trigger: "change" }
        ],
        maintenanceType: [
          { required: true, message: "维修类型不能为空", trigger: "change" }
        ],
        maintenanceDate: [
          { required: true, message: "维修日期不能为空", trigger: "blur" }
        ],
        faultDescription: [
          { required: true, message: "故障描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getVehicleOptions();
  },
  methods: {
    /** 查询维修记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeMaintenanceDate && '' != this.daterangeMaintenanceDate) {
        this.queryParams.params["beginMaintenanceDate"] = this.daterangeMaintenanceDate[0];
        this.queryParams.params["endMaintenanceDate"] = this.daterangeMaintenanceDate[1];
      }
      listMaintenance(this.queryParams).then(response => {
        this.maintenanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取车辆选项 */
    getVehicleOptions() {
      listInfo().then(response => {
        this.vehicleOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        maintenanceId: null,
        vehicleId: null,
        maintenanceType: null,
        maintenanceDate: null,
        faultDescription: null,
        maintenanceContent: null,
        maintenanceCost: null,
        maintenancePerson: null,
        status: "pending",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeMaintenanceDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.maintenanceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加维修记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const maintenanceId = row.maintenanceId || this.ids
      getMaintenance(maintenanceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改维修记录";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getMaintenance(row.maintenanceId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.maintenanceId != null) {
            updateMaintenance(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaintenance(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const maintenanceIds = row.maintenanceId || this.ids;
      this.$modal.confirm('是否确认删除维修记录编号为"' + maintenanceIds + '"的数据项？').then(function() {
        return delMaintenance(maintenanceIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 费用统计 */
    handleStatistics() {
      this.$modal.msgInfo("费用统计功能开发中...");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/maintenance/export', {
        ...this.queryParams
      }, `maintenance_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
