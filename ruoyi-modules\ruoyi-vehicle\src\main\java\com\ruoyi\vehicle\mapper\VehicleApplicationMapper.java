package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleApplication;

/**
 * 机械用车申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleApplicationMapper 
{
    /**
     * 查询机械用车申请
     * 
     * @param applicationId 机械用车申请主键
     * @return 机械用车申请
     */
    public VehicleApplication selectVehicleApplicationByApplicationId(Long applicationId);

    /**
     * 查询机械用车申请列表
     * 
     * @param vehicleApplication 机械用车申请
     * @return 机械用车申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationList(VehicleApplication vehicleApplication);

    /**
     * 新增机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    public int insertVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 修改机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    public int updateVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 删除机械用车申请
     * 
     * @param applicationId 机械用车申请主键
     * @return 结果
     */
    public int deleteVehicleApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除机械用车申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 根据审批状态查询申请列表
     * 
     * @param approvalStatus 审批状态
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByStatus(String approvalStatus);

    /**
     * 根据队伍ID查询申请列表
     * 
     * @param teamId 队伍ID
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByTeamId(Long teamId);

    /**
     * 根据申请人查询申请列表
     * 
     * @param applicant 申请人
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByApplicant(String applicant);

    /**
     * 查询待调度的申请列表
     * 
     * @return 申请集合
     */
    public List<VehicleApplication> selectPendingScheduleApplications();

    /**
     * 查询已分配车辆的申请列表
     * 
     * @param vehicleId 车辆ID
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByVehicleId(Long vehicleId);
}
