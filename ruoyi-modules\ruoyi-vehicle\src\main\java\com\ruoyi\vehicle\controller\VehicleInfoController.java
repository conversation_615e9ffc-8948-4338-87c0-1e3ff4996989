package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleInfo;
import com.ruoyi.vehicle.service.IVehicleInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 机械车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/info")
public class VehicleInfoController extends BaseController
{
    @Autowired
    private IVehicleInfoService vehicleInfoService;

    /**
     * 查询机械车辆信息列表
     */
    @RequiresPermissions("vehicle:info:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleInfo vehicleInfo)
    {
        startPage();
        List<VehicleInfo> list = vehicleInfoService.selectVehicleInfoList(vehicleInfo);
        return getDataTable(list);
    }

    /**
     * 导出机械车辆信息列表
     */
    @RequiresPermissions("vehicle:info:export")
    @Log(title = "机械车辆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleInfo vehicleInfo)
    {
        List<VehicleInfo> list = vehicleInfoService.selectVehicleInfoList(vehicleInfo);
        ExcelUtil<VehicleInfo> util = new ExcelUtil<VehicleInfo>(VehicleInfo.class);
        util.exportExcel(response, list, "机械车辆信息数据");
    }

    /**
     * 获取机械车辆信息详细信息
     */
    @RequiresPermissions("vehicle:info:query")
    @GetMapping(value = "/{vehicleId}")
    public AjaxResult getInfo(@PathVariable("vehicleId") Long vehicleId)
    {
        return success(vehicleInfoService.selectVehicleInfoByVehicleId(vehicleId));
    }

    /**
     * 新增机械车辆信息
     */
    @RequiresPermissions("vehicle:info:add")
    @Log(title = "机械车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleInfo vehicleInfo)
    {
        vehicleInfo.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleInfoService.insertVehicleInfo(vehicleInfo));
    }

    /**
     * 修改机械车辆信息
     */
    @RequiresPermissions("vehicle:info:edit")
    @Log(title = "机械车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleInfo vehicleInfo)
    {
        vehicleInfo.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleInfoService.updateVehicleInfo(vehicleInfo));
    }

    /**
     * 删除机械车辆信息
     */
    @RequiresPermissions("vehicle:info:remove")
    @Log(title = "机械车辆信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{vehicleIds}")
    public AjaxResult remove(@PathVariable Long[] vehicleIds)
    {
        return toAjax(vehicleInfoService.deleteVehicleInfoByVehicleIds(vehicleIds));
    }

    /**
     * 获取可用车辆列表
     */
    @RequiresPermissions("vehicle:info:list")
    @GetMapping("/available")
    public AjaxResult getAvailableVehicles()
    {
        List<VehicleInfo> list = vehicleInfoService.selectAvailableVehicleList();
        return success(list);
    }

    /**
     * 根据车辆类型获取车辆列表
     */
    @RequiresPermissions("vehicle:info:list")
    @GetMapping("/type/{vehicleType}")
    public AjaxResult getVehiclesByType(@PathVariable String vehicleType)
    {
        List<VehicleInfo> list = vehicleInfoService.selectVehicleInfoByType(vehicleType);
        return success(list);
    }

    /**
     * 更新车辆状态
     */
    @RequiresPermissions("vehicle:info:edit")
    @Log(title = "更新车辆状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{vehicleId}/{status}")
    public AjaxResult updateStatus(@PathVariable Long vehicleId, @PathVariable String status)
    {
        return toAjax(vehicleInfoService.updateVehicleStatus(vehicleId, status));
    }

    /**
     * 导入车辆信息数据
     */
    @RequiresPermissions("vehicle:info:import")
    @Log(title = "车辆信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<VehicleInfo> util = new ExcelUtil<VehicleInfo>(VehicleInfo.class);
        List<VehicleInfo> vehicleList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = vehicleInfoService.importVehicle(vehicleList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载车辆信息导入模板
     */
    @RequiresPermissions("vehicle:info:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<VehicleInfo> util = new ExcelUtil<VehicleInfo>(VehicleInfo.class);
        util.importTemplateExcel(response, "车辆信息数据");
    }
}
