import request from '@/utils/request'

// 查询车辆违章记录列表
export function listViolation(query) {
  return request({
    url: '/vehicle/violation/list',
    method: 'get',
    params: query
  })
}

// 查询车辆违章记录详细
export function getViolation(violationId) {
  return request({
    url: '/vehicle/violation/' + violationId,
    method: 'get'
  })
}

// 新增车辆违章记录
export function addViolation(data) {
  return request({
    url: '/vehicle/violation',
    method: 'post',
    data: data
  })
}

// 修改车辆违章记录
export function updateViolation(data) {
  return request({
    url: '/vehicle/violation',
    method: 'put',
    data: data
  })
}

// 删除车辆违章记录
export function delViolation(violationId) {
  return request({
    url: '/vehicle/violation/' + violationId,
    method: 'delete'
  })
}

// 根据车辆ID查询违章记录
export function getViolationByVehicleId(vehicleId) {
  return request({
    url: '/vehicle/violation/vehicle/' + vehicleId,
    method: 'get'
  })
}

// 根据处理状态查询违章记录
export function getViolationByStatus(status) {
  return request({
    url: '/vehicle/violation/status/' + status,
    method: 'get'
  })
}

// 处理违章记录
export function processViolation(violationId) {
  return request({
    url: '/vehicle/violation/process/' + violationId,
    method: 'put'
  })
}

// 批量处理违章记录
export function batchProcessViolation(violationIds) {
  return request({
    url: '/vehicle/violation/batch-process',
    method: 'put',
    data: violationIds
  })
}
