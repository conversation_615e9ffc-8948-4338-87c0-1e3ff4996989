package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.VehicleInfoMapper;
import com.ruoyi.vehicle.domain.VehicleInfo;
import com.ruoyi.vehicle.service.IVehicleInfoService;

/**
 * 机械车辆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleInfoServiceImpl implements IVehicleInfoService 
{
    @Autowired
    private VehicleInfoMapper vehicleInfoMapper;

    /**
     * 查询机械车辆信息
     * 
     * @param vehicleId 机械车辆信息主键
     * @return 机械车辆信息
     */
    @Override
    public VehicleInfo selectVehicleInfoByVehicleId(Long vehicleId)
    {
        return vehicleInfoMapper.selectVehicleInfoByVehicleId(vehicleId);
    }

    /**
     * 查询机械车辆信息列表
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 机械车辆信息
     */
    @Override
    public List<VehicleInfo> selectVehicleInfoList(VehicleInfo vehicleInfo)
    {
        return vehicleInfoMapper.selectVehicleInfoList(vehicleInfo);
    }

    /**
     * 新增机械车辆信息
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 结果
     */
    @Override
    public int insertVehicleInfo(VehicleInfo vehicleInfo)
    {
        vehicleInfo.setCreateTime(DateUtils.getNowDate());
        return vehicleInfoMapper.insertVehicleInfo(vehicleInfo);
    }

    /**
     * 修改机械车辆信息
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 结果
     */
    @Override
    public int updateVehicleInfo(VehicleInfo vehicleInfo)
    {
        vehicleInfo.setUpdateTime(DateUtils.getNowDate());
        return vehicleInfoMapper.updateVehicleInfo(vehicleInfo);
    }

    /**
     * 批量删除机械车辆信息
     * 
     * @param vehicleIds 需要删除的机械车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteVehicleInfoByVehicleIds(Long[] vehicleIds)
    {
        return vehicleInfoMapper.deleteVehicleInfoByVehicleIds(vehicleIds);
    }

    /**
     * 删除机械车辆信息信息
     * 
     * @param vehicleId 机械车辆信息主键
     * @return 结果
     */
    @Override
    public int deleteVehicleInfoByVehicleId(Long vehicleId)
    {
        return vehicleInfoMapper.deleteVehicleInfoByVehicleId(vehicleId);
    }

    /**
     * 根据车辆状态查询车辆列表
     * 
     * @param vehicleStatus 车辆状态
     * @return 车辆信息集合
     */
    @Override
    public List<VehicleInfo> selectVehicleInfoByStatus(String vehicleStatus)
    {
        return vehicleInfoMapper.selectVehicleInfoByStatus(vehicleStatus);
    }

    /**
     * 根据车辆类型查询车辆列表
     * 
     * @param vehicleType 车辆类型
     * @return 车辆信息集合
     */
    @Override
    public List<VehicleInfo> selectVehicleInfoByType(String vehicleType)
    {
        return vehicleInfoMapper.selectVehicleInfoByType(vehicleType);
    }

    /**
     * 查询可用车辆列表
     * 
     * @return 车辆信息集合
     */
    @Override
    public List<VehicleInfo> selectAvailableVehicleList()
    {
        return vehicleInfoMapper.selectAvailableVehicleList();
    }

    /**
     * 更新车辆状态
     * 
     * @param vehicleId 车辆ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public int updateVehicleStatus(Long vehicleId, String status)
    {
        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.setVehicleId(vehicleId);
        vehicleInfo.setVehicleStatus(status);
        vehicleInfo.setUpdateTime(DateUtils.getNowDate());
        return vehicleInfoMapper.updateVehicleInfo(vehicleInfo);
    }

    /**
     * 导入车辆信息数据
     * 
     * @param vehicleList 车辆信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importVehicle(List<VehicleInfo> vehicleList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(vehicleList) || vehicleList.size() == 0)
        {
            throw new ServiceException("导入车辆数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (VehicleInfo vehicleInfo : vehicleList)
        {
            try
            {
                // 验证是否存在这个车辆
                VehicleInfo v = new VehicleInfo();
                v.setLicensePlate(vehicleInfo.getLicensePlate());
                List<VehicleInfo> vehicles = vehicleInfoMapper.selectVehicleInfoList(v);
                if (vehicles.size() == 0)
                {
                    vehicleInfo.setCreateBy(operName);
                    this.insertVehicleInfo(vehicleInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、车辆 " + vehicleInfo.getLicensePlate() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    vehicleInfo.setVehicleId(vehicles.get(0).getVehicleId());
                    vehicleInfo.setUpdateBy(operName);
                    this.updateVehicleInfo(vehicleInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、车辆 " + vehicleInfo.getLicensePlate() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、车辆 " + vehicleInfo.getLicensePlate() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、车辆 " + vehicleInfo.getLicensePlate() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
