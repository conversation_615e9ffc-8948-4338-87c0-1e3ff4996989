import request from '@/utils/request'

// 查询队伍信息列表
export function listTeam(query) {
  return request({
    url: '/vehicle/team/list',
    method: 'get',
    params: query
  })
}

// 查询队伍信息详细
export function getTeam(teamId) {
  return request({
    url: '/vehicle/team/' + teamId,
    method: 'get'
  })
}

// 新增队伍信息
export function addTeam(data) {
  return request({
    url: '/vehicle/team',
    method: 'post',
    data: data
  })
}

// 修改队伍信息
export function updateTeam(data) {
  return request({
    url: '/vehicle/team',
    method: 'put',
    data: data
  })
}

// 删除队伍信息
export function delTeam(teamId) {
  return request({
    url: '/vehicle/team/' + teamId,
    method: 'delete'
  })
}

// 根据队伍类型查询队伍列表
export function getTeamByType(teamType) {
  return request({
    url: '/vehicle/team/type/' + teamType,
    method: 'get'
  })
}

// 根据状态查询队伍列表
export function getTeamByStatus(status) {
  return request({
    url: '/vehicle/team/status/' + status,
    method: 'get'
  })
}

// 查询活跃队伍列表
export function getActiveTeams() {
  return request({
    url: '/vehicle/team/active',
    method: 'get'
  })
}

// 更新队伍状态
export function updateTeamStatus(teamId, status) {
  return request({
    url: '/vehicle/team/status/' + teamId + '/' + status,
    method: 'put'
  })
}

// 获取队伍下拉选项
export function getTeamOptions() {
  return request({
    url: '/vehicle/team/options',
    method: 'get'
  })
}
