package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 车辆统计对象 vehicle_statistics
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 统计ID */
    private Long statisticsId;

    /** 统计类型 */
    @Excel(name = "统计类型")
    private String statisticsType;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statisticsDate;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 队伍ID */
    @Excel(name = "队伍ID")
    private Long teamId;

    /** 使用次数 */
    @Excel(name = "使用次数")
    private Long usageCount;

    /** 使用小时数 */
    @Excel(name = "使用小时数")
    private BigDecimal usageHours;

    /** 总费用 */
    @Excel(name = "总费用")
    private BigDecimal totalCost;

    /** 平均费用 */
    @Excel(name = "平均费用")
    private BigDecimal averageCost;

    public void setStatisticsId(Long statisticsId) 
    {
        this.statisticsId = statisticsId;
    }

    public Long getStatisticsId() 
    {
        return statisticsId;
    }
    public void setStatisticsType(String statisticsType) 
    {
        this.statisticsType = statisticsType;
    }

    public String getStatisticsType() 
    {
        return statisticsType;
    }
    public void setStatisticsDate(Date statisticsDate) 
    {
        this.statisticsDate = statisticsDate;
    }

    public Date getStatisticsDate() 
    {
        return statisticsDate;
    }
    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    public String getVehicleType() 
    {
        return vehicleType;
    }
    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    public Long getTeamId() 
    {
        return teamId;
    }
    public void setUsageCount(Long usageCount) 
    {
        this.usageCount = usageCount;
    }

    public Long getUsageCount() 
    {
        return usageCount;
    }
    public void setUsageHours(BigDecimal usageHours) 
    {
        this.usageHours = usageHours;
    }

    public BigDecimal getUsageHours() 
    {
        return usageHours;
    }
    public void setTotalCost(BigDecimal totalCost) 
    {
        this.totalCost = totalCost;
    }

    public BigDecimal getTotalCost() 
    {
        return totalCost;
    }
    public void setAverageCost(BigDecimal averageCost) 
    {
        this.averageCost = averageCost;
    }

    public BigDecimal getAverageCost() 
    {
        return averageCost;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("statisticsId", getStatisticsId())
            .append("statisticsType", getStatisticsType())
            .append("statisticsDate", getStatisticsDate())
            .append("vehicleType", getVehicleType())
            .append("teamId", getTeamId())
            .append("usageCount", getUsageCount())
            .append("usageHours", getUsageHours())
            .append("totalCost", getTotalCost())
            .append("averageCost", getAverageCost())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
