{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\team.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\team.js", "mtime": 1754141841672}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTeam", "query", "request", "url", "method", "params", "getTeam", "teamId", "addTeam", "data", "updateTeam", "delTeam", "getTeamByType", "teamType", "getTeamByStatus", "status", "getActiveTeams", "updateTeamStatus", "getTeamOptions"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/team.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询队伍信息列表\nexport function listTeam(query) {\n  return request({\n    url: '/vehicle/team/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询队伍信息详细\nexport function getTeam(teamId) {\n  return request({\n    url: '/vehicle/team/' + teamId,\n    method: 'get'\n  })\n}\n\n// 新增队伍信息\nexport function addTeam(data) {\n  return request({\n    url: '/vehicle/team',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改队伍信息\nexport function updateTeam(data) {\n  return request({\n    url: '/vehicle/team',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除队伍信息\nexport function delTeam(teamId) {\n  return request({\n    url: '/vehicle/team/' + teamId,\n    method: 'delete'\n  })\n}\n\n// 根据队伍类型查询队伍列表\nexport function getTeamByType(teamType) {\n  return request({\n    url: '/vehicle/team/type/' + teamType,\n    method: 'get'\n  })\n}\n\n// 根据状态查询队伍列表\nexport function getTeamByStatus(status) {\n  return request({\n    url: '/vehicle/team/status/' + status,\n    method: 'get'\n  })\n}\n\n// 查询活跃队伍列表\nexport function getActiveTeams() {\n  return request({\n    url: '/vehicle/team/active',\n    method: 'get'\n  })\n}\n\n// 更新队伍状态\nexport function updateTeamStatus(teamId, status) {\n  return request({\n    url: '/vehicle/team/status/' + teamId + '/' + status,\n    method: 'put'\n  })\n}\n\n// 获取队伍下拉选项\nexport function getTeamOptions() {\n  return request({\n    url: '/vehicle/team/options',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,MAAM;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,MAAM;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,aAAaA,CAACC,QAAQ,EAAE;EACtC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGU,QAAQ;IACrCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGY,MAAM;IACrCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,gBAAgBA,CAACV,MAAM,EAAEQ,MAAM,EAAE;EAC/C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,MAAM,GAAG,GAAG,GAAGQ,MAAM;IACpDX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}