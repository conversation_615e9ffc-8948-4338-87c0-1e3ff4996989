package com.ruoyi.vehicle.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.vehicle.domain.VehicleStatistics;
import org.apache.ibatis.annotations.Param;

/**
 * 车辆统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleStatisticsMapper 
{
    /**
     * 查询车辆统计
     * 
     * @param statisticsId 车辆统计主键
     * @return 车辆统计
     */
    public VehicleStatistics selectVehicleStatisticsByStatisticsId(Long statisticsId);

    /**
     * 查询车辆统计列表
     * 
     * @param vehicleStatistics 车辆统计
     * @return 车辆统计集合
     */
    public List<VehicleStatistics> selectVehicleStatisticsList(VehicleStatistics vehicleStatistics);

    /**
     * 新增车辆统计
     * 
     * @param vehicleStatistics 车辆统计
     * @return 结果
     */
    public int insertVehicleStatistics(VehicleStatistics vehicleStatistics);

    /**
     * 修改车辆统计
     * 
     * @param vehicleStatistics 车辆统计
     * @return 结果
     */
    public int updateVehicleStatistics(VehicleStatistics vehicleStatistics);

    /**
     * 删除车辆统计
     * 
     * @param statisticsId 车辆统计主键
     * @return 结果
     */
    public int deleteVehicleStatisticsByStatisticsId(Long statisticsId);

    /**
     * 批量删除车辆统计
     * 
     * @param statisticsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleStatisticsByStatisticsIds(Long[] statisticsIds);

    /**
     * 车辆使用情况统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param vehicleType 车辆类型
     * @return 统计结果
     */
    List<Map<String, Object>> getVehicleUsageStatistics(@Param("startDate") String startDate, 
                                                        @Param("endDate") String endDate, 
                                                        @Param("vehicleType") String vehicleType);

    /**
     * 队伍维度统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param teamId 队伍ID
     * @return 统计结果
     */
    List<Map<String, Object>> getTeamUsageStatistics(@Param("startDate") String startDate, 
                                                     @Param("endDate") String endDate, 
                                                     @Param("teamId") Long teamId);

    /**
     * 出租单位统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    List<Map<String, Object>> getRentalUnitStatistics(@Param("startDate") String startDate, 
                                                      @Param("endDate") String endDate);

    /**
     * 作业区域统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    List<Map<String, Object>> getWorkAreaStatistics(@Param("startDate") String startDate, 
                                                    @Param("endDate") String endDate);

    /**
     * 费用单位分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param costBearer 费用承担方
     * @return 统计结果
     */
    List<Map<String, Object>> getCostUnitAnalysis(@Param("startDate") String startDate, 
                                                 @Param("endDate") String endDate, 
                                                 @Param("costBearer") String costBearer);

    /**
     * 月度趋势统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    List<Map<String, Object>> getMonthlyTrendStatistics(@Param("startDate") String startDate, 
                                                        @Param("endDate") String endDate);

    /**
     * 车辆效率分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param vehicleType 车辆类型
     * @return 统计结果
     */
    List<Map<String, Object>> getVehicleEfficiencyAnalysis(@Param("startDate") String startDate, 
                                                           @Param("endDate") String endDate, 
                                                           @Param("vehicleType") String vehicleType);
}
