import request from '@/utils/request'

// 查询车辆维修记录列表
export function listMaintenance(query) {
  return request({
    url: '/vehicle/maintenance/list',
    method: 'get',
    params: query
  })
}

// 查询车辆维修记录详细
export function getMaintenance(maintenanceId) {
  return request({
    url: '/vehicle/maintenance/' + maintenanceId,
    method: 'get'
  })
}

// 新增车辆维修记录
export function addMaintenance(data) {
  return request({
    url: '/vehicle/maintenance',
    method: 'post',
    data: data
  })
}

// 修改车辆维修记录
export function updateMaintenance(data) {
  return request({
    url: '/vehicle/maintenance',
    method: 'put',
    data: data
  })
}

// 删除车辆维修记录
export function delMaintenance(maintenanceId) {
  return request({
    url: '/vehicle/maintenance/' + maintenanceId,
    method: 'delete'
  })
}

// 根据车辆ID查询维修记录
export function getMaintenanceByVehicleId(vehicleId) {
  return request({
    url: '/vehicle/maintenance/vehicle/' + vehicleId,
    method: 'get'
  })
}

// 根据维修状态查询维修记录
export function getMaintenanceByStatus(status) {
  return request({
    url: '/vehicle/maintenance/status/' + status,
    method: 'get'
  })
}

// 获取维修费用统计
export function getCostStatistics() {
  return request({
    url: '/vehicle/maintenance/cost-statistics',
    method: 'get'
  })
}

// 获取维修频次统计
export function getFrequencyStatistics() {
  return request({
    url: '/vehicle/maintenance/frequency-statistics',
    method: 'get'
  })
}
