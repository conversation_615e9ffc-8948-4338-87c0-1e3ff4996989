package com.ruoyi.vehicle.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.VehicleShiftApprovalMapper;
import com.ruoyi.vehicle.domain.VehicleShiftApproval;
import com.ruoyi.vehicle.service.IVehicleShiftApprovalService;

/**
 * 台班审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleShiftApprovalServiceImpl implements IVehicleShiftApprovalService 
{
    @Autowired
    private VehicleShiftApprovalMapper vehicleShiftApprovalMapper;

    /**
     * 查询台班审批
     * 
     * @param approvalId 台班审批主键
     * @return 台班审批
     */
    @Override
    public VehicleShiftApproval selectVehicleShiftApprovalByApprovalId(Long approvalId)
    {
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalByApprovalId(approvalId);
    }

    /**
     * 查询台班审批列表
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 台班审批
     */
    @Override
    public List<VehicleShiftApproval> selectVehicleShiftApprovalList(VehicleShiftApproval vehicleShiftApproval)
    {
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalList(vehicleShiftApproval);
    }

    /**
     * 新增台班审批
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 结果
     */
    @Override
    public int insertVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval)
    {
        vehicleShiftApproval.setCreateTime(DateUtils.getNowDate());
        return vehicleShiftApprovalMapper.insertVehicleShiftApproval(vehicleShiftApproval);
    }

    /**
     * 修改台班审批
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 结果
     */
    @Override
    public int updateVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval)
    {
        vehicleShiftApproval.setUpdateTime(DateUtils.getNowDate());
        return vehicleShiftApprovalMapper.updateVehicleShiftApproval(vehicleShiftApproval);
    }

    /**
     * 批量删除台班审批
     * 
     * @param approvalIds 需要删除的台班审批主键
     * @return 结果
     */
    @Override
    public int deleteVehicleShiftApprovalByApprovalIds(Long[] approvalIds)
    {
        return vehicleShiftApprovalMapper.deleteVehicleShiftApprovalByApprovalIds(approvalIds);
    }

    /**
     * 删除台班审批信息
     * 
     * @param approvalId 台班审批主键
     * @return 结果
     */
    @Override
    public int deleteVehicleShiftApprovalByApprovalId(Long approvalId)
    {
        return vehicleShiftApprovalMapper.deleteVehicleShiftApprovalByApprovalId(approvalId);
    }

    /**
     * 创建审批记录
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param approvalLevel 审批级别
     * @param approver 审批人
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int createApprovalRecord(String businessType, Long businessId, Integer approvalLevel, String approver, String operName)
    {
        VehicleShiftApproval approval = new VehicleShiftApproval();
        approval.setBusinessType(businessType);
        approval.setBusinessId(businessId);
        approval.setApprovalLevel(approvalLevel);
        approval.setApprovalStatus("pending");
        approval.setApprover(approver);
        approval.setCreateBy(operName);
        approval.setCreateTime(DateUtils.getNowDate());
        
        return vehicleShiftApprovalMapper.insertVehicleShiftApproval(approval);
    }

    /**
     * 审批处理
     * 
     * @param approvalId 审批ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int processApproval(Long approvalId, String approvalStatus, String approvalComments, String operName)
    {
        VehicleShiftApproval approval = new VehicleShiftApproval();
        approval.setApprovalId(approvalId);
        approval.setApprovalStatus(approvalStatus);
        approval.setApprovalComments(approvalComments);
        approval.setApprovalTime(DateUtils.getNowDate());
        approval.setUpdateBy(operName);
        approval.setUpdateTime(DateUtils.getNowDate());
        
        return vehicleShiftApprovalMapper.updateVehicleShiftApproval(approval);
    }

    /**
     * 批量审批处理
     * 
     * @param approvalIds 审批ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int batchProcessApproval(Long[] approvalIds, String approvalStatus, String approvalComments, String operName)
    {
        int result = 0;
        for (Long approvalId : approvalIds) {
            result += processApproval(approvalId, approvalStatus, approvalComments, operName);
        }
        return result;
    }

    /**
     * 根据审批人查询待审批列表
     * 
     * @param approver 审批人
     * @return 审批集合
     */
    @Override
    public List<VehicleShiftApproval> selectPendingApprovalsByApprover(String approver)
    {
        VehicleShiftApproval query = new VehicleShiftApproval();
        query.setApprover(approver);
        query.setApprovalStatus("pending");
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalList(query);
    }

    /**
     * 根据业务ID和类型查询审批记录
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 审批集合
     */
    @Override
    public List<VehicleShiftApproval> selectApprovalsByBusinessIdAndType(Long businessId, String businessType)
    {
        VehicleShiftApproval query = new VehicleShiftApproval();
        query.setBusinessId(businessId);
        query.setBusinessType(businessType);
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalList(query);
    }

    /**
     * 根据审批级别查询待审批列表
     * 
     * @param approvalLevel 审批级别
     * @return 审批集合
     */
    @Override
    public List<VehicleShiftApproval> selectPendingApprovalsByLevel(Integer approvalLevel)
    {
        VehicleShiftApproval query = new VehicleShiftApproval();
        query.setApprovalLevel(approvalLevel);
        query.setApprovalStatus("pending");
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalList(query);
    }

    /**
     * 检查是否需要下一级审批
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param currentLevel 当前审批级别
     * @return 是否需要下一级审批
     */
    @Override
    public boolean needNextLevelApproval(String businessType, Long businessId, Integer currentLevel)
    {
        // 简单实现：最多3级审批
        return currentLevel < 3;
    }

    /**
     * 创建下一级审批记录
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param currentLevel 当前审批级别
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int createNextLevelApproval(String businessType, Long businessId, Integer currentLevel, String operName)
    {
        Integer nextLevel = currentLevel + 1;
        String nextApprover = getNextLevelApprover(businessType, nextLevel);
        
        return createApprovalRecord(businessType, businessId, nextLevel, nextApprover, operName);
    }

    /**
     * 获取下一级审批人
     * 
     * @param businessType 业务类型
     * @param level 审批级别
     * @return 审批人
     */
    private String getNextLevelApprover(String businessType, Integer level)
    {
        // 简单实现，实际应该从配置表或规则引擎获取
        if (level == 1) {
            return "team_leader";
        } else if (level == 2) {
            return "department_manager";
        } else if (level == 3) {
            return "general_manager";
        }
        return "admin";
    }

    /**
     * 获取审批统计信息
     * 
     * @param approver 审批人
     * @return 统计信息
     */
    @Override
    public Object getApprovalStatistics(String approver)
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 待审批数量
        List<VehicleShiftApproval> pending = selectPendingApprovalsByApprover(approver);
        statistics.put("pendingCount", pending.size());
        
        // 今日已审批数量
        VehicleShiftApproval todayQuery = new VehicleShiftApproval();
        todayQuery.setApprover(approver);
        // 这里应该添加日期条件，简化处理
        statistics.put("todayApprovedCount", 0);
        
        // 本月已审批数量
        statistics.put("monthApprovedCount", 0);
        
        return statistics;
    }

    /**
     * 查询超时未审批的记录
     * 
     * @param timeoutHours 超时小时数
     * @return 审批集合
     */
    @Override
    public List<VehicleShiftApproval> selectTimeoutApprovals(Integer timeoutHours)
    {
        VehicleShiftApproval query = new VehicleShiftApproval();
        query.setApprovalStatus("pending");
        // 这里应该添加时间条件，简化处理
        return vehicleShiftApprovalMapper.selectVehicleShiftApprovalList(query);
    }
}
