package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 机械车辆信息对象 vehicle_info
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 车辆ID */
    private Long vehicleId;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 车辆型号 */
    @Excel(name = "车辆型号")
    private String vehicleModel;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String unitName;

    /** 车牌号码 */
    @Excel(name = "车牌号码")
    private String licensePlate;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    private String driverName;

    /** 司机电话 */
    @Excel(name = "司机电话")
    private String driverPhone;

    /** 指挥姓名 */
    @Excel(name = "指挥姓名")
    private String commanderName;

    /** 指挥电话 */
    @Excel(name = "指挥电话")
    private String commanderPhone;

    /** 车辆入场时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "车辆入场时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date entryTime;

    /** 一期/二期 */
    @Excel(name = "项目期", readConverterExp = "一期=1,二期=2")
    private String projectPhase;

    /** 车辆状态 */
    @Excel(name = "车辆状态", readConverterExp = "可用=0,故障=1,维护=2,退场=3")
    private String vehicleStatus;

    /** 台班确认人 */
    @Excel(name = "台班确认人")
    private String shiftConfirmer;

    /** 费用计量单位 */
    @Excel(name = "费用计量单位", readConverterExp = "天=day,小时=hour")
    private String costUnit;

    /** 车辆重量（吨） */
    @Excel(name = "车辆重量")
    private BigDecimal vehicleWeight;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 每小时费用 */
    @Excel(name = "每小时费用")
    private BigDecimal costPerHour;

    public void setVehicleId(Long vehicleId) 
    {
        this.vehicleId = vehicleId;
    }

    public Long getVehicleId() 
    {
        return vehicleId;
    }

    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    @NotBlank(message = "车辆类型不能为空")
    @Size(min = 0, max = 50, message = "车辆类型长度不能超过50个字符")
    public String getVehicleType() 
    {
        return vehicleType;
    }

    public void setVehicleModel(String vehicleModel) 
    {
        this.vehicleModel = vehicleModel;
    }

    @NotBlank(message = "车辆型号不能为空")
    @Size(min = 0, max = 100, message = "车辆型号长度不能超过100个字符")
    public String getVehicleModel() 
    {
        return vehicleModel;
    }

    public void setUnitName(String unitName) 
    {
        this.unitName = unitName;
    }

    @NotBlank(message = "单位名称不能为空")
    @Size(min = 0, max = 200, message = "单位名称长度不能超过200个字符")
    public String getUnitName() 
    {
        return unitName;
    }

    public void setLicensePlate(String licensePlate) 
    {
        this.licensePlate = licensePlate;
    }

    @Size(min = 0, max = 20, message = "车牌号码长度不能超过20个字符")
    public String getLicensePlate() 
    {
        return licensePlate;
    }

    public void setDriverName(String driverName) 
    {
        this.driverName = driverName;
    }

    @Size(min = 0, max = 50, message = "司机姓名长度不能超过50个字符")
    public String getDriverName() 
    {
        return driverName;
    }

    public void setDriverPhone(String driverPhone) 
    {
        this.driverPhone = driverPhone;
    }

    @Size(min = 0, max = 20, message = "司机电话长度不能超过20个字符")
    public String getDriverPhone() 
    {
        return driverPhone;
    }

    public void setCommanderName(String commanderName) 
    {
        this.commanderName = commanderName;
    }

    @Size(min = 0, max = 50, message = "指挥姓名长度不能超过50个字符")
    public String getCommanderName() 
    {
        return commanderName;
    }

    public void setCommanderPhone(String commanderPhone) 
    {
        this.commanderPhone = commanderPhone;
    }

    @Size(min = 0, max = 20, message = "指挥电话长度不能超过20个字符")
    public String getCommanderPhone() 
    {
        return commanderPhone;
    }

    public void setEntryTime(Date entryTime) 
    {
        this.entryTime = entryTime;
    }

    public Date getEntryTime() 
    {
        return entryTime;
    }

    public void setProjectPhase(String projectPhase) 
    {
        this.projectPhase = projectPhase;
    }

    @Size(min = 0, max = 10, message = "项目期长度不能超过10个字符")
    public String getProjectPhase() 
    {
        return projectPhase;
    }

    public void setVehicleStatus(String vehicleStatus) 
    {
        this.vehicleStatus = vehicleStatus;
    }

    @Size(min = 0, max = 20, message = "车辆状态长度不能超过20个字符")
    public String getVehicleStatus() 
    {
        return vehicleStatus;
    }

    public void setShiftConfirmer(String shiftConfirmer) 
    {
        this.shiftConfirmer = shiftConfirmer;
    }

    @Size(min = 0, max = 200, message = "台班确认人长度不能超过200个字符")
    public String getShiftConfirmer() 
    {
        return shiftConfirmer;
    }

    public void setCostUnit(String costUnit) 
    {
        this.costUnit = costUnit;
    }

    @Size(min = 0, max = 10, message = "费用计量单位长度不能超过10个字符")
    public String getCostUnit()
    {
        return costUnit;
    }

    public void setVehicleWeight(BigDecimal vehicleWeight)
    {
        this.vehicleWeight = vehicleWeight;
    }

    public BigDecimal getVehicleWeight()
    {
        return vehicleWeight;
    }

    public void setUnitPrice(BigDecimal unitPrice)
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice()
    {
        return unitPrice;
    }

    public void setCostPerHour(BigDecimal costPerHour)
    {
        this.costPerHour = costPerHour;
    }

    public BigDecimal getCostPerHour()
    {
        return costPerHour;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("vehicleId", getVehicleId())
            .append("vehicleType", getVehicleType())
            .append("vehicleModel", getVehicleModel())
            .append("unitName", getUnitName())
            .append("licensePlate", getLicensePlate())
            .append("driverName", getDriverName())
            .append("driverPhone", getDriverPhone())
            .append("commanderName", getCommanderName())
            .append("commanderPhone", getCommanderPhone())
            .append("entryTime", getEntryTime())
            .append("projectPhase", getProjectPhase())
            .append("vehicleStatus", getVehicleStatus())
            .append("shiftConfirmer", getShiftConfirmer())
            .append("costUnit", getCostUnit())
            .append("vehicleWeight", getVehicleWeight())
            .append("unitPrice", getUnitPrice())
            .append("costPerHour", getCostPerHour())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
