<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">机械用车申请</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="medium">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请标题" prop="applicationTitle">
              <el-input v-model="form.applicationTitle" placeholder="请输入申请标题" maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="form.applicant" placeholder="请输入申请人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="vehicleType">
              <el-select v-model="form.vehicleType" placeholder="请选择车辆类型" @change="handleVehicleTypeChange">
                <el-option
                  v-for="type in vehicleTypeOptions"
                  :key="type.dictValue"
                  :label="type.dictLabel"
                  :value="type.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆型号" prop="vehicleModel">
              <el-select v-model="form.vehicleModel" placeholder="请选择车辆型号" :disabled="!form.vehicleType">
                <el-option
                  v-for="model in vehicleModelOptions"
                  :key="model.dictValue"
                  :label="model.dictLabel"
                  :value="model.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="队伍信息" prop="teamId">
              <el-select v-model="form.teamId" placeholder="请选择队伍" @change="handleTeamChange">
                <el-option
                  v-for="team in teamOptions"
                  :key="team.teamId"
                  :label="team.teamName"
                  :value="team.teamId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="applicantPhone">
              <el-input v-model="form.applicantPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="用车地点" prop="usageLocation">
              <el-input v-model="form.usageLocation" placeholder="请输入详细的用车地点" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用车开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="请选择用车开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用车结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="请选择用车结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="施工作业说明" prop="workDescription">
          <el-input
            v-model="form.workDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细说明施工作业内容"
            maxlength="500"
            show-word-limit />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            maxlength="200"
            show-word-limit />
        </el-form-item>

        <!-- 车辆忙闲状态展示 -->
        <el-form-item label="车辆状态">
          <el-button type="text" @click="showVehicleStatus" icon="el-icon-view">
            查看当前车辆忙闲状态
          </el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            <i class="el-icon-check"></i> 提交申请
          </el-button>
          <el-button @click="resetForm">
            <i class="el-icon-refresh-left"></i> 重置
          </el-button>
          <el-button @click="saveDraft">
            <i class="el-icon-document"></i> 保存草稿
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 车辆状态对话框 -->
    <el-dialog title="车辆忙闲状态" :visible.sync="statusDialogVisible" width="800px">
      <el-table :data="vehicleStatusList" v-loading="statusLoading">
        <el-table-column label="车辆类型" prop="vehicleType" />
        <el-table-column label="车辆型号" prop="vehicleModel" />
        <el-table-column label="车牌号" prop="licensePlate" />
        <el-table-column label="司机" prop="driverName" />
        <el-table-column label="状态" prop="vehicleStatus">
          <template slot-scope="scope">
            <el-tag :type="getVehicleStatusType(scope.row.vehicleStatus)">
              {{ getVehicleStatusText(scope.row.vehicleStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="当前位置" prop="currentLocation" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { submitApplication, addApplication } from "@/api/vehicle/application";
import { getTeamOptions } from "@/api/vehicle/team";
import { getAvailableVehicles } from "@/api/vehicle/application";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "ApplicationApply",
  data() {
    return {
      // 表单数据
      form: {
        applicationTitle: '',
        vehicleType: '',
        vehicleModel: '',
        usageLocation: '',
        workDescription: '',
        startTime: '',
        endTime: '',
        teamId: null,
        applicant: '',
        applicantPhone: '',
        remark: ''
      },
      // 提交状态
      submitLoading: false,
      // 选项数据
      vehicleTypeOptions: [],
      vehicleModelOptions: [],
      teamOptions: [],
      // 车辆状态对话框
      statusDialogVisible: false,
      statusLoading: false,
      vehicleStatusList: [],
      // 表单验证规则
      rules: {
        applicationTitle: [
          { required: true, message: "申请标题不能为空", trigger: "blur" },
          { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" }
        ],
        vehicleType: [
          { required: true, message: "请选择车辆类型", trigger: "change" }
        ],
        vehicleModel: [
          { required: true, message: "请选择车辆型号", trigger: "change" }
        ],
        usageLocation: [
          { required: true, message: "用车地点不能为空", trigger: "blur" }
        ],
        workDescription: [
          { required: true, message: "施工作业说明不能为空", trigger: "blur" },
          { min: 10, max: 500, message: "长度在 10 到 500 个字符", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "请选择用车开始时间", trigger: "change" }
        ],
        endTime: [
          { required: true, message: "请选择用车结束时间", trigger: "change" }
        ],
        teamId: [
          { required: true, message: "请选择队伍", trigger: "change" }
        ],
        applicant: [
          { required: true, message: "申请人不能为空", trigger: "blur" }
        ],
        applicantPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.loadOptions();
  },
  methods: {
    /** 加载选项数据 */
    async loadOptions() {
      try {
        // 加载车辆类型字典
        const vehicleTypeRes = await getDicts("vehicle_type");
        this.vehicleTypeOptions = vehicleTypeRes.data;
        
        // 加载队伍选项
        const teamRes = await getTeamOptions();
        this.teamOptions = teamRes.data;
      } catch (error) {
        this.$modal.msgError("加载选项数据失败");
      }
    },
    
    /** 车辆类型变化处理 */
    async handleVehicleTypeChange(value) {
      this.form.vehicleModel = '';
      this.vehicleModelOptions = [];
      
      if (value) {
        try {
          // 根据车辆类型加载对应的型号
          const modelRes = await getDicts("vehicle_model_" + value);
          this.vehicleModelOptions = modelRes.data;
        } catch (error) {
          console.warn("加载车辆型号失败:", error);
        }
      }
    },
    
    /** 队伍变化处理 */
    handleTeamChange(teamId) {
      const selectedTeam = this.teamOptions.find(team => team.teamId === teamId);
      if (selectedTeam) {
        this.form.applicant = selectedTeam.teamLeader;
        this.form.applicantPhone = selectedTeam.leaderPhone;
      }
    },
    
    /** 显示车辆状态 */
    async showVehicleStatus() {
      this.statusDialogVisible = true;
      this.statusLoading = true;
      
      try {
        const response = await getAvailableVehicles({
          vehicleType: this.form.vehicleType,
          startTime: this.form.startTime,
          endTime: this.form.endTime
        });
        this.vehicleStatusList = response.data;
      } catch (error) {
        this.$modal.msgError("获取车辆状态失败");
      } finally {
        this.statusLoading = false;
      }
    },
    
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证时间范围
          if (new Date(this.form.endTime) <= new Date(this.form.startTime)) {
            this.$modal.msgError("用车结束时间必须大于开始时间");
            return;
          }
          
          this.submitLoading = true;
          submitApplication(this.form).then(response => {
            this.$modal.msgSuccess("用车申请提交成功");
            this.goBack();
          }).catch(() => {
            this.submitLoading = false;
          });
        }
      });
    },
    
    /** 保存草稿 */
    saveDraft() {
      const draftData = { ...this.form, approvalStatus: 'draft' };
      addApplication(draftData).then(response => {
        this.$modal.msgSuccess("草稿保存成功");
      });
    },
    
    /** 重置表单 */
    resetForm() {
      this.$refs["form"].resetFields();
      this.vehicleModelOptions = [];
    },
    
    /** 获取车辆状态类型 */
    getVehicleStatusType(status) {
      const statusMap = {
        'available': 'success',
        'busy': 'warning',
        'maintenance': 'info',
        'fault': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取车辆状态文本 */
    getVehicleStatusText(status) {
      const statusMap = {
        'available': '可用',
        'busy': '使用中',
        'maintenance': '维护中',
        'fault': '故障'
      };
      return statusMap[status] || '未知';
    },
    
    /** 返回列表 */
    goBack() {
      this.$router.push('/vehicle/application');
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-button {
  margin-right: 10px;
}
</style>
