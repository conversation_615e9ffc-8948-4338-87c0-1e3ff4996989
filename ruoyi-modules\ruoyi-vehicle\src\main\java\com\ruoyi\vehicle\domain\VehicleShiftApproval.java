package com.ruoyi.vehicle.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 台班审批对象 vehicle_shift_approval
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleShiftApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 审批ID */
    private Long approvalId;

    /** 业务类型 */
    @Excel(name = "业务类型", readConverterExp = "需求计划=demand_plan,用车订单=vehicle_order")
    private String businessType;

    /** 业务ID */
    @Excel(name = "业务ID")
    private Long businessId;

    /** 审批级别 */
    @Excel(name = "审批级别", readConverterExp = "1=项目调度室,2=机械主管,3=经营部门")
    private Integer approvalLevel;

    /** 审批人 */
    @Excel(name = "审批人")
    private String approver;

    /** 审批状态 */
    @Excel(name = "审批状态", readConverterExp = "待审批=pending,已通过=approved,已拒绝=rejected")
    private String approvalStatus;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审批意见 */
    @Excel(name = "审批意见")
    private String approvalComments;

    public void setApprovalId(Long approvalId) 
    {
        this.approvalId = approvalId;
    }

    public Long getApprovalId() 
    {
        return approvalId;
    }

    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    @NotBlank(message = "业务类型不能为空")
    @Size(min = 0, max = 50, message = "业务类型长度不能超过50个字符")
    public String getBusinessType() 
    {
        return businessType;
    }

    public void setBusinessId(Long businessId) 
    {
        this.businessId = businessId;
    }

    @NotNull(message = "业务ID不能为空")
    public Long getBusinessId() 
    {
        return businessId;
    }

    public void setApprovalLevel(Integer approvalLevel) 
    {
        this.approvalLevel = approvalLevel;
    }

    @NotNull(message = "审批级别不能为空")
    public Integer getApprovalLevel() 
    {
        return approvalLevel;
    }

    public void setApprover(String approver) 
    {
        this.approver = approver;
    }

    @NotBlank(message = "审批人不能为空")
    @Size(min = 0, max = 50, message = "审批人长度不能超过50个字符")
    public String getApprover() 
    {
        return approver;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    @Size(min = 0, max = 20, message = "审批状态长度不能超过20个字符")
    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }

    public void setApprovalComments(String approvalComments) 
    {
        this.approvalComments = approvalComments;
    }

    public String getApprovalComments() 
    {
        return approvalComments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("approvalId", getApprovalId())
            .append("businessType", getBusinessType())
            .append("businessId", getBusinessId())
            .append("approvalLevel", getApprovalLevel())
            .append("approver", getApprover())
            .append("approvalStatus", getApprovalStatus())
            .append("approvalTime", getApprovalTime())
            .append("approvalComments", getApprovalComments())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
