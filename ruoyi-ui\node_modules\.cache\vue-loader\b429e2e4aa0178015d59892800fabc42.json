{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue", "mtime": 1754139905404}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVmVoaWNsZVN0YXRpc3RpY3MiLAogIGRpY3RzOiBbJ3ZlaGljbGVfdHlwZSddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3RhcnREYXRlOiBudWxsLAogICAgICAgIGVuZERhdGU6IG51bGwsCiAgICAgICAgdmVoaWNsZVR5cGU6IG51bGwKICAgICAgfSwKICAgICAgLy8g5oC75L2T57uf6K6h5pWw5o2uCiAgICAgIG92ZXJhbGxTdGF0czoge30sCiAgICAgIC8vIOaXtumXtOiMg+WbtAogICAgICB0aW1lUmFuZ2U6ICdkYXknLAogICAgICAvLyDovabovobmjpLooYzmppwKICAgICAgdmVoaWNsZVJhbmtpbmc6IFtdLAogICAgICAvLyDpmJ/kvI3mjpLooYzmppwKICAgICAgdGVhbVJhbmtpbmc6IFtdLAogICAgICAvLyDlm77ooajlrp7kvosKICAgICAgY2hhcnRzOiB7CiAgICAgICAgdmVoaWNsZVR5cGU6IG51bGwsCiAgICAgICAgdGVhbTogbnVsbCwKICAgICAgICB0cmVuZDogbnVsbAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdERlZmF1bHREYXRlcygpOwogICAgdGhpcy5nZXRPdmVyYWxsU3RhdGlzdGljcygpOwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdENoYXJ0cygpOwogICAgdGhpcy5sb2FkQWxsRGF0YSgpOwogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIC8vIOmUgOavgeWbvuihqOWunuS+iwogICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7CiAgICAgIGlmIChjaGFydCkgewogICAgICAgIGNoYXJ0LmRpc3Bvc2UoKTsKICAgICAgfQogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yid5aeL5YyW6buY6K6k5pel5pyfICovCiAgICBpbml0RGVmYXVsdERhdGVzKCkgewogICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKTsgLy8gMzDlpKnliY0KICAgICAgCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnREYXRlID0gdGhpcy5mb3JtYXREYXRlKHN0YXJ0KTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmREYXRlID0gdGhpcy5mb3JtYXREYXRlKGVuZCk7CiAgICB9LAogICAgLyoqIOagvOW8j+WMluaXpeacnyAqLwogICAgZm9ybWF0RGF0ZShkYXRlKSB7CiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7CiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhkYXRlLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7CiAgICB9LAogICAgLyoqIOWIneWni+WMluWbvuihqCAqLwogICAgaW5pdENoYXJ0cygpIHsKICAgICAgdGhpcy5jaGFydHMudmVoaWNsZVR5cGUgPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3ZlaGljbGVUeXBlQ2hhcnQnKSk7CiAgICAgIHRoaXMuY2hhcnRzLnRlYW0gPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3RlYW1DaGFydCcpKTsKICAgICAgdGhpcy5jaGFydHMudHJlbmQgPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3RyZW5kQ2hhcnQnKSk7CiAgICB9LAogICAgLyoqIOiOt+WPluaAu+S9k+e7n+iuoSAqLwogICAgZ2V0T3ZlcmFsbFN0YXRpc3RpY3MoKSB7CiAgICAgIC8vIFRPRE86IOiwg+eUqEFQSeiOt+WPluaAu+S9k+e7n+iuoeaVsOaNrgogICAgICAvLyDmqKHmi5/mlbDmja4KICAgICAgdGhpcy5vdmVyYWxsU3RhdHMgPSB7CiAgICAgICAgdG90YWxWZWhpY2xlczogMTU2LAogICAgICAgIHRvdGFsT3JkZXJzOiAxMjQ4LAogICAgICAgIHRvdGFsV29ya0hvdXJzOiAzNDU2LAogICAgICAgIHV0aWxpemF0aW9uUmF0ZTogNzguNQogICAgICB9OwogICAgfSwKICAgIC8qKiDliqDovb3miYDmnInmlbDmja4gKi8KICAgIGxvYWRBbGxEYXRhKCkgewogICAgICB0aGlzLmxvYWRWZWhpY2xlVHlwZURhdGEoKTsKICAgICAgdGhpcy5sb2FkVGVhbURhdGEoKTsKICAgICAgdGhpcy5sb2FkVHJlbmREYXRhKCk7CiAgICAgIHRoaXMubG9hZFJhbmtpbmdEYXRhKCk7CiAgICB9LAogICAgLyoqIOWKoOi9vei9pui+huexu+Wei+aVsOaNriAqLwogICAgbG9hZFZlaGljbGVUeXBlRGF0YSgpIHsKICAgICAgLy8gVE9ETzog6LCD55SoQVBJ6I635Y+W6L2m6L6G57G75Z6L57uf6K6h5pWw5o2uCiAgICAgIC8vIOaooeaLn+aVsOaNrgogICAgICBjb25zdCBkYXRhID0gWwogICAgICAgIHsgbmFtZTogJ+aMluaOmOacuicsIHZhbHVlOiA0NSB9LAogICAgICAgIHsgbmFtZTogJ+aOqOWcn+acuicsIHZhbHVlOiAzMiB9LAogICAgICAgIHsgbmFtZTogJ+ijhei9veacuicsIHZhbHVlOiAyOCB9LAogICAgICAgIHsgbmFtZTogJ+i1t+mHjeacuicsIHZhbHVlOiAyNSB9LAogICAgICAgIHsgbmFtZTogJ+i/kOi+k+i9picsIHZhbHVlOiAyNiB9CiAgICAgIF07CiAgICAgIAogICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgdGl0bGU6IHsKICAgICAgICAgIHRleHQ6ICfovabovobnsbvlnovliIbluIMnLAogICAgICAgICAgbGVmdDogJ2NlbnRlcicKICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJwogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICfovabovobmlbDph48nLAogICAgICAgICAgICB0eXBlOiAncGllJywKICAgICAgICAgICAgcmFkaXVzOiAnNTAlJywKICAgICAgICAgICAgZGF0YTogZGF0YSwKICAgICAgICAgICAgZW1waGFzaXM6IHsKICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDEwLAogICAgICAgICAgICAgICAgc2hhZG93T2Zmc2V0WDogMCwKICAgICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjUpJwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfTsKICAgICAgCiAgICAgIHRoaXMuY2hhcnRzLnZlaGljbGVUeXBlLnNldE9wdGlvbihvcHRpb24pOwogICAgfSwKICAgIC8qKiDliqDovb3pmJ/kvI3mlbDmja4gKi8KICAgIGxvYWRUZWFtRGF0YSgpIHsKICAgICAgLy8gVE9ETzog6LCD55SoQVBJ6I635Y+W6Zif5LyN57uf6K6h5pWw5o2uCiAgICAgIC8vIOaooeaLn+aVsOaNrgogICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgdGl0bGU6IHsKICAgICAgICAgIHRleHQ6ICfpmJ/kvI3nlKjovabnu5/orqEnLAogICAgICAgICAgbGVmdDogJ2NlbnRlcicKICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB4QXhpczogewogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgIGRhdGE6IFsn56ys5LiA5pa95bel6ZifJywgJ+esrOS6jOaWveW3pemYnycsICfnrKzkuInmlr3lt6XpmJ8nLCAn5py65qKw57u05L+u6ZifJywgJ+WuieWFqOebkeedo+mYnyddCiAgICAgICAgfSwKICAgICAgICB5QXhpczogewogICAgICAgICAgdHlwZTogJ3ZhbHVlJwogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICfnlKjovabmrKHmlbAnLAogICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgZGF0YTogWzEyMCwgOTgsIDg3LCA0NSwgMjNdLAogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM0MDlFRkYnCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH07CiAgICAgIAogICAgICB0aGlzLmNoYXJ0cy50ZWFtLnNldE9wdGlvbihvcHRpb24pOwogICAgfSwKICAgIC8qKiDliqDovb3otovlir/mlbDmja4gKi8KICAgIGxvYWRUcmVuZERhdGEoKSB7CiAgICAgIC8vIFRPRE86IOiwg+eUqEFQSeiOt+WPlui2i+WKv+e7n+iuoeaVsOaNrgogICAgICAvLyDmqKHmi5/mlbDmja4KICAgICAgY29uc3Qgb3B0aW9uID0gewogICAgICAgIHRpdGxlOiB7CiAgICAgICAgICB0ZXh0OiAn55So6L2m6LaL5Yq/57uf6K6hJywKICAgICAgICAgIGxlZnQ6ICdjZW50ZXInCiAgICAgICAgfSwKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycKICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgZGF0YTogWyforqLljZXmlbDph48nLCAn5bel5L2c5pe26ZW/J10sCiAgICAgICAgICB0b3A6IDMwCiAgICAgICAgfSwKICAgICAgICB4QXhpczogewogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgIGRhdGE6IFsnMeaciCcsICcy5pyIJywgJzPmnIgnLCAnNOaciCcsICc15pyIJywgJzbmnIgnLCAnN+aciCcsICc45pyIJ10KICAgICAgICB9LAogICAgICAgIHlBeGlzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsCiAgICAgICAgICAgIG5hbWU6ICforqLljZXmlbDph48nCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgICBuYW1lOiAn5bel5L2c5pe26ZW/JwogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICforqLljZXmlbDph48nLAogICAgICAgICAgICB0eXBlOiAnbGluZScsCiAgICAgICAgICAgIGRhdGE6IFsxMjAsIDEzMiwgMTAxLCAxMzQsIDkwLCAyMzAsIDIxMCwgMTU2XQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+W3peS9nOaXtumVvycsCiAgICAgICAgICAgIHR5cGU6ICdiYXInLAogICAgICAgICAgICB5QXhpc0luZGV4OiAxLAogICAgICAgICAgICBkYXRhOiBbMi4wLCA0LjksIDcuMCwgMjMuMiwgMjUuNiwgNzYuNywgMTM1LjYsIDE2Mi4yXQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfTsKICAgICAgCiAgICAgIHRoaXMuY2hhcnRzLnRyZW5kLnNldE9wdGlvbihvcHRpb24pOwogICAgfSwKICAgIC8qKiDliqDovb3mjpLooYzmppzmlbDmja4gKi8KICAgIGxvYWRSYW5raW5nRGF0YSgpIHsKICAgICAgLy8gVE9ETzog6LCD55SoQVBJ6I635Y+W5o6S6KGM5qac5pWw5o2uCiAgICAgIC8vIOaooeaLn+aVsOaNrgogICAgICB0aGlzLnZlaGljbGVSYW5raW5nID0gWwogICAgICAgIHsgcmFuazogMSwgdmVoaWNsZU1vZGVsOiAn5Y2h54m5MzIwRCcsIHVzYWdlQ291bnQ6IDQ1LCB3b3JrSG91cnM6IDM2MCB9LAogICAgICAgIHsgcmFuazogMiwgdmVoaWNsZU1vZGVsOiAn5bCP5p2+UEMyMDAnLCB1c2FnZUNvdW50OiAzOCwgd29ya0hvdXJzOiAzMDQgfSwKICAgICAgICB7IHJhbms6IDMsIHZlaGljbGVNb2RlbDogJ+S4ieS4gFNZMjE1JywgdXNhZ2VDb3VudDogMzIsIHdvcmtIb3VyczogMjU2IH0sCiAgICAgICAgeyByYW5rOiA0LCB2ZWhpY2xlTW9kZWw6ICflvpDlt6VYRTIxNScsIHVzYWdlQ291bnQ6IDI4LCB3b3JrSG91cnM6IDIyNCB9LAogICAgICAgIHsgcmFuazogNSwgdmVoaWNsZU1vZGVsOiAn5p+z5belQ0xHOTIyJywgdXNhZ2VDb3VudDogMjUsIHdvcmtIb3VyczogMjAwIH0KICAgICAgXTsKICAgICAgCiAgICAgIHRoaXMudGVhbVJhbmtpbmcgPSBbCiAgICAgICAgeyByYW5rOiAxLCB0ZWFtTmFtZTogJ+esrOS4gOaWveW3pemYnycsIG9yZGVyQ291bnQ6IDEyMCwgd29ya0hvdXJzOiA5NjAgfSwKICAgICAgICB7IHJhbms6IDIsIHRlYW1OYW1lOiAn56ys5LqM5pa95bel6ZifJywgb3JkZXJDb3VudDogOTgsIHdvcmtIb3VyczogNzg0IH0sCiAgICAgICAgeyByYW5rOiAzLCB0ZWFtTmFtZTogJ+esrOS4ieaWveW3pemYnycsIG9yZGVyQ291bnQ6IDg3LCB3b3JrSG91cnM6IDY5NiB9LAogICAgICAgIHsgcmFuazogNCwgdGVhbU5hbWU6ICfmnLrmorDnu7Tkv67pmJ8nLCBvcmRlckNvdW50OiA0NSwgd29ya0hvdXJzOiAzNjAgfSwKICAgICAgICB7IHJhbms6IDUsIHRlYW1OYW1lOiAn5a6J5YWo55uR552j6ZifJywgb3JkZXJDb3VudDogMjMsIHdvcmtIb3VyczogMTg0IH0KICAgICAgXTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5sb2FkQWxsRGF0YSgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5pbml0RGVmYXVsdERhdGVzKCk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5Yi35paw6L2m6L6G57G75Z6L5Zu+6KGoICovCiAgICByZWZyZXNoVmVoaWNsZVR5cGVDaGFydCgpIHsKICAgICAgdGhpcy5sb2FkVmVoaWNsZVR5cGVEYXRhKCk7CiAgICB9LAogICAgLyoqIOWIt+aWsOmYn+S8jeWbvuihqCAqLwogICAgcmVmcmVzaFRlYW1DaGFydCgpIHsKICAgICAgdGhpcy5sb2FkVGVhbURhdGEoKTsKICAgIH0sCiAgICAvKiog5pS55Y+Y5pe26Ze06IyD5Zu0ICovCiAgICBjaGFuZ2VUaW1lUmFuZ2UocmFuZ2UpIHsKICAgICAgdGhpcy50aW1lUmFuZ2UgPSByYW5nZTsKICAgICAgdGhpcy5sb2FkVHJlbmREYXRhKCk7CiAgICB9LAogICAgLyoqIOWvvOWHuue7n+iuoeaKpeihqCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICAvLyBUT0RPOiDlrp7njrDlr7zlh7rlip/og70KICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a+85Ye65Yqf6IO95byA5Y+R5LitLi4uIik7CiAgICB9LAogICAgLyoqIOWIt+aWsOaJgOacieaVsOaNriAqLwogICAgaGFuZGxlUmVmcmVzaEFsbCgpIHsKICAgICAgdGhpcy5nZXRPdmVyYWxsU3RhdGlzdGljcygpOwogICAgICB0aGlzLmxvYWRBbGxEYXRhKCk7CiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaVsOaNruWIt+aWsOaIkOWKnyIpOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0KA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/statistics", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"开始日期\" prop=\"startDate\">\n        <el-date-picker\n          v-model=\"queryParams.startDate\"\n          type=\"date\"\n          placeholder=\"选择开始日期\"\n          value-format=\"yyyy-MM-dd\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"结束日期\" prop=\"endDate\">\n        <el-date-picker\n          v-model=\"queryParams.endDate\"\n          type=\"date\"\n          placeholder=\"选择结束日期\"\n          value-format=\"yyyy-MM-dd\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"mb20\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #409EFF;\">\n              <i class=\"el-icon-truck\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalVehicles || 0 }}</div>\n              <div class=\"stat-label\">车辆总数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #67C23A;\">\n              <i class=\"el-icon-s-order\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalOrders || 0 }}</div>\n              <div class=\"stat-label\">订单总数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #E6A23C;\">\n              <i class=\"el-icon-time\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalWorkHours || 0 }}</div>\n              <div class=\"stat-label\">总工作时长</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #F56C6C;\">\n              <i class=\"el-icon-warning\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.utilizationRate || 0 }}%</div>\n              <div class=\"stat-label\">车辆利用率</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 图表区域 -->\n    <el-row :gutter=\"20\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>车辆类型统计</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshVehicleTypeChart\">刷新</el-button>\n          </div>\n          <div id=\"vehicleTypeChart\" style=\"height: 300px;\"></div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>队伍用车统计</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshTeamChart\">刷新</el-button>\n          </div>\n          <div id=\"teamChart\" style=\"height: 300px;\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>时间趋势统计</span>\n            <el-button-group style=\"float: right;\">\n              <el-button size=\"mini\" @click=\"changeTimeRange('day')\" :type=\"timeRange === 'day' ? 'primary' : ''\">按天</el-button>\n              <el-button size=\"mini\" @click=\"changeTimeRange('month')\" :type=\"timeRange === 'month' ? 'primary' : ''\">按月</el-button>\n            </el-button-group>\n          </div>\n          <div id=\"trendChart\" style=\"height: 400px;\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 排行榜 -->\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>车辆使用排行榜</span>\n          </div>\n          <el-table :data=\"vehicleRanking\" style=\"width: 100%\" size=\"small\">\n            <el-table-column prop=\"rank\" label=\"排名\" width=\"60\" align=\"center\" />\n            <el-table-column prop=\"vehicleModel\" label=\"车辆型号\" />\n            <el-table-column prop=\"usageCount\" label=\"使用次数\" align=\"center\" />\n            <el-table-column prop=\"workHours\" label=\"工作时长\" align=\"center\" />\n          </el-table>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>队伍用车排行榜</span>\n          </div>\n          <el-table :data=\"teamRanking\" style=\"width: 100%\" size=\"small\">\n            <el-table-column prop=\"rank\" label=\"排名\" width=\"60\" align=\"center\" />\n            <el-table-column prop=\"teamName\" label=\"队伍名称\" />\n            <el-table-column prop=\"orderCount\" label=\"订单数量\" align=\"center\" />\n            <el-table-column prop=\"workHours\" label=\"工作时长\" align=\"center\" />\n          </el-table>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 导出按钮 -->\n    <el-row style=\"margin-top: 20px;\">\n      <el-col :span=\"24\" style=\"text-align: center;\">\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"handleExport\">导出统计报表</el-button>\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleRefreshAll\">刷新所有数据</el-button>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: \"VehicleStatistics\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 显示搜索条件\n      showSearch: true,\n      // 查询参数\n      queryParams: {\n        startDate: null,\n        endDate: null,\n        vehicleType: null\n      },\n      // 总体统计数据\n      overallStats: {},\n      // 时间范围\n      timeRange: 'day',\n      // 车辆排行榜\n      vehicleRanking: [],\n      // 队伍排行榜\n      teamRanking: [],\n      // 图表实例\n      charts: {\n        vehicleType: null,\n        team: null,\n        trend: null\n      }\n    };\n  },\n  created() {\n    this.initDefaultDates();\n    this.getOverallStatistics();\n  },\n  mounted() {\n    this.initCharts();\n    this.loadAllData();\n  },\n  beforeDestroy() {\n    // 销毁图表实例\n    Object.values(this.charts).forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  methods: {\n    /** 初始化默认日期 */\n    initDefaultDates() {\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前\n      \n      this.queryParams.startDate = this.formatDate(start);\n      this.queryParams.endDate = this.formatDate(end);\n    },\n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n    /** 初始化图表 */\n    initCharts() {\n      this.charts.vehicleType = echarts.init(document.getElementById('vehicleTypeChart'));\n      this.charts.team = echarts.init(document.getElementById('teamChart'));\n      this.charts.trend = echarts.init(document.getElementById('trendChart'));\n    },\n    /** 获取总体统计 */\n    getOverallStatistics() {\n      // TODO: 调用API获取总体统计数据\n      // 模拟数据\n      this.overallStats = {\n        totalVehicles: 156,\n        totalOrders: 1248,\n        totalWorkHours: 3456,\n        utilizationRate: 78.5\n      };\n    },\n    /** 加载所有数据 */\n    loadAllData() {\n      this.loadVehicleTypeData();\n      this.loadTeamData();\n      this.loadTrendData();\n      this.loadRankingData();\n    },\n    /** 加载车辆类型数据 */\n    loadVehicleTypeData() {\n      // TODO: 调用API获取车辆类型统计数据\n      // 模拟数据\n      const data = [\n        { name: '挖掘机', value: 45 },\n        { name: '推土机', value: 32 },\n        { name: '装载机', value: 28 },\n        { name: '起重机', value: 25 },\n        { name: '运输车', value: 26 }\n      ];\n      \n      const option = {\n        title: {\n          text: '车辆类型分布',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        series: [\n          {\n            name: '车辆数量',\n            type: 'pie',\n            radius: '50%',\n            data: data,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            }\n          }\n        ]\n      };\n      \n      this.charts.vehicleType.setOption(option);\n    },\n    /** 加载队伍数据 */\n    loadTeamData() {\n      // TODO: 调用API获取队伍统计数据\n      // 模拟数据\n      const option = {\n        title: {\n          text: '队伍用车统计',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        xAxis: {\n          type: 'category',\n          data: ['第一施工队', '第二施工队', '第三施工队', '机械维修队', '安全监督队']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [\n          {\n            name: '用车次数',\n            type: 'bar',\n            data: [120, 98, 87, 45, 23],\n            itemStyle: {\n              color: '#409EFF'\n            }\n          }\n        ]\n      };\n      \n      this.charts.team.setOption(option);\n    },\n    /** 加载趋势数据 */\n    loadTrendData() {\n      // TODO: 调用API获取趋势统计数据\n      // 模拟数据\n      const option = {\n        title: {\n          text: '用车趋势统计',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: ['订单数量', '工作时长'],\n          top: 30\n        },\n        xAxis: {\n          type: 'category',\n          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月']\n        },\n        yAxis: [\n          {\n            type: 'value',\n            name: '订单数量'\n          },\n          {\n            type: 'value',\n            name: '工作时长'\n          }\n        ],\n        series: [\n          {\n            name: '订单数量',\n            type: 'line',\n            data: [120, 132, 101, 134, 90, 230, 210, 156]\n          },\n          {\n            name: '工作时长',\n            type: 'bar',\n            yAxisIndex: 1,\n            data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2]\n          }\n        ]\n      };\n      \n      this.charts.trend.setOption(option);\n    },\n    /** 加载排行榜数据 */\n    loadRankingData() {\n      // TODO: 调用API获取排行榜数据\n      // 模拟数据\n      this.vehicleRanking = [\n        { rank: 1, vehicleModel: '卡特320D', usageCount: 45, workHours: 360 },\n        { rank: 2, vehicleModel: '小松PC200', usageCount: 38, workHours: 304 },\n        { rank: 3, vehicleModel: '三一SY215', usageCount: 32, workHours: 256 },\n        { rank: 4, vehicleModel: '徐工XE215', usageCount: 28, workHours: 224 },\n        { rank: 5, vehicleModel: '柳工CLG922', usageCount: 25, workHours: 200 }\n      ];\n      \n      this.teamRanking = [\n        { rank: 1, teamName: '第一施工队', orderCount: 120, workHours: 960 },\n        { rank: 2, teamName: '第二施工队', orderCount: 98, workHours: 784 },\n        { rank: 3, teamName: '第三施工队', orderCount: 87, workHours: 696 },\n        { rank: 4, teamName: '机械维修队', orderCount: 45, workHours: 360 },\n        { rank: 5, teamName: '安全监督队', orderCount: 23, workHours: 184 }\n      ];\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.loadAllData();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.initDefaultDates();\n      this.handleQuery();\n    },\n    /** 刷新车辆类型图表 */\n    refreshVehicleTypeChart() {\n      this.loadVehicleTypeData();\n    },\n    /** 刷新队伍图表 */\n    refreshTeamChart() {\n      this.loadTeamData();\n    },\n    /** 改变时间范围 */\n    changeTimeRange(range) {\n      this.timeRange = range;\n      this.loadTrendData();\n    },\n    /** 导出统计报表 */\n    handleExport() {\n      // TODO: 实现导出功能\n      this.$modal.msgSuccess(\"导出功能开发中...\");\n    },\n    /** 刷新所有数据 */\n    handleRefreshAll() {\n      this.getOverallStatistics();\n      this.loadAllData();\n      this.$modal.msgSuccess(\"数据刷新成功\");\n    }\n  }\n};\n</script>\n\n<style scoped>\n.stat-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 8px;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}