# RuoYi-Cloud 一键部署脚本

基于 Docker 的 RuoYi-Cloud 微服务一键部署解决方案，支持 CentOS 7 系统。

## 📋 功能特性

- ✅ **分离式部署**: 中间件和应用分离部署，便于管理
- ✅ **多镜像源支持**: 内置多个国内镜像源，自动故障转移
- ✅ **可配置化**: 支持自定义镜像列表、端口、资源限制等
- ✅ **环境检查**: 自动检查系统环境和依赖命令
- ✅ **自动安装**: 自动检测并安装缺失的依赖命令
- ✅ **中文版 Portainer**: 容器管理界面
- ✅ **数据分离**: 数据目录与容器分离，便于备份和迁移
- ✅ **健康检查**: 内置服务健康检查机制
- ✅ **日志管理**: 统一日志收集和管理

## 🏗️ 架构说明

### 中间件服务
- **MySQL 5.7**: 关系型数据库服务
- **达梦 DM8**: 国产数据库服务
- **Redis 7.0**: 缓存服务
- **Nacos 2.2.3**: 注册中心和配置中心
- **Nginx 1.24**: 反向代理和静态文件服务
- **Portainer**: 容器管理界面（中文版）

### 应用服务
- **ruoyi-gateway**: API网关服务
- **ruoyi-auth**: 认证授权服务
- **ruoyi-modules-system**: 系统管理服务
- **ruoyi-modules-gen**: 代码生成服务
- **ruoyi-modules-job**: 定时任务服务
- **ruoyi-modules-file**: 文件管理服务
- **ruoyi-visual-monitor**: 监控中心服务

## 📁 目录结构

```
Docker/
├── config.sh                      # 配置文件
├── install.sh                     # 主安装脚本
├── install-middleware.sh          # 中间件部署脚本（MySQL+达梦）
├── install-application.sh         # 应用部署脚本
├── docker-compose-middleware.yml  # 中间件Docker Compose配置
├── docker-compose-application.yml # 应用Docker Compose配置
├── README.md                      # 说明文档
└── README-DATABASE.md             # 数据库支持说明
```

## 🎯 数据库支持

本部署方案同时支持 **MySQL** 和 **达梦数据库**：

- **MySQL 5.7**: 开源关系型数据库，广泛使用
- **达梦 DM8**: 国产商业数据库，支持国产化需求

中间件部署时会**同时安装**两个数据库，应用部署时可以选择使用哪个数据库。

详细的数据库配置和使用说明请参考：[数据库支持说明](README-DATABASE.md)

## 🚀 快速开始

### 1. 环境要求

- **操作系统**: CentOS 7
- **内存**: 建议 4GB 以上
- **磁盘**: 建议 10GB 以上可用空间
- **网络**: 能够访问互联网

### 2. 依赖安装

脚本会自动检查以下依赖，**如果缺失会自动尝试安装**：

```bash
# 安装 Docker
curl -fsSL https://get.docker.com | bash
systemctl enable docker && systemctl start docker

# 安装 Docker Compose
curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 安装其他工具
yum install -y curl wget nc-openbsd firewalld

# 安装 Maven（应用构建需要）
wget https://archive.apache.org/dist/maven/maven-3/3.8.6/binaries/apache-maven-3.8.6-bin.tar.gz
tar -xzf apache-maven-3.8.6-bin.tar.gz -C /opt/
echo 'export PATH=/opt/apache-maven-3.8.6/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 安装 Node.js（前端构建需要）
curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs

# 安装 Java
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel
```

**注意**: 从v1.1版本开始，部署脚本具备智能安装功能：
- 自动检测已安装的软件，避免重复安装
- 如果检测到依赖缺失，会自动尝试安装
- 只有自动安装失败时，才会提示手动安装
- Docker镜像也会检查是否已存在，避免重复下载

### 3. 部署步骤

#### 方式一：完整一键部署

```bash
# 进入部署目录
cd Docker

# 设置执行权限
chmod +x *.sh

# 完整部署（推荐）
./install.sh -f
```

#### 方式二：分步部署（推荐）

```bash
# 1. 检查环境
./install.sh -c

# 2. 部署中间件（MySQL + 达梦数据库 + Redis + Nacos）
./install.sh -m

# 3. 部署应用（可选择数据库类型）
./install.sh -a
```

#### 方式三：单独部署

```bash
# 仅部署中间件（MySQL + 达梦数据库）
./install-middleware.sh

# 仅部署应用（可选择数据库）
./install-application.sh --database=mysql   # 使用MySQL
./install-application.sh --database=dameng  # 使用达梦数据库
```



## ⚙️ 配置说明

### 主要配置项

编辑 `config.sh` 文件可以自定义以下配置：

```bash
# 部署路径
DEPLOY_ROOT="/erdCloud"
DATA_ROOT="/home"

# 数据库配置
MYSQL_ROOT_PASSWORD="RuoYi@2024"
MYSQL_DATABASE="ry-cloud"

# Redis配置
REDIS_PASSWORD="RuoYi@2024"

# 镜像源配置（可添加更多）
DOCKER_REGISTRIES=(
    "registry.cn-hangzhou.aliyuncs.com"
    "registry.cn-beijing.aliyuncs.com"
    # ... 更多镜像源
)
```

### 端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| Nginx | 80 | 前端访问 |
| MySQL | 3306 | MySQL数据库 |
| 达梦数据库 | 5236 | 达梦数据库 |
| Redis | 6379 | 缓存 |
| Nacos | 8848 | 注册中心 |
| Gateway | 8080 | API网关 |
| Auth | 9200 | 认证服务 |
| System | 9201 | 系统服务 |
| Gen | 9202 | 代码生成 |
| Job | 9203 | 定时任务 |
| File | 9300 | 文件服务 |
| Monitor | 9100 | 监控中心 |
| Portainer | 9000 | 容器管理 |

## 🔧 常用命令

### 服务管理

```bash
# 查看所有容器状态
docker ps

# 查看服务日志
docker logs ruoyi-gateway

# 重启服务
docker restart ruoyi-gateway

# 停止所有服务
cd Docker
docker-compose -f docker-compose-middleware.yml down
docker-compose -f docker-compose-application.yml down

# 启动所有服务
docker-compose -f docker-compose-middleware.yml up -d
docker-compose -f docker-compose-application.yml up -d
```

### 数据备份

```bash
# 备份MySQL数据
docker exec ruoyi-mysql mysqldump -uroot -p${MYSQL_ROOT_PASSWORD} --all-databases > backup.sql

# 备份Redis数据
docker exec ruoyi-redis redis-cli --rdb /data/dump.rdb

# 备份配置文件
tar -czf config-backup.tar.gz /erdCloud/config /home/<USER>/home/<USER>/home/<USER>
```

## 🌐 访问地址

部署完成后，可以通过以下地址访问各个服务：

- **前端系统**: http://服务器IP:80
- **API网关**: http://服务器IP:8080
- **Nacos控制台**: http://服务器IP:8848/nacos
- **Portainer**: http://服务器IP:9000

### 默认账号

- **系统管理员**: admin / admin123
- **Nacos**: nacos / nacos

## 🐛 故障排除

### 常见问题

1. **镜像拉取失败**
   - 脚本会自动尝试多个镜像源
   - 检查网络连接
   - 手动配置Docker镜像加速器

2. **服务启动失败**
   - 检查端口是否被占用：`netstat -tlnp | grep 端口号`
   - 查看容器日志：`docker logs 容器名`
   - 检查磁盘空间：`df -h`

3. **前端访问404**
   - 确认前端构建成功
   - 检查Nginx配置
   - 查看Nginx日志

4. **数据库连接失败**
   - 确认MySQL容器正常运行
   - 检查数据库密码配置
   - 查看应用服务日志

### 日志查看

```bash
# 查看部署日志
tail -f /erdCloud/logs/*/app.log

# 查看容器日志
docker logs -f ruoyi-gateway

# 查看系统日志
journalctl -u docker -f
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查相关日志文件
3. 确认系统环境满足要求
4. 提交Issue时请附上详细的错误信息和环境信息

## 📄 许可证

本项目遵循 MIT 许可证。
