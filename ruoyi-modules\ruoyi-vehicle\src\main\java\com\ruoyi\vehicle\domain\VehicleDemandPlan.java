package com.ruoyi.vehicle.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 车辆需求计划对象 vehicle_demand_plan
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleDemandPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 计划ID */
    private Long planId;

    /** 计划标题 */
    @Excel(name = "计划标题")
    private String planTitle;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 车辆型号 */
    @Excel(name = "车辆型号")
    private String vehicleModel;

    /** 需求单位 */
    @Excel(name = "需求单位")
    private String demandUnit;

    /** 需求开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "需求开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date demandStartTime;

    /** 需求结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "需求结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date demandEndTime;

    /** 用途说明 */
    @Excel(name = "用途说明")
    private String usagePurpose;

    /** 队伍ID */
    @Excel(name = "队伍ID")
    private Long teamId;

    /** 队伍信息 */
    private TeamInfo teamInfo;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicant;

    /** 申请人联系方式 */
    @Excel(name = "申请人联系方式")
    private String applicantPhone;

    /** 审批状态 */
    @Excel(name = "审批状态", readConverterExp = "待审批=0,项目调度室审批=1,机械主管审批=2,经营审批=3,已通过=4,已拒绝=5")
    private String approvalStatus;

    /** 当前审批人 */
    @Excel(name = "当前审批人")
    private String currentApprover;

    /** 审批意见 */
    @Excel(name = "审批意见")
    private String approvalComments;

    public void setPlanId(Long planId) 
    {
        this.planId = planId;
    }

    public Long getPlanId() 
    {
        return planId;
    }

    public void setPlanTitle(String planTitle) 
    {
        this.planTitle = planTitle;
    }

    @NotBlank(message = "计划标题不能为空")
    @Size(min = 0, max = 200, message = "计划标题长度不能超过200个字符")
    public String getPlanTitle() 
    {
        return planTitle;
    }

    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    @NotBlank(message = "车辆类型不能为空")
    @Size(min = 0, max = 50, message = "车辆类型长度不能超过50个字符")
    public String getVehicleType() 
    {
        return vehicleType;
    }

    public void setVehicleModel(String vehicleModel) 
    {
        this.vehicleModel = vehicleModel;
    }

    @NotBlank(message = "车辆型号不能为空")
    @Size(min = 0, max = 100, message = "车辆型号长度不能超过100个字符")
    public String getVehicleModel() 
    {
        return vehicleModel;
    }

    public void setDemandUnit(String demandUnit) 
    {
        this.demandUnit = demandUnit;
    }

    @NotBlank(message = "需求单位不能为空")
    @Size(min = 0, max = 200, message = "需求单位长度不能超过200个字符")
    public String getDemandUnit() 
    {
        return demandUnit;
    }

    public void setDemandStartTime(Date demandStartTime) 
    {
        this.demandStartTime = demandStartTime;
    }

    @NotNull(message = "需求开始时间不能为空")
    public Date getDemandStartTime() 
    {
        return demandStartTime;
    }

    public void setDemandEndTime(Date demandEndTime) 
    {
        this.demandEndTime = demandEndTime;
    }

    @NotNull(message = "需求结束时间不能为空")
    public Date getDemandEndTime() 
    {
        return demandEndTime;
    }

    public void setUsagePurpose(String usagePurpose) 
    {
        this.usagePurpose = usagePurpose;
    }

    public String getUsagePurpose() 
    {
        return usagePurpose;
    }

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    @NotNull(message = "队伍ID不能为空")
    public Long getTeamId() 
    {
        return teamId;
    }

    public void setTeamInfo(TeamInfo teamInfo) 
    {
        this.teamInfo = teamInfo;
    }

    public TeamInfo getTeamInfo() 
    {
        return teamInfo;
    }

    public void setApplicant(String applicant) 
    {
        this.applicant = applicant;
    }

    @NotBlank(message = "申请人不能为空")
    @Size(min = 0, max = 50, message = "申请人长度不能超过50个字符")
    public String getApplicant() 
    {
        return applicant;
    }

    public void setApplicantPhone(String applicantPhone) 
    {
        this.applicantPhone = applicantPhone;
    }

    @Size(min = 0, max = 20, message = "申请人联系方式长度不能超过20个字符")
    public String getApplicantPhone() 
    {
        return applicantPhone;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    @Size(min = 0, max = 20, message = "审批状态长度不能超过20个字符")
    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setCurrentApprover(String currentApprover) 
    {
        this.currentApprover = currentApprover;
    }

    @Size(min = 0, max = 50, message = "当前审批人长度不能超过50个字符")
    public String getCurrentApprover() 
    {
        return currentApprover;
    }

    public void setApprovalComments(String approvalComments) 
    {
        this.approvalComments = approvalComments;
    }

    public String getApprovalComments() 
    {
        return approvalComments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("planId", getPlanId())
            .append("planTitle", getPlanTitle())
            .append("vehicleType", getVehicleType())
            .append("vehicleModel", getVehicleModel())
            .append("demandUnit", getDemandUnit())
            .append("demandStartTime", getDemandStartTime())
            .append("demandEndTime", getDemandEndTime())
            .append("usagePurpose", getUsagePurpose())
            .append("teamId", getTeamId())
            .append("applicant", getApplicant())
            .append("applicantPhone", getApplicantPhone())
            .append("approvalStatus", getApprovalStatus())
            .append("currentApprover", getCurrentApprover())
            .append("approvalComments", getApprovalComments())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
