{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\violation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\violation\\index.vue", "mtime": 1754141667786}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RWaW9sYXRpb24sIGdldFZpb2xhdGlvbiwgZGVsVmlvbGF0aW9uLCBhZGRWaW9sYXRpb24sIHVwZGF0ZVZpb2xhdGlvbiwgcHJvY2Vzc1Zpb2xhdGlvbiwgYmF0Y2hQcm9jZXNzVmlvbGF0aW9uIH0gZnJvbSAiQC9hcGkvdmVoaWNsZS92aW9sYXRpb24iOwppbXBvcnQgeyBsaXN0SW5mbyB9IGZyb20gIkAvYXBpL3ZlaGljbGUvaW5mbyI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlZpb2xhdGlvbiIsCiAgZGljdHM6IFsndmlvbGF0aW9uX3N0YXR1cyddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6L+d56ug6K6w5b2V6KGo5qC85pWw5o2uCiAgICAgIHZpb2xhdGlvbkxpc3Q6IFtdLAogICAgICAvLyDovabovobpgInpobkKICAgICAgdmVoaWNsZU9wdGlvbnM6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOi/neeroOaXtumXtOaXtumXtOiMg+WbtAogICAgICBkYXRlcmFuZ2VWaW9sYXRpb25UaW1lOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgdmVoaWNsZUlkOiBudWxsLAogICAgICAgIHZpb2xhdGlvblR5cGU6IG51bGwsCiAgICAgICAgdmlvbGF0aW9uVGltZTogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB2ZWhpY2xlSWQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLovabovobkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICB2aW9sYXRpb25UeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L+d56ug57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdmlvbGF0aW9uVGltZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui/neeroOaXtumXtOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB2aW9sYXRpb25Mb2NhdGlvbjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui/neeroOWcsOeCueS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRWZWhpY2xlT3B0aW9ucygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoui/neeroOiusOW9leWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMgPSB7fTsKICAgICAgaWYgKG51bGwgIT0gdGhpcy5kYXRlcmFuZ2VWaW9sYXRpb25UaW1lICYmICcnICE9IHRoaXMuZGF0ZXJhbmdlVmlvbGF0aW9uVGltZSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJiZWdpblZpb2xhdGlvblRpbWUiXSA9IHRoaXMuZGF0ZXJhbmdlVmlvbGF0aW9uVGltZVswXTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siZW5kVmlvbGF0aW9uVGltZSJdID0gdGhpcy5kYXRlcmFuZ2VWaW9sYXRpb25UaW1lWzFdOwogICAgICB9CiAgICAgIGxpc3RWaW9sYXRpb24odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy52aW9sYXRpb25MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5bovabovobpgInpobkgKi8KICAgIGdldFZlaGljbGVPcHRpb25zKCkgewogICAgICBsaXN0SW5mbygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMudmVoaWNsZU9wdGlvbnMgPSByZXNwb25zZS5yb3dzOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdmlvbGF0aW9uSWQ6IG51bGwsCiAgICAgICAgdmVoaWNsZUlkOiBudWxsLAogICAgICAgIHZpb2xhdGlvblR5cGU6IG51bGwsCiAgICAgICAgdmlvbGF0aW9uVGltZTogbnVsbCwKICAgICAgICB2aW9sYXRpb25Mb2NhdGlvbjogbnVsbCwKICAgICAgICB2aW9sYXRpb25EZXNjcmlwdGlvbjogbnVsbCwKICAgICAgICBwZW5hbHR5QW1vdW50OiBudWxsLAogICAgICAgIHN0YXR1czogInBlbmRpbmciLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVyYW5nZVZpb2xhdGlvblRpbWUgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udmlvbGF0aW9uSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6L+d56ug6K6w5b2VIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgdmlvbGF0aW9uSWQgPSByb3cudmlvbGF0aW9uSWQgfHwgdGhpcy5pZHMKICAgICAgZ2V0VmlvbGF0aW9uKHZpb2xhdGlvbklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnov53nq6DorrDlvZUiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS52aW9sYXRpb25JZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVZpb2xhdGlvbih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFZpb2xhdGlvbih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgdmlvbGF0aW9uSWRzID0gcm93LnZpb2xhdGlvbklkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTov53nq6DorrDlvZXnvJblj7fkuLoiJyArIHZpb2xhdGlvbklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsVmlvbGF0aW9uKHZpb2xhdGlvbklkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWkhOeQhui/neeroOiusOW9lSAqLwogICAgaGFuZGxlUHJvY2Vzcyhyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5aSE55CG6K+l6L+d56ug6K6w5b2V77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gcHJvY2Vzc1Zpb2xhdGlvbihyb3cudmlvbGF0aW9uSWQpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlpITnkIbmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDmibnph4/lpITnkIbov53nq6DorrDlvZUgKi8KICAgIGhhbmRsZUJhdGNoUHJvY2VzcygpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5om56YeP5aSE55CG6YCJ5Lit55qE6L+d56ug6K6w5b2V77yfJykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGJhdGNoUHJvY2Vzc1Zpb2xhdGlvbih0aGlzLmlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaJuemHj+WkhOeQhuaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCd2ZWhpY2xlL3Zpb2xhdGlvbi9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgdmlvbGF0aW9uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyNA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/violation", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"车辆ID\" prop=\"vehicleId\">\n        <el-select v-model=\"queryParams.vehicleId\" placeholder=\"请选择车辆\" clearable>\n          <el-option\n            v-for=\"vehicle in vehicleOptions\"\n            :key=\"vehicle.vehicleId\"\n            :label=\"vehicle.vehicleModel\"\n            :value=\"vehicle.vehicleId\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"违章类型\" prop=\"violationType\">\n        <el-select v-model=\"queryParams.violationType\" placeholder=\"请选择违章类型\" clearable>\n          <el-option label=\"超速\" value=\"超速\"></el-option>\n          <el-option label=\"违停\" value=\"违停\"></el-option>\n          <el-option label=\"闯红灯\" value=\"闯红灯\"></el-option>\n          <el-option label=\"其他\" value=\"其他\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"处理状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择处理状态\" clearable>\n          <el-option label=\"未处理\" value=\"pending\"></el-option>\n          <el-option label=\"处理中\" value=\"processing\"></el-option>\n          <el-option label=\"已处理\" value=\"processed\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"违章时间\">\n        <el-date-picker\n          v-model=\"daterangeViolationTime\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:violation:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:violation:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:violation:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:violation:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleBatchProcess\"\n          v-hasPermi=\"['vehicle:violation:process']\"\n        >批量处理</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"violationList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"违章ID\" align=\"center\" prop=\"violationId\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" prop=\"vehicleInfo\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"违章类型\" align=\"center\" prop=\"violationType\" />\n      <el-table-column label=\"违章时间\" align=\"center\" prop=\"violationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.violationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"违章地点\" align=\"center\" prop=\"violationLocation\" />\n      <el-table-column label=\"罚款金额\" align=\"center\" prop=\"penaltyAmount\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #E6A23C;\">¥{{ scope.row.penaltyAmount }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"处理状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.violation_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:remove']\"\n          >删除</el-button>\n          <el-button\n            v-if=\"scope.row.status === 'pending'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleProcess(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:process']\"\n          >处理</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改违章记录对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"车辆\" prop=\"vehicleId\">\n          <el-select v-model=\"form.vehicleId\" placeholder=\"请选择车辆\">\n            <el-option\n              v-for=\"vehicle in vehicleOptions\"\n              :key=\"vehicle.vehicleId\"\n              :label=\"vehicle.vehicleModel\"\n              :value=\"vehicle.vehicleId\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"违章类型\" prop=\"violationType\">\n          <el-select v-model=\"form.violationType\" placeholder=\"请选择违章类型\">\n            <el-option label=\"超速\" value=\"超速\"></el-option>\n            <el-option label=\"违停\" value=\"违停\"></el-option>\n            <el-option label=\"闯红灯\" value=\"闯红灯\"></el-option>\n            <el-option label=\"其他\" value=\"其他\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"违章时间\" prop=\"violationTime\">\n          <el-date-picker clearable\n            v-model=\"form.violationTime\"\n            type=\"datetime\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            placeholder=\"请选择违章时间\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"违章地点\" prop=\"violationLocation\">\n          <el-input v-model=\"form.violationLocation\" placeholder=\"请输入违章地点\" />\n        </el-form-item>\n        <el-form-item label=\"罚款金额\" prop=\"penaltyAmount\">\n          <el-input v-model=\"form.penaltyAmount\" placeholder=\"请输入罚款金额\" />\n        </el-form-item>\n        <el-form-item label=\"违章描述\" prop=\"violationDescription\">\n          <el-input v-model=\"form.violationDescription\" type=\"textarea\" placeholder=\"请输入违章描述\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listViolation, getViolation, delViolation, addViolation, updateViolation, processViolation, batchProcessViolation } from \"@/api/vehicle/violation\";\nimport { listInfo } from \"@/api/vehicle/info\";\n\nexport default {\n  name: \"Violation\",\n  dicts: ['violation_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 违章记录表格数据\n      violationList: [],\n      // 车辆选项\n      vehicleOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 违章时间时间范围\n      daterangeViolationTime: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleId: null,\n        violationType: null,\n        violationTime: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        vehicleId: [\n          { required: true, message: \"车辆不能为空\", trigger: \"change\" }\n        ],\n        violationType: [\n          { required: true, message: \"违章类型不能为空\", trigger: \"change\" }\n        ],\n        violationTime: [\n          { required: true, message: \"违章时间不能为空\", trigger: \"blur\" }\n        ],\n        violationLocation: [\n          { required: true, message: \"违章地点不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getVehicleOptions();\n  },\n  methods: {\n    /** 查询违章记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.daterangeViolationTime && '' != this.daterangeViolationTime) {\n        this.queryParams.params[\"beginViolationTime\"] = this.daterangeViolationTime[0];\n        this.queryParams.params[\"endViolationTime\"] = this.daterangeViolationTime[1];\n      }\n      listViolation(this.queryParams).then(response => {\n        this.violationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 获取车辆选项 */\n    getVehicleOptions() {\n      listInfo().then(response => {\n        this.vehicleOptions = response.rows;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        violationId: null,\n        vehicleId: null,\n        violationType: null,\n        violationTime: null,\n        violationLocation: null,\n        violationDescription: null,\n        penaltyAmount: null,\n        status: \"pending\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.daterangeViolationTime = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.violationId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加违章记录\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const violationId = row.violationId || this.ids\n      getViolation(violationId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改违章记录\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.violationId != null) {\n            updateViolation(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addViolation(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const violationIds = row.violationId || this.ids;\n      this.$modal.confirm('是否确认删除违章记录编号为\"' + violationIds + '\"的数据项？').then(function() {\n        return delViolation(violationIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 处理违章记录 */\n    handleProcess(row) {\n      this.$modal.confirm('是否确认处理该违章记录？').then(function() {\n        return processViolation(row.violationId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"处理成功\");\n      }).catch(() => {});\n    },\n    /** 批量处理违章记录 */\n    handleBatchProcess() {\n      this.$modal.confirm('是否确认批量处理选中的违章记录？').then(() => {\n        return batchProcessViolation(this.ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"批量处理成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/violation/export', {\n        ...this.queryParams\n      }, `violation_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"]}]}