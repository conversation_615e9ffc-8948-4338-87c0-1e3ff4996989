package com.ruoyi.vehicle.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 机械用车申请对象 vehicle_application
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 申请标题 */
    @Excel(name = "申请标题")
    private String applicationTitle;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 车辆型号 */
    @Excel(name = "车辆型号")
    private String vehicleModel;

    /** 用车地点 */
    @Excel(name = "用车地点")
    private String usageLocation;

    /** 施工作业说明 */
    @Excel(name = "施工作业说明")
    private String workDescription;

    /** 用车开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "用车开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 用车结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "用车结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 队伍ID */
    @Excel(name = "队伍ID")
    private Long teamId;

    /** 队伍信息 */
    private TeamInfo teamInfo;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applicant;

    /** 申请人联系方式 */
    @Excel(name = "申请人联系方式")
    private String applicantPhone;

    /** 审批状态 */
    @Excel(name = "审批状态", readConverterExp = "待审批=pending,已通过=approved,已拒绝=rejected")
    private String approvalStatus;

    /** 分配的车辆ID */
    @Excel(name = "分配的车辆ID")
    private Long assignedVehicleId;

    /** 分配的车辆信息 */
    private VehicleInfo assignedVehicleInfo;

    /** 分配的司机 */
    @Excel(name = "分配的司机")
    private String assignedDriver;

    /** 调度人员 */
    @Excel(name = "调度人员")
    private String scheduler;

    /** 调度时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "调度时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date scheduleTime;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }

    public void setApplicationTitle(String applicationTitle) 
    {
        this.applicationTitle = applicationTitle;
    }

    @NotBlank(message = "申请标题不能为空")
    @Size(min = 0, max = 200, message = "申请标题长度不能超过200个字符")
    public String getApplicationTitle() 
    {
        return applicationTitle;
    }

    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    @NotBlank(message = "车辆类型不能为空")
    @Size(min = 0, max = 50, message = "车辆类型长度不能超过50个字符")
    public String getVehicleType() 
    {
        return vehicleType;
    }

    public void setVehicleModel(String vehicleModel) 
    {
        this.vehicleModel = vehicleModel;
    }

    @NotBlank(message = "车辆型号不能为空")
    @Size(min = 0, max = 100, message = "车辆型号长度不能超过100个字符")
    public String getVehicleModel() 
    {
        return vehicleModel;
    }

    public void setUsageLocation(String usageLocation) 
    {
        this.usageLocation = usageLocation;
    }

    @NotBlank(message = "用车地点不能为空")
    @Size(min = 0, max = 200, message = "用车地点长度不能超过200个字符")
    public String getUsageLocation() 
    {
        return usageLocation;
    }

    public void setWorkDescription(String workDescription) 
    {
        this.workDescription = workDescription;
    }

    public String getWorkDescription() 
    {
        return workDescription;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    @NotNull(message = "用车开始时间不能为空")
    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    @NotNull(message = "用车结束时间不能为空")
    public Date getEndTime() 
    {
        return endTime;
    }

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    @NotNull(message = "队伍ID不能为空")
    public Long getTeamId() 
    {
        return teamId;
    }

    public void setTeamInfo(TeamInfo teamInfo) 
    {
        this.teamInfo = teamInfo;
    }

    public TeamInfo getTeamInfo() 
    {
        return teamInfo;
    }

    public void setApplicant(String applicant) 
    {
        this.applicant = applicant;
    }

    @NotBlank(message = "申请人不能为空")
    @Size(min = 0, max = 50, message = "申请人长度不能超过50个字符")
    public String getApplicant() 
    {
        return applicant;
    }

    public void setApplicantPhone(String applicantPhone) 
    {
        this.applicantPhone = applicantPhone;
    }

    @Size(min = 0, max = 20, message = "申请人联系方式长度不能超过20个字符")
    public String getApplicantPhone() 
    {
        return applicantPhone;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    @Size(min = 0, max = 20, message = "审批状态长度不能超过20个字符")
    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setAssignedVehicleId(Long assignedVehicleId) 
    {
        this.assignedVehicleId = assignedVehicleId;
    }

    public Long getAssignedVehicleId() 
    {
        return assignedVehicleId;
    }

    public void setAssignedVehicleInfo(VehicleInfo assignedVehicleInfo) 
    {
        this.assignedVehicleInfo = assignedVehicleInfo;
    }

    public VehicleInfo getAssignedVehicleInfo() 
    {
        return assignedVehicleInfo;
    }

    public void setAssignedDriver(String assignedDriver) 
    {
        this.assignedDriver = assignedDriver;
    }

    @Size(min = 0, max = 50, message = "分配的司机长度不能超过50个字符")
    public String getAssignedDriver() 
    {
        return assignedDriver;
    }

    public void setScheduler(String scheduler) 
    {
        this.scheduler = scheduler;
    }

    @Size(min = 0, max = 50, message = "调度人员长度不能超过50个字符")
    public String getScheduler() 
    {
        return scheduler;
    }

    public void setScheduleTime(Date scheduleTime) 
    {
        this.scheduleTime = scheduleTime;
    }

    public Date getScheduleTime() 
    {
        return scheduleTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("applicationTitle", getApplicationTitle())
            .append("vehicleType", getVehicleType())
            .append("vehicleModel", getVehicleModel())
            .append("usageLocation", getUsageLocation())
            .append("workDescription", getWorkDescription())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("teamId", getTeamId())
            .append("applicant", getApplicant())
            .append("applicantPhone", getApplicantPhone())
            .append("approvalStatus", getApprovalStatus())
            .append("assignedVehicleId", getAssignedVehicleId())
            .append("assignedDriver", getAssignedDriver())
            .append("scheduler", getScheduler())
            .append("scheduleTime", getScheduleTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
