import request from '@/utils/request'

// 查询用车订单列表
export function listOrder(query) {
  return request({
    url: '/vehicle/order/list',
    method: 'get',
    params: query
  })
}

// 查询用车订单详细
export function getOrder(orderId) {
  return request({
    url: '/vehicle/order/' + orderId,
    method: 'get'
  })
}

// 新增用车订单
export function addOrder(data) {
  return request({
    url: '/vehicle/order',
    method: 'post',
    data: data
  })
}

// 修改用车订单
export function updateOrder(data) {
  return request({
    url: '/vehicle/order',
    method: 'put',
    data: data
  })
}

// 删除用车订单
export function delOrder(orderId) {
  return request({
    url: '/vehicle/order/' + orderId,
    method: 'delete'
  })
}

// 根据申请ID创建订单
export function createOrderFromApplication(applicationId) {
  return request({
    url: '/vehicle/order/create-from-application/' + applicationId,
    method: 'post'
  })
}

// 司机开始用车
export function startOrder(orderId, startPhotoUrl) {
  return request({
    url: '/vehicle/order/start/' + orderId,
    method: 'put',
    params: { startPhotoUrl: startPhotoUrl }
  })
}

// 司机结束用车
export function finishOrder(orderId, endPhotoUrl) {
  return request({
    url: '/vehicle/order/finish/' + orderId,
    method: 'put',
    params: { endPhotoUrl: endPhotoUrl }
  })
}

// 队伍确认订单
export function teamConfirmOrder(orderId) {
  return request({
    url: '/vehicle/order/team-confirm/' + orderId,
    method: 'put'
  })
}

// 调度室确认订单
export function dispatchConfirmOrder(orderId) {
  return request({
    url: '/vehicle/order/dispatch-confirm/' + orderId,
    method: 'put'
  })
}

// 主管确认订单
export function managerConfirmOrder(orderId) {
  return request({
    url: '/vehicle/order/manager-confirm/' + orderId,
    method: 'put'
  })
}

// 退回订单
export function rejectOrder(orderId, data) {
  return request({
    url: '/vehicle/order/reject/' + orderId,
    method: 'put',
    data: data
  })
}

// 根据订单状态查询订单列表
export function getOrderByStatus(orderStatus) {
  return request({
    url: '/vehicle/order/status/' + orderStatus,
    method: 'get'
  })
}

// 根据车辆ID查询订单列表
export function getOrderByVehicleId(vehicleId) {
  return request({
    url: '/vehicle/order/vehicle/' + vehicleId,
    method: 'get'
  })
}

// 根据队伍ID查询订单列表
export function getOrderByTeamId(teamId) {
  return request({
    url: '/vehicle/order/team/' + teamId,
    method: 'get'
  })
}

// 查询待确认的订单列表
export function getPendingConfirmOrders(confirmType) {
  return request({
    url: '/vehicle/order/pending-confirm/' + confirmType,
    method: 'get'
  })
}

// 批量确认订单
export function batchConfirmOrders(data, confirmType) {
  return request({
    url: '/vehicle/order/batch-confirm',
    method: 'put',
    data: data,
    params: { confirmType: confirmType }
  })
}

// 上传拍照文件
export function uploadPhoto(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/vehicle/order/upload-photo',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
