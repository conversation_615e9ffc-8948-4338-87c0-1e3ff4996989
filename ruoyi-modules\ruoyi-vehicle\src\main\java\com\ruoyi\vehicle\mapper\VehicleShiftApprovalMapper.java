package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleShiftApproval;

/**
 * 车辆调度审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleShiftApprovalMapper 
{
    /**
     * 查询车辆调度审批
     * 
     * @param approvalId 车辆调度审批主键
     * @return 车辆调度审批
     */
    public VehicleShiftApproval selectVehicleShiftApprovalByApprovalId(Long approvalId);

    /**
     * 查询车辆调度审批列表
     * 
     * @param vehicleShiftApproval 车辆调度审批
     * @return 车辆调度审批集合
     */
    public List<VehicleShiftApproval> selectVehicleShiftApprovalList(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 新增车辆调度审批
     * 
     * @param vehicleShiftApproval 车辆调度审批
     * @return 结果
     */
    public int insertVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 修改车辆调度审批
     * 
     * @param vehicleShiftApproval 车辆调度审批
     * @return 结果
     */
    public int updateVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 删除车辆调度审批
     * 
     * @param approvalId 车辆调度审批主键
     * @return 结果
     */
    public int deleteVehicleShiftApprovalByApprovalId(Long approvalId);

    /**
     * 批量删除车辆调度审批
     * 
     * @param approvalIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleShiftApprovalByApprovalIds(Long[] approvalIds);
}
