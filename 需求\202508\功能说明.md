| 机械车辆管理 | 支持对已入场的机械车辆进行维护，包含车辆类型、型号、单位名称、【车牌号码、司机姓名、司机电话、指挥姓名、指挥电话】（非必填项）、车辆入场时间、一期/二期、车辆状态（可用、故障、维护、退场、台班确认人（由调度室选择，可多选））、费用计量单位（天或小时）等信息； 违章记录：支持获取智慧工地对应车辆违章记录信息，自动关联对应车辆进行查看当前违章信息； 维修记录：支持手动录入、导入维修记录信息；（单独开个tab页） | 1、车辆类型、型号关联关系；（数字字典维护） 2、台班确认人信息在哪里维护；（台班是个角色） 3、违章记录的数据有样例不？是集成还是导入？ 4、明确维修记录的数据字段 |
| ------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 车辆需求计划 | 支持机械车辆需求计划申请，包含“机械需求计划申请（队伍负责人）-项目调度室审批-机械主管领导审批-经营审批”，审批通过后即可安排车辆入场登记，在机械车辆管理模块进行数据登记；（登记信息包含车辆类型、型号、需求单位、需求时间段、用途、队伍负责人、负责人联系方式） | 1、队伍信息怎么维护？（队伍有独立页面维护） 2、有没有较明确的组织结构，审批角色中，项目调度室、机械主管领导、计划经营部等都涉及哪些角色？（提前给组织架构、角色、人员信息） |
| 机械用车申请 | 支持队伍用车时在线发起用车申请，申请时需提供车辆类型（下拉菜单）、车辆型号（下拉菜单）、用车地点、施工作业说明、用车开始/结束时间、申请人、联系方式等信息提交审批，工程部审核通过后，项目工程部可按类型查看待调度安排数量及对类型车辆忙闲情况（车辆加上状态，状态界定1是未派出，2.已用车人结束打卡为准），支持单条或批量调度安排合适车辆，同时调度时可选择不同类型下车辆； | 1、调度的逻辑和约束条件需要再明确下，用车申请审批调度后，车辆状态是怎么样的？是否能调整或修改？（同一时间只能有一辆车） |
| 用车订单管理 | 用车申请通过的数据在项目调度室审批通过后，选择调度的车辆及司机后会生成一个用车订单； 1、司机端 （1）用车开始：司机到达用车订单要求的地点后，点击进入订单详情拍照上传提交，维护用车开始时间； （2）用车结束：用车结束后，司机点击详情页面，在用车结束字段下点击拍照上传提交，维护用车结束时间； 2、队伍负责人：司机标记用车结束后，对应队伍负责人可点击订单查看司机用车开始、结束时间等信息，存在异议时可退回司机端调整（弹出对话框，输入存在异议理由），无误后可点击确认完成操作； 3、项目调度室：用车队伍负责人确认完成后，数据流转到台班确认人进行确认，台班确认人确认后判断当前车辆类型是否超过50吨，当超过时触发下一级确认，不超过时流程完成关闭；（1按队伍，2车型区分费是否由项目承担） 4、机械主管领导：关闭订单（由项目承担才规划到这儿）； | 1、司机端                                                    |
| 消息通知     | 支持不同业务场景下触发对应通知 1、机械需求计划申请：提交后触发项目调度室、机械主管的审批，审核结果触发机械需求计划申请队伍负责人通知； 2、机械用车申请：提交后触发调度室审批，审核完成后触发调度司机、用车队伍服务人审核结果通知； 3、用车结束后，司机拍照上传提交，触发用车队伍负责人确认提醒，队伍负责人确认后触发项目调度室、机械主管领导审批确认 | 钉钉消息                                                     |
| 台班审批     | 支持项目调度室、机械主管领导在审核数据时，支持批量审核，通过、退回等操作； | 1、需确认架构是否构建流程引擎组件，本次业务流程复杂度一般，可以不用组件也能实现； |
| 车辆台班统计 | 支持统计各队伍用车情况； 1、队伍使用车辆分析：按队伍维度，统计月度用车情况，展示各分类下机械车辆数量、时间等； 2、出租单位分析：按车辆所属出租单位维度统计展示，查看出租单位各车辆使用情况； 3、作业区域统计分析：统计各个区域车辆使用情况； 4、费用单位分析：多维度查看各单位费用信息 | 1、用车订单中没有看到费用相关的数据字段，看怎么做费用分析；（针对各单位车辆使用情况） |


开发计划：
机械车辆管理
前端开发：
1. 车辆信息录入表单页面（包含车辆类型、型号、单位名称等字段）
2. 车辆信息列表展示页面
3. 车辆信息编辑页面
4. 车辆状态筛选功能
5. 违章记录展示tab页
6. 维修记录录入和展示tab页
7. 车辆信息导入功能
后端开发：
1. 车辆信息实体类设计
2. 车辆信息数据库表设计
3. 车辆信息增删改查接口
4. 车辆状态枚举设计
5. 违章记录数据接口集成
6. 维修记录实体类和接口
7. 数据字典维护接口
8. 导入/导出

车辆需求计划
前端开发：
1. 需求计划申请表单页面
2. 审批流程状态展示
3. 需求计划列表页面
4. 需求计划详情页面
5. 审批操作界面
后端开发：
1. 需求计划实体类设计
2. 需求计划数据库表设计
3. 需求计划申请接口
4. 多级审批流程实现
5. 审批状态管理
6. 队伍信息维护接口
7. 角色权限配置

机械用车申请
前端开发：
1. 用车申请表单页面
2. 车辆类型/型号下拉选择
3. 用车申请列表页面
4. 车辆忙闲状态展示
5. 调度安排界面
6. 批量调度功能
后端开发：
1. 用车申请实体类设计
2. 用车申请数据库表设计
3. 用车申请接口
4. 车辆状态管理逻辑
5. 调度逻辑实现
6. 车辆忙闲状态查询接口

用车订单管理
前端开发：
1. 司机端订单详情页面
2. 用车开始/结束拍照上传功能
3. 队伍负责人订单确认页面
4. 异议退回功能界面
5. 项目调度室确认页面
6. 机械主管领导审批页面
后端开发：
1. 用车订单实体类设计
2. 用车订单数据库表设计
3. 订单状态流转逻辑
4. 拍照上传接口
5. 确认/退回业务逻辑
6. 50吨阈值判断逻辑

消息通知
前端开发：
1. 钉钉消息展示组件
2. 消息列表页面
3. 消息详情页面
4. 消息状态标识
后端开发：
1. 消息通知实体类设计
2. 消息通知数据库表设计
3. 钉钉消息推送接口集成
4. 消息触发逻辑实现
5. 不同业务场景消息模板
6. 消息状态管理

台班审批
前端开发：
1. 批量审核界面
2. 审核操作按钮（通过/退回）
3. 审核列表展示
4. 审核详情页面
后端开发：
1. 审批实体类设计
2. 审批数据库表设计
3. 批量审核接口
4. 审批状态管理
5. 审批流程逻辑实现

车辆台班统计
前端开发：
1. 车辆使用情况统计图表展示
2. 队伍维度分析页面
3. 出租单位分析页面
4. 作业区域统计页面
5. 费用单位分析页面
6. 多维度筛选功能
后端开发：
1. 统计数据实体类设计
2. 统计数据库视图/表设计
3. 各维度统计接口
4. 费用计算逻辑
5. 数据聚合查询实现
6. 统计报表导出接口
