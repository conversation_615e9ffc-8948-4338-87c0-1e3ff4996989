<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">司机端 - 我的订单</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 订单统计 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待开始</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.runningCount }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.finishedCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.todayCount }}</div>
              <div class="stat-label">今日订单</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择状态" clearable @change="getList">
            <el-option label="待开始" value="pending"></el-option>
            <el-option label="进行中" value="running"></el-option>
            <el-option label="司机已结束" value="driver_finished"></el-option>
            <el-option label="已完成" value="completed"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用车日期">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getList">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 订单列表 -->
      <el-table v-loading="loading" :data="orderList" @row-click="handleRowClick">
        <el-table-column label="订单ID" align="center" prop="orderId" width="80" />
        <el-table-column label="车辆信息" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">
              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
        <el-table-column label="计划时间" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.plannedStartTime, '{m}-{d} {h}:{i}') }}</div>
            <div style="color: #909399; font-size: 12px;">
              至 {{ parseTime(scope.row.plannedEndTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="实际时间" align="center" width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.actualStartTime">
              开始：{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}
            </div>
            <div v-if="scope.row.actualEndTime" style="color: #909399; font-size: 12px;">
              结束：{{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}
            </div>
            <div v-if="!scope.row.actualStartTime" style="color: #E6A23C;">未开始</div>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="mini">
              {{ getStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.orderStatus === 'pending'"
              size="mini"
              type="primary"
              @click="handleStart(scope.row)"
            >开始用车</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'running'"
              size="mini"
              type="success"
              @click="handleFinish(scope.row)"
            >结束用车</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 开始用车对话框 -->
    <el-dialog title="开始用车" :visible.sync="startDialogVisible" width="500px" append-to-body>
      <el-form ref="startForm" :model="startForm" :rules="startRules" label-width="100px">
        <el-form-item label="订单信息">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="用车地点">{{ currentOrder.usageLocation }}</el-descriptions-item>
            <el-descriptions-item label="计划时间">
              {{ parseTime(currentOrder.plannedStartTime) }} 至 {{ parseTime(currentOrder.plannedEndTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        
        <el-form-item label="开始拍照" prop="startPhoto">
          <el-upload
            ref="startUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="startFileList"
            :on-success="handleStartPhotoSuccess"
            :on-remove="handleStartPhotoRemove"
            :before-upload="beforePhotoUpload"
            list-type="picture-card"
            accept="image/*">
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">请拍摄车辆和作业现场照片</div>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="startForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入开始用车备注"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="startDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitStart" :loading="startLoading">确认开始</el-button>
      </div>
    </el-dialog>

    <!-- 结束用车对话框 -->
    <el-dialog title="结束用车" :visible.sync="finishDialogVisible" width="500px" append-to-body>
      <el-form ref="finishForm" :model="finishForm" :rules="finishRules" label-width="100px">
        <el-form-item label="用车时长">
          <div class="time-info">
            <div>开始时间：{{ parseTime(currentOrder.actualStartTime) }}</div>
            <div>当前时间：{{ parseTime(new Date()) }}</div>
            <div style="color: #409EFF; font-weight: bold;">
              用车时长：{{ calculateDuration(currentOrder.actualStartTime, new Date()) }}
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="结束拍照" prop="endPhoto">
          <el-upload
            ref="finishUpload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="finishFileList"
            :on-success="handleFinishPhotoSuccess"
            :on-remove="handleFinishPhotoRemove"
            :before-upload="beforePhotoUpload"
            list-type="picture-card"
            accept="image/*">
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">请拍摄车辆和作业完成照片</div>
        </el-form-item>
        
        <el-form-item label="工作总结">
          <el-input
            v-model="finishForm.workSummary"
            type="textarea"
            :rows="4"
            placeholder="请简要总结本次用车工作内容"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="finishDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFinish" :loading="finishLoading">确认结束</el-button>
      </div>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailDialogVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ detailOrder.orderId }}</el-descriptions-item>
        <el-descriptions-item label="车辆信息">
          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="用车地点" :span="2">{{ detailOrder.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="计划开始时间">{{ parseTime(detailOrder.plannedStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="计划结束时间">{{ parseTime(detailOrder.plannedEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际开始时间">
          {{ detailOrder.actualStartTime ? parseTime(detailOrder.actualStartTime) : '未开始' }}
        </el-descriptions-item>
        <el-descriptions-item label="实际结束时间">
          {{ detailOrder.actualEndTime ? parseTime(detailOrder.actualEndTime) : '未结束' }}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusTagType(detailOrder.orderStatus)">
            {{ getStatusText(detailOrder.orderStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="队伍信息">
          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 照片展示 -->
      <div v-if="detailOrder.startPhotoUrl || detailOrder.endPhotoUrl" style="margin-top: 20px;">
        <h4>作业照片</h4>
        <el-row :gutter="20">
          <el-col :span="12" v-if="detailOrder.startPhotoUrl">
            <div class="photo-section">
              <h5>开始照片</h5>
              <el-image
                :src="detailOrder.startPhotoUrl"
                :preview-src-list="[detailOrder.startPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="12" v-if="detailOrder.endPhotoUrl">
            <div class="photo-section">
              <h5>结束照片</h5>
              <el-image
                :src="detailOrder.endPhotoUrl"
                :preview-src-list="[detailOrder.endPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, startOrder, finishOrder } from "@/api/vehicle/order";
import { getToken } from "@/utils/auth";

export default {
  name: "DriverOrder",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 订单列表
      orderList: [],
      // 统计信息
      statistics: {
        pendingCount: 0,
        runningCount: 0,
        finishedCount: 0,
        todayCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderStatus: null,
        driverName: this.$store.state.user.name // 当前登录司机
      },
      // 日期范围
      dateRange: [],
      // 当前订单
      currentOrder: {},
      detailOrder: {},
      // 开始用车对话框
      startDialogVisible: false,
      startLoading: false,
      startForm: {
        startPhotoUrl: '',
        remark: ''
      },
      startFileList: [],
      // 结束用车对话框
      finishDialogVisible: false,
      finishLoading: false,
      finishForm: {
        endPhotoUrl: '',
        workSummary: ''
      },
      finishFileList: [],
      // 详情对话框
      detailDialogVisible: false,
      // 上传配置
      uploadUrl: process.env.VUE_APP_BASE_API + "/vehicle/order/upload-photo",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      // 表单验证规则
      startRules: {
        startPhoto: [
          { required: true, message: "请上传开始照片", trigger: "change" }
        ]
      },
      finishRules: {
        endPhoto: [
          { required: true, message: "请上传结束照片", trigger: "change" }
        ],
        workSummary: [
          { required: true, message: "请填写工作总结", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.params["beginPlannedStartTime"] = this.dateRange[0];
        this.queryParams.params["endPlannedStartTime"] = this.dateRange[1];
      }
      
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 加载统计信息 */
    loadStatistics() {
      // TODO: 调用统计接口
      this.statistics = {
        pendingCount: 3,
        runningCount: 1,
        finishedCount: 8,
        todayCount: 2
      };
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新列表 */
    refreshList() {
      this.getList();
      this.loadStatistics();
      this.$modal.msgSuccess("刷新成功");
    },
    
    /** 行点击事件 */
    handleRowClick(row) {
      this.handleView(row);
    },
    
    /** 开始用车 */
    handleStart(row) {
      this.currentOrder = row;
      this.startDialogVisible = true;
      this.startForm = {
        startPhotoUrl: '',
        remark: ''
      };
      this.startFileList = [];
    },
    
    /** 结束用车 */
    handleFinish(row) {
      this.currentOrder = row;
      this.finishDialogVisible = true;
      this.finishForm = {
        endPhotoUrl: '',
        workSummary: ''
      };
      this.finishFileList = [];
    },
    
    /** 查看详情 */
    handleView(row) {
      getOrder(row.orderId).then(response => {
        this.detailOrder = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 提交开始用车 */
    submitStart() {
      this.$refs["startForm"].validate(valid => {
        if (valid) {
          this.startLoading = true;
          startOrder(this.currentOrder.orderId, this.startForm.startPhotoUrl).then(response => {
            this.$modal.msgSuccess("开始用车成功");
            this.startDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.startLoading = false;
          });
        }
      });
    },
    
    /** 提交结束用车 */
    submitFinish() {
      this.$refs["finishForm"].validate(valid => {
        if (valid) {
          this.finishLoading = true;
          finishOrder(this.currentOrder.orderId, this.finishForm.endPhotoUrl).then(response => {
            this.$modal.msgSuccess("结束用车成功，等待队伍确认");
            this.finishDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.finishLoading = false;
          });
        }
      });
    },
    
    /** 开始照片上传成功 */
    handleStartPhotoSuccess(response, file) {
      this.startForm.startPhotoUrl = response.url;
    },
    
    /** 开始照片移除 */
    handleStartPhotoRemove() {
      this.startForm.startPhotoUrl = '';
    },
    
    /** 结束照片上传成功 */
    handleFinishPhotoSuccess(response, file) {
      this.finishForm.endPhotoUrl = response.url;
    },
    
    /** 结束照片移除 */
    handleFinishPhotoRemove() {
      this.finishForm.endPhotoUrl = '';
    },
    
    /** 上传前检查 */
    beforePhotoUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt5M = file.size / 1024 / 1024 < 5;
      
      if (!isImage) {
        this.$modal.msgError('只能上传图片文件!');
        return false;
      }
      if (!isLt5M) {
        this.$modal.msgError('上传图片大小不能超过 5MB!');
        return false;
      }
      return true;
    },
    
    /** 计算用车时长 */
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '0小时';
      
      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      return `${hours}小时${minutes}分钟`;
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'pending': 'warning',
        'running': 'primary',
        'driver_finished': 'success',
        'team_confirmed': 'success',
        'dispatch_confirmed': 'success',
        'completed': 'success'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'pending': '待开始',
        'running': '进行中',
        'driver_finished': '司机已结束',
        'team_confirmed': '队伍已确认',
        'dispatch_confirmed': '调度已确认',
        'completed': '已完成'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.mb20 {
  margin-bottom: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.time-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.8;
}

.photo-section {
  text-align: center;
}

.photo-section h5 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
