-- 为用车订单表添加费用管理相关字段

-- 添加费用相关字段
ALTER TABLE `vehicle_order` 
ADD COLUMN `cost_unit` varchar(10) DEFAULT 'hour' COMMENT '费用计量单位（hour/day）' AFTER `vehicle_weight`,
ADD COLUMN `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价' AFTER `cost_unit`,
ADD COLUMN `actual_duration` decimal(10,2) DEFAULT NULL COMMENT '实际用车时长' AFTER `unit_price`,
ADD COLUMN `total_cost` decimal(10,2) DEFAULT NULL COMMENT '总费用' AFTER `actual_duration`,
ADD COLUMN `cost_bearer` varchar(50) DEFAULT NULL COMMENT '费用承担方' AFTER `total_cost`,
ADD COLUMN `cost_status` varchar(20) DEFAULT 'pending' COMMENT '费用状态（pending/calculated/confirmed）' AFTER `cost_bearer`,
ADD COLUMN `cost_calculate_time` datetime DEFAULT NULL COMMENT '费用计算时间' AFTER `cost_status`,
ADD COLUMN `cost_confirm_time` datetime DEFAULT NULL COMMENT '费用确认时间' AFTER `cost_calculate_time`,
ADD COLUMN `cost_confirm_person` varchar(50) DEFAULT NULL COMMENT '费用确认人' AFTER `cost_confirm_time`;

-- 添加索引
CREATE INDEX `idx_vehicle_order_cost_status` ON `vehicle_order` (`cost_status`);
CREATE INDEX `idx_vehicle_order_cost_bearer` ON `vehicle_order` (`cost_bearer`);

-- 更新现有数据的默认值
UPDATE `vehicle_order` SET `cost_unit` = 'hour', `cost_status` = 'pending' WHERE `cost_unit` IS NULL;

-- 添加费用统计视图
CREATE VIEW `v_vehicle_cost_statistics` AS
SELECT 
    vo.team_id,
    ti.team_name,
    vo.vehicle_id,
    vi.vehicle_type,
    vi.vehicle_model,
    vo.cost_bearer,
    COUNT(*) as order_count,
    SUM(vo.actual_duration) as total_duration,
    SUM(vo.total_cost) as total_cost,
    AVG(vo.unit_price) as avg_unit_price,
    DATE_FORMAT(vo.actual_start_time, '%Y-%m') as month_year
FROM vehicle_order vo
LEFT JOIN team_info ti ON vo.team_id = ti.team_id
LEFT JOIN vehicle_info vi ON vo.vehicle_id = vi.vehicle_id
WHERE vo.order_status = 'completed' 
  AND vo.cost_status = 'confirmed'
  AND vo.total_cost IS NOT NULL
GROUP BY vo.team_id, vo.vehicle_id, vo.cost_bearer, DATE_FORMAT(vo.actual_start_time, '%Y-%m');

-- 创建费用计算存储过程
DELIMITER $$

CREATE PROCEDURE `CalculateOrderCost`(IN order_id BIGINT)
BEGIN
    DECLARE v_start_time DATETIME;
    DECLARE v_end_time DATETIME;
    DECLARE v_vehicle_id BIGINT;
    DECLARE v_cost_unit VARCHAR(10);
    DECLARE v_unit_price DECIMAL(10,2);
    DECLARE v_duration DECIMAL(10,2);
    DECLARE v_total_cost DECIMAL(10,2);
    DECLARE v_vehicle_weight DECIMAL(10,2);
    DECLARE v_cost_bearer VARCHAR(50);
    
    -- 获取订单信息
    SELECT actual_start_time, actual_end_time, vehicle_id, cost_unit
    INTO v_start_time, v_end_time, v_vehicle_id, v_cost_unit
    FROM vehicle_order 
    WHERE order_id = order_id;
    
    -- 获取车辆信息
    SELECT vehicle_weight, cost_per_hour
    INTO v_vehicle_weight, v_unit_price
    FROM vehicle_info 
    WHERE vehicle_id = v_vehicle_id;
    
    -- 计算时长
    IF v_cost_unit = 'hour' THEN
        SET v_duration = TIMESTAMPDIFF(MINUTE, v_start_time, v_end_time) / 60.0;
    ELSE
        SET v_duration = TIMESTAMPDIFF(DAY, v_start_time, v_end_time);
        IF v_duration = 0 THEN
            SET v_duration = 1; -- 不足一天按一天计算
        END IF;
    END IF;
    
    -- 计算总费用
    SET v_total_cost = v_duration * v_unit_price;
    
    -- 确定费用承担方（超过50吨由项目承担）
    IF v_vehicle_weight > 50 THEN
        SET v_cost_bearer = '项目承担';
    ELSE
        SET v_cost_bearer = '队伍承担';
    END IF;
    
    -- 更新订单费用信息
    UPDATE vehicle_order 
    SET 
        unit_price = v_unit_price,
        actual_duration = v_duration,
        total_cost = v_total_cost,
        cost_bearer = v_cost_bearer,
        cost_status = 'calculated',
        cost_calculate_time = NOW()
    WHERE order_id = order_id;
    
END$$

DELIMITER ;

-- 创建费用确认存储过程
DELIMITER $$

CREATE PROCEDURE `ConfirmOrderCost`(IN order_id BIGINT, IN confirm_person VARCHAR(50))
BEGIN
    UPDATE vehicle_order 
    SET 
        cost_status = 'confirmed',
        cost_confirm_time = NOW(),
        cost_confirm_person = confirm_person
    WHERE order_id = order_id AND cost_status = 'calculated';
END$$

DELIMITER ;
