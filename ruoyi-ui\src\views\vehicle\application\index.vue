<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请标题" prop="applicationTitle">
        <el-input
          v-model="queryParams.applicationTitle"
          placeholder="请输入申请标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆类型" prop="vehicleType">
        <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
          <el-option label="待审批" value="pending"></el-option>
          <el-option label="已审批" value="approved"></el-option>
          <el-option label="已拒绝" value="rejected"></el-option>
          <el-option label="已调度" value="dispatched"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vehicle:application:add']"
        >新增申请</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vehicle:application:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vehicle:application:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-operation"
          size="mini"
          @click="handleDispatch"
        >调度安排</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:application:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="applicationId" width="80" />
      <el-table-column label="申请标题" align="center" prop="applicationTitle" :show-overflow-tooltip="true" />
      <el-table-column label="车辆需求" align="center" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.vehicleType }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.vehicleModel }}</div>
        </template>
      </el-table-column>
      <el-table-column label="用车时间" align="center" width="180">
        <template slot-scope="scope">
          <div>{{ parseTime(scope.row.startTime, '{m}-{d} {h}:{i}') }}</div>
          <div style="color: #909399; font-size: 12px;">
            至 {{ parseTime(scope.row.endTime, '{m}-{d} {h}:{i}') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" width="100" />
      <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
      <el-table-column label="审批状态" align="center" prop="approvalStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.approvalStatus)" size="mini">
            {{ getStatusText(scope.row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分配情况" align="center" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.assignedVehicleId">
            <div style="color: #67C23A;">已分配车辆</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.assignedDriver }}</div>
          </div>
          <div v-else style="color: #E6A23C;">待分配</div>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:application:edit']"
            v-if="scope.row.approvalStatus === 'pending'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:application:remove']"
            v-if="scope.row.approvalStatus === 'pending'"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-operation"
            @click="handleDispatchSingle(scope.row)"
            v-hasPermi="['vehicle:application:dispatch']"
            v-if="scope.row.approvalStatus === 'approved' && !scope.row.assignedVehicleId"
          >调度</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="detailDialogVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请ID">{{ detailApplication.applicationId }}</el-descriptions-item>
        <el-descriptions-item label="申请标题">{{ detailApplication.applicationTitle }}</el-descriptions-item>
        <el-descriptions-item label="车辆类型">{{ detailApplication.vehicleType }}</el-descriptions-item>
        <el-descriptions-item label="车辆型号">{{ detailApplication.vehicleModel }}</el-descriptions-item>
        <el-descriptions-item label="用车地点" :span="2">{{ detailApplication.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="施工作业说明" :span="2">{{ detailApplication.workDescription }}</el-descriptions-item>
        <el-descriptions-item label="用车开始时间">{{ parseTime(detailApplication.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="用车结束时间">{{ parseTime(detailApplication.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ detailApplication.applicant }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ detailApplication.applicantPhone }}</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getStatusTagType(detailApplication.approvalStatus)">
            {{ getStatusText(detailApplication.approvalStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(detailApplication.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="分配车辆" v-if="detailApplication.assignedVehicleId">
          {{ detailApplication.assignedVehicleId }}
        </el-descriptions-item>
        <el-descriptions-item label="分配司机" v-if="detailApplication.assignedDriver">
          {{ detailApplication.assignedDriver }}
        </el-descriptions-item>
        <el-descriptions-item label="调度时间" v-if="detailApplication.dispatchTime">
          {{ parseTime(detailApplication.dispatchTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="调度人员" v-if="detailApplication.dispatchPerson">
          {{ detailApplication.dispatchPerson }}
        </el-descriptions-item>
        <el-descriptions-item label="调度备注" v-if="detailApplication.dispatchRemark" :span="2">
          {{ detailApplication.dispatchRemark }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" v-if="detailApplication.remark" :span="2">
          {{ detailApplication.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listApplication, getApplication, delApplication, exportApplication } from "@/api/vehicle/application";

export default {
  name: "Application",
  dicts: ['vehicle_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用车申请表格数据
      applicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情对话框
      detailDialogVisible: false,
      detailApplication: {},
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationTitle: null,
        vehicleType: null,
        approvalStatus: null,
        applicant: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用车申请列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.params["beginStartTime"] = this.dateRange[0];
        this.queryParams.params["endStartTime"] = this.dateRange[1];
      }
      listApplication(this.queryParams).then(response => {
        this.applicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/vehicle/application/apply');
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      const applicationId = row.applicationId || this.ids
      this.$router.push('/vehicle/application/edit/' + applicationId);
    },
    
    /** 查看详情 */
    handleView(row) {
      getApplication(row.applicationId).then(response => {
        this.detailApplication = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const applicationIds = row.applicationId || this.ids;
      this.$modal.confirm('是否确认删除用车申请编号为"' + applicationIds + '"的数据项？').then(function() {
        return delApplication(applicationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    
    /** 调度安排 */
    handleDispatch() {
      this.$router.push('/vehicle/application/dispatch');
    },
    
    /** 单个调度 */
    handleDispatchSingle(row) {
      this.$router.push('/vehicle/application/dispatch?applicationId=' + row.applicationId);
    },
    
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/application/export', {
        ...this.queryParams
      }, `application_${new Date().getTime()}.xlsx`)
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'dispatched': 'info'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已审批',
        'rejected': '已拒绝',
        'dispatched': '已调度'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>
