import request from '@/utils/request'

// 查询用车申请列表
export function listApplication(query) {
  return request({
    url: '/vehicle/application/list',
    method: 'get',
    params: query
  })
}

// 查询用车申请详细
export function getApplication(applicationId) {
  return request({
    url: '/vehicle/application/' + applicationId,
    method: 'get'
  })
}

// 新增用车申请
export function addApplication(data) {
  return request({
    url: '/vehicle/application',
    method: 'post',
    data: data
  })
}

// 修改用车申请
export function updateApplication(data) {
  return request({
    url: '/vehicle/application',
    method: 'put',
    data: data
  })
}

// 删除用车申请
export function delApplication(applicationId) {
  return request({
    url: '/vehicle/application/' + applicationId,
    method: 'delete'
  })
}

// 导出用车申请
export function exportApplication(query) {
  return request({
    url: '/vehicle/application/export',
    method: 'get',
    params: query
  })
}

// 审批用车申请
export function approveApplication(data) {
  return request({
    url: '/vehicle/application/approve',
    method: 'put',
    data: data
  })
}

// 拒绝用车申请
export function rejectApplication(data) {
  return request({
    url: '/vehicle/application/reject',
    method: 'put',
    data: data
  })
}

// 分配车辆
export function assignVehicle(data) {
  return request({
    url: '/vehicle/application/assign',
    method: 'put',
    data: data
  })
}

// 调度确认
export function dispatchConfirm(data) {
  return request({
    url: '/vehicle/application/dispatch-confirm',
    method: 'put',
    data: data
  })
}

// 根据申请状态查询申请列表
export function getApplicationsByStatus(status) {
  return request({
    url: '/vehicle/application/status/' + status,
    method: 'get'
  })
}

// 根据申请人查询申请列表
export function getApplicationsByApplicant(applicant) {
  return request({
    url: '/vehicle/application/applicant/' + applicant,
    method: 'get'
  })
}

// 根据队伍ID查询申请列表
export function getApplicationsByTeam(teamId) {
  return request({
    url: '/vehicle/application/team/' + teamId,
    method: 'get'
  })
}

// 查询待审批的申请列表
export function getPendingApplications() {
  return request({
    url: '/vehicle/application/pending',
    method: 'get'
  })
}

// 查询已审批的申请列表
export function getApprovedApplications() {
  return request({
    url: '/vehicle/application/approved',
    method: 'get'
  })
}

// 查询已调度的申请列表
export function getDispatchedApplications() {
  return request({
    url: '/vehicle/application/dispatched',
    method: 'get'
  })
}

// 批量审批申请
export function batchApprove(applicationIds) {
  return request({
    url: '/vehicle/application/batch-approve',
    method: 'put',
    data: {
      applicationIds: applicationIds
    }
  })
}

// 批量拒绝申请
export function batchReject(applicationIds, rejectReason) {
  return request({
    url: '/vehicle/application/batch-reject',
    method: 'put',
    data: {
      applicationIds: applicationIds,
      rejectReason: rejectReason
    }
  })
}

// 获取申请统计信息
export function getApplicationStats() {
  return request({
    url: '/vehicle/application/stats',
    method: 'get'
  })
}

// 根据车辆类型查询申请列表
export function getApplicationsByVehicleType(vehicleType) {
  return request({
    url: '/vehicle/application/vehicle-type/' + vehicleType,
    method: 'get'
  })
}

// 查询紧急申请列表
export function getUrgentApplications() {
  return request({
    url: '/vehicle/application/urgent',
    method: 'get'
  })
}

// 撤销申请
export function cancelApplication(applicationId, reason) {
  return request({
    url: '/vehicle/application/cancel/' + applicationId,
    method: 'put',
    data: {
      reason: reason
    }
  })
}

// 重新提交申请
export function resubmitApplication(applicationId, data) {
  return request({
    url: '/vehicle/application/resubmit/' + applicationId,
    method: 'put',
    data: data
  })
}

// 提交申请
export function submitApplication(data) {
  return request({
    url: '/vehicle/application/submit',
    method: 'post',
    data: data
  })
}

// 获取可用车辆列表
export function getAvailableVehicles(query) {
  return request({
    url: '/vehicle/application/available-vehicles',
    method: 'get',
    params: query
  })
}

// 批量分配车辆
export function batchAssignVehicle(data) {
  return request({
    url: '/vehicle/application/batch-assign',
    method: 'put',
    data: data
  })
}
