package com.ruoyi.vehicle.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 车辆台班统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleStatisticsService 
{
    /**
     * 获取车辆总体统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getVehicleOverallStatistics();

    /**
     * 按车辆类型统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatisticsByVehicleType(Date startDate, Date endDate);

    /**
     * 按队伍统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatisticsByTeam(Date startDate, Date endDate);

    /**
     * 按时间段统计（按天）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatisticsByDay(Date startDate, Date endDate);

    /**
     * 按时间段统计（按月）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getStatisticsByMonth(Date startDate, Date endDate);

    /**
     * 车辆利用率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getVehicleUtilizationStatistics(Date startDate, Date endDate);

    /**
     * 台班工作量统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param vehicleType 车辆类型（可选）
     * @param teamId 队伍ID（可选）
     * @return 统计结果
     */
    public Map<String, Object> getShiftWorkloadStatistics(Date startDate, Date endDate, String vehicleType, Long teamId);

    /**
     * 违章记录统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public Map<String, Object> getViolationStatistics(Date startDate, Date endDate);

    /**
     * 维修记录统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public Map<String, Object> getMaintenanceStatistics(Date startDate, Date endDate);

    /**
     * 订单状态统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public Map<String, Object> getOrderStatusStatistics(Date startDate, Date endDate);

    /**
     * 审批效率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public Map<String, Object> getApprovalEfficiencyStatistics(Date startDate, Date endDate);

    /**
     * 车辆排行榜统计（按使用频次）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 返回数量限制
     * @return 统计结果
     */
    public List<Map<String, Object>> getVehicleRankingByUsage(Date startDate, Date endDate, Integer limit);

    /**
     * 队伍排行榜统计（按用车次数）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 返回数量限制
     * @return 统计结果
     */
    public List<Map<String, Object>> getTeamRankingByUsage(Date startDate, Date endDate, Integer limit);

    /**
     * 司机工作量统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    public List<Map<String, Object>> getDriverWorkloadStatistics(Date startDate, Date endDate);

    /**
     * 生成统计报表数据
     * 
     * @param reportType 报表类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param params 其他参数
     * @return 报表数据
     */
    public Map<String, Object> generateStatisticsReport(String reportType, Date startDate, Date endDate, Map<String, Object> params);

    /**
     * 导出统计数据
     * 
     * @param reportType 报表类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param params 其他参数
     * @return 导出数据
     */
    public List<Map<String, Object>> exportStatisticsData(String reportType, Date startDate, Date endDate, Map<String, Object> params);

    /**
     * 获取实时统计数据（用于Dashboard）
     * 
     * @return 实时统计数据
     */
    public Map<String, Object> getRealTimeStatistics();

    /**
     * 预警统计（超时、异常等）
     * 
     * @return 预警统计数据
     */
    public Map<String, Object> getWarningStatistics();
}
