#!/bin/bash

# =============================================================================
# RuoYi-Cloud 应用一键部署脚本
# 用于部署RuoYi-Cloud应用服务，包含前端构建、后端服务部署等功能
# =============================================================================

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 加载配置文件
source "${SCRIPT_DIR}/config.sh"

# 必需的构建命令列表
BUILD_COMMANDS=(
    "mvn"
    "npm"
    "node"
    "java"
    "docker"
    "docker-compose"
)

# 函数：显示帮助信息
show_help() {
    echo "RuoYi-Cloud 应用一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -c, --check             仅检查构建环境"
    echo "  -b, --build-only        仅构建应用，不部署"
    echo "  -d, --deploy-only       仅部署应用，不构建"
    echo "  -f, --force             强制重新构建和部署"
    echo "  --skip-frontend         跳过前端构建"
    echo "  --skip-backend          跳过后端构建"
    echo "  --skip-sql              跳过SQL脚本复制"
    echo "  --database=TYPE         指定数据库类型 (mysql|dameng)"
    echo ""
    echo "示例:"
    echo "  $0                      完整构建和部署"
    echo "  $0 -c                   检查构建环境"
    echo "  $0 -b                   仅构建应用"
    echo "  $0 -d                   仅部署应用"
    echo "  $0 --database=dameng    使用达梦数据库部署"
    echo "  $0 --database=mysql     使用MySQL数据库部署"
    echo ""
}

# 函数：自动安装构建依赖
auto_install_build_dependencies() {
    local commands=("$@")

    log_info "开始自动安装构建依赖..."

    for cmd in "${commands[@]}"; do
        log_info "正在安装: $cmd"

        case "$cmd" in
            "mvn")
                install_maven
                ;;
            "npm"|"node")
                install_nodejs
                ;;
            "java")
                install_java
                ;;
            "docker"|"docker-compose")
                log_info "$cmd 应该已在中间件安装时安装，跳过"
                ;;
            *)
                log_warn "不知道如何安装构建命令: $cmd"
                ;;
        esac
    done

    log_info "构建依赖安装完成"
}

# 函数：显示安装进度
show_progress() {
    local chars="/-\|"
    local i=0
    while true; do
        printf "\b${chars:$i:1}"
        i=$(( (i+1) % 4 ))
        sleep 0.5
    done
}

# 函数：安装Maven
install_maven() {
    log_info "安装Maven..."

    local maven_version="3.8.6"
    local maven_dir="/opt/apache-maven-${maven_version}"

    # 检查Maven是否已经安装并可用
    if command -v mvn >/dev/null 2>&1; then
        log_info "Maven已安装并可用，跳过安装步骤"
        return 0
    fi

    # 检查是否已经下载但未配置环境变量
    if [[ -d "$maven_dir" ]]; then
        log_info "Maven已存在，配置环境变量..."
        if ! grep -q "apache-maven" ~/.bashrc; then
            echo "export PATH=${maven_dir}/bin:\$PATH" >> ~/.bashrc
        fi
        export PATH="${maven_dir}/bin:$PATH"
        return 0
    fi

    # 下载并安装Maven
    local maven_url="https://archive.apache.org/dist/maven/maven-3/${maven_version}/binaries/apache-maven-${maven_version}-bin.tar.gz"
    local maven_url_cn="https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/${maven_version}/binaries/apache-maven-${maven_version}-bin.tar.gz"

    cd /tmp

    # 尝试从清华镜像下载
    echo -n "  从清华镜像下载Maven ... "
    show_progress &
    local progress_pid=$!

    if timeout 120 wget "$maven_url_cn" -O "apache-maven-${maven_version}-bin.tar.gz" >/dev/null 2>&1; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"
    else
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${RED}✗ 失败${NC}"

        # 尝试从官方源下载
        echo -n "  从官方源下载Maven ... "
        show_progress &
        progress_pid=$!

        if timeout 180 wget "$maven_url" -O "apache-maven-${maven_version}-bin.tar.gz" >/dev/null 2>&1; then
            kill $progress_pid 2>/dev/null
            wait $progress_pid 2>/dev/null
            echo -e " ${GREEN}✓ 成功${NC}"
        else
            kill $progress_pid 2>/dev/null
            wait $progress_pid 2>/dev/null
            echo -e " ${RED}✗ 失败${NC}"
            log_error "Maven下载失败"
            return 1
        fi
    fi

    # 解压安装
    echo -n "  解压安装Maven ... "
    if tar -xzf "apache-maven-${maven_version}-bin.tar.gz" -C /opt/ >/dev/null 2>&1; then
        echo -e " ${GREEN}✓ 成功${NC}"

        # 配置环境变量
        if ! grep -q "apache-maven" ~/.bashrc; then
            echo "export PATH=${maven_dir}/bin:\$PATH" >> ~/.bashrc
        fi
        export PATH="${maven_dir}/bin:$PATH"

        # 清理下载文件
        rm -f "apache-maven-${maven_version}-bin.tar.gz"

        log_info "Maven安装成功"
        return 0
    else
        echo -e " ${RED}✗ 失败${NC}"
        log_error "Maven安装失败"
        return 1
    fi
}

# 函数：安装Node.js
install_nodejs() {
    # 检查Node.js是否已经安装
    if command -v node >/dev/null 2>&1 && command -v npm >/dev/null 2>&1; then
        log_info "Node.js和npm已安装，跳过安装步骤"
        return 0
    fi

    log_info "安装Node.js..."

    # 方法1: 使用EPEL仓库（速度较快）
    echo -n "  使用EPEL仓库安装 ... "
    show_progress &
    local progress_pid=$!

    if timeout 120 bash -c 'yum install -y epel-release >/dev/null 2>&1 && yum install -y nodejs npm >/dev/null 2>&1'; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"
        log_info "Node.js安装成功（EPEL）"
        return 0
    fi

    kill $progress_pid 2>/dev/null
    wait $progress_pid 2>/dev/null
    echo -e " ${RED}✗ 失败${NC}"

    # 方法2: 使用NodeSource仓库
    echo -n "  使用NodeSource仓库安装 ... "
    show_progress &
    progress_pid=$!

    if timeout 180 bash -c 'curl -fsSL https://rpm.nodesource.com/setup_16.x | bash - >/dev/null 2>&1 && yum install -y nodejs >/dev/null 2>&1'; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"
        log_info "Node.js安装成功（NodeSource）"
        return 0
    fi

    kill $progress_pid 2>/dev/null
    wait $progress_pid 2>/dev/null
    echo -e " ${RED}✗ 失败${NC}"

    # 方法3: 使用二进制包
    echo -n "  下载二进制包安装 ... "
    show_progress &
    progress_pid=$!

    local node_version="16.20.0"
    local node_url="https://nodejs.org/dist/v${node_version}/node-v${node_version}-linux-x64.tar.xz"
    local node_dir="/opt/node-v${node_version}-linux-x64"

    cd /tmp
    if timeout 180 wget "$node_url" >/dev/null 2>&1; then
        if tar -xf "node-v${node_version}-linux-x64.tar.xz" -C /opt/ >/dev/null 2>&1; then
            # 创建软链接
            ln -sf "${node_dir}/bin/node" /usr/local/bin/node
            ln -sf "${node_dir}/bin/npm" /usr/local/bin/npm

            # 清理下载文件
            rm -f "node-v${node_version}-linux-x64.tar.xz"

            kill $progress_pid 2>/dev/null
            wait $progress_pid 2>/dev/null
            echo -e " ${GREEN}✓ 成功${NC}"
            log_info "Node.js安装成功（二进制包）"
            return 0
        fi
    fi

    kill $progress_pid 2>/dev/null
    wait $progress_pid 2>/dev/null
    echo -e " ${RED}✗ 失败${NC}"

    log_error "Node.js安装失败，所有方法都尝试过了"
    return 1
}

# 函数：安装Java
install_java() {
    # 检查Java是否已经安装
    if command -v java >/dev/null 2>&1 && command -v javac >/dev/null 2>&1; then
        log_info "Java已安装，跳过安装步骤"
        return 0
    fi

    log_info "安装Java..."

    echo -n "  安装OpenJDK 8 ... "
    show_progress &
    local progress_pid=$!

    # 安装OpenJDK 8
    if timeout 120 yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel >/dev/null 2>&1; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"

        # 设置JAVA_HOME
        local java_home="/usr/lib/jvm/java-1.8.0-openjdk"
        if [[ -d "$java_home" ]]; then
            if ! grep -q "JAVA_HOME" ~/.bashrc; then
                echo "export JAVA_HOME=$java_home" >> ~/.bashrc
                echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> ~/.bashrc
            fi
            export JAVA_HOME="$java_home"
            export PATH="$JAVA_HOME/bin:$PATH"
        fi

        log_info "Java安装成功"
        return 0
    else
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${RED}✗ 失败${NC}"
        log_error "Java安装失败"
        return 1
    fi
}

# 函数：检查构建环境
check_build_environment() {
    log_info "检查构建环境..."
    
    # 检查并自动安装构建命令
    local missing_commands=()
    for cmd in "${BUILD_COMMANDS[@]}"; do
        if ! check_command "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_warn "检测到以下构建命令未安装，将尝试自动安装:"
        for cmd in "${missing_commands[@]}"; do
            echo "  - $cmd"
        done
        echo ""

        # 尝试自动安装缺失的命令
        auto_install_build_dependencies "${missing_commands[@]}"

        # 重新检查命令是否安装成功
        local still_missing=()
        for cmd in "${missing_commands[@]}"; do
            if ! check_command "$cmd"; then
                still_missing+=("$cmd")
            fi
        done

        if [[ ${#still_missing[@]} -gt 0 ]]; then
            log_error "以下构建命令自动安装失败，请手动安装:"
            for cmd in "${still_missing[@]}"; do
                echo "  - $cmd"
            done
            echo ""
            echo "手动安装建议:"
            echo "  # 安装Maven"
            echo "  wget https://archive.apache.org/dist/maven/maven-3/3.8.6/binaries/apache-maven-3.8.6-bin.tar.gz"
            echo "  tar -xzf apache-maven-3.8.6-bin.tar.gz -C /opt/"
            echo "  echo 'export PATH=/opt/apache-maven-3.8.6/bin:\$PATH' >> ~/.bashrc"
            echo ""
            echo "  # 安装Node.js"
            echo "  curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -"
            echo "  yum install -y nodejs"
            echo ""
            echo "  # 安装Java"
            echo "  yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel"
            exit 1
        else
            log_info "所有构建依赖安装成功"
        fi
    fi
    
    # 检查Java版本
    local java_version=$(java -version 2>&1 | head -1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    if [[ "$java_version" != "1.8" ]]; then
        log_warn "建议使用Java 1.8，当前版本: $java_version"
    else
        log_info "Java版本检查通过: $java_version"
    fi
    
    # 检查Maven版本
    local maven_version=$(mvn -version 2>/dev/null | head -1 | awk '{print $3}')
    log_info "Maven版本: $maven_version"
    
    # 检查Node.js版本
    local node_version=$(node --version 2>/dev/null)
    log_info "Node.js版本: $node_version"
    
    # 检查npm版本
    local npm_version=$(npm --version 2>/dev/null)
    log_info "npm版本: $npm_version"
    
    # 检查Docker服务
    check_docker_status || exit 1
    
    # 检查中间件是否运行
    log_info "检查中间件服务状态..."

    local middleware_services=("ruoyi-redis" "ruoyi-nacos")
    for service in "${middleware_services[@]}"; do
        if ! docker ps | grep -q "$service"; then
            log_error "中间件服务 $service 未运行，请先执行中间件部署脚本"
            echo "  ./install-middleware.sh"
            exit 1
        else
            log_info "中间件服务 $service 运行正常"
        fi
    done

    # 检查数据库服务
    local mysql_running=$(docker ps | grep -c "ruoyi-mysql" || echo "0")
    local dameng_running=$(docker ps | grep -c "ruoyi-dameng" || echo "0")

    if [[ "$mysql_running" == "0" && "$dameng_running" == "0" ]]; then
        log_error "没有数据库服务运行，请先执行中间件部署脚本"
        echo "  ./install-middleware.sh"
        exit 1
    fi

    log_info "发现可用的数据库服务:"
    if [[ "$mysql_running" != "0" ]]; then
        log_info "  - MySQL 数据库 (ruoyi-mysql)"
    fi
    if [[ "$dameng_running" != "0" ]]; then
        log_info "  - 达梦数据库 (ruoyi-dameng)"
    fi
    
    log_info "构建环境检查完成"
}

# 函数：选择数据库类型
select_database_type() {
    local selected_db=""

    # 如果通过参数指定了数据库类型，直接使用
    if [[ -n "$DATABASE_TYPE_PARAM" ]]; then
        selected_db="$DATABASE_TYPE_PARAM"
        log_info "通过参数指定数据库类型: $selected_db"
    else
        # 检查可用的数据库
        local mysql_running=$(docker ps | grep -c "ruoyi-mysql" || echo "0")
        local dameng_running=$(docker ps | grep -c "ruoyi-dameng" || echo "0")

        if [[ "$mysql_running" != "0" && "$dameng_running" != "0" ]]; then
            # 两个数据库都可用，让用户选择
            echo ""
            echo "检测到多个数据库服务，请选择要使用的数据库:"
            echo "  1) MySQL 数据库"
            echo "  2) 达梦数据库"
            echo ""

            while true; do
                read -p "请输入选择 (1-2): " choice
                case $choice in
                    1)
                        selected_db="mysql"
                        break
                        ;;
                    2)
                        selected_db="dameng"
                        break
                        ;;
                    *)
                        echo "无效选择，请输入 1 或 2"
                        ;;
                esac
            done
        elif [[ "$mysql_running" != "0" ]]; then
            selected_db="mysql"
            log_info "自动选择MySQL数据库"
        elif [[ "$dameng_running" != "0" ]]; then
            selected_db="dameng"
            log_info "自动选择达梦数据库"
        else
            log_error "没有可用的数据库服务"
            exit 1
        fi
    fi

    # 验证选择的数据库是否可用
    local service_name="ruoyi-${selected_db}"
    if ! docker ps | grep -q "$service_name"; then
        log_error "选择的数据库服务 $service_name 未运行"
        exit 1
    fi

    # 设置全局变量
    export SELECTED_DATABASE="$selected_db"

    log_info "已选择数据库类型: $selected_db"

    # 显示数据库连接信息
    if [[ "$selected_db" == "dameng" ]]; then
        echo "达梦数据库连接信息:"
        echo "  服务名: ruoyi-dameng"
        echo "  端口: ${PORTS[dameng]}"
        echo "  数据库: $DAMENG_DATABASE"
        echo "  用户名: $DAMENG_USER"
    else
        echo "MySQL数据库连接信息:"
        echo "  服务名: ruoyi-mysql"
        echo "  端口: ${PORTS[mysql]}"
        echo "  数据库: $MYSQL_DATABASE"
        echo "  用户名: root"
    fi
    echo ""
}

# 函数：创建应用目录结构
create_application_directories() {
    log_info "创建应用目录结构..."
    
    # 创建应用目录
    create_directory "$DEPLOY_ROOT/application"
    create_directory "$DEPLOY_ROOT/application/gateway"
    create_directory "$DEPLOY_ROOT/application/auth"
    create_directory "$DEPLOY_ROOT/application/modules/system"
    create_directory "$DEPLOY_ROOT/application/modules/gen"
    create_directory "$DEPLOY_ROOT/application/modules/job"
    create_directory "$DEPLOY_ROOT/application/modules/file"
    create_directory "$DEPLOY_ROOT/application/visual/monitor"
    
    # 创建前端目录
    create_directory "$DEPLOY_ROOT/nginx/html"
    create_directory "$DEPLOY_ROOT/nginx/conf"
    create_directory "$DEPLOY_ROOT/nginx/conf.d"
    create_directory "$DEPLOY_ROOT/nginx/logs"
    create_directory "$DEPLOY_ROOT/nginx/ssl"
    
    # 创建日志目录
    create_directory "$DEPLOY_ROOT/logs/gateway"
    create_directory "$DEPLOY_ROOT/logs/auth"
    create_directory "$DEPLOY_ROOT/logs/system"
    create_directory "$DEPLOY_ROOT/logs/gen"
    create_directory "$DEPLOY_ROOT/logs/job"
    create_directory "$DEPLOY_ROOT/logs/file"
    create_directory "$DEPLOY_ROOT/logs/monitor"
    
    # 创建配置目录
    create_directory "$DEPLOY_ROOT/config"
    
    # 创建SQL目录
    create_directory "$DEPLOY_ROOT/sql"
    
    # 创建上传目录
    create_directory "$DATA_ROOT/upload"
    
    log_info "应用目录结构创建完成"
}

# 函数：构建前端
build_frontend() {
    if [[ "$SKIP_FRONTEND" == "true" ]]; then
        log_info "跳过前端构建"
        return 0
    fi
    
    log_info "开始构建前端..."
    
    local frontend_dir
    if [[ -d "$PROJECT_ROOT/RuoYi-Cloud/ruoyi-ui" ]]; then
        frontend_dir="$PROJECT_ROOT/RuoYi-Cloud/ruoyi-ui"
    elif [[ -d "$PROJECT_ROOT/RuoYi-Vue/ruoyi-ui" ]]; then
        frontend_dir="$PROJECT_ROOT/RuoYi-Vue/ruoyi-ui"
    else
        log_error "未找到前端项目目录"
        return 1
    fi
    
    cd "$frontend_dir"
    
    # 检查package.json是否存在
    if [[ ! -f "package.json" ]]; then
        log_error "未找到package.json文件"
        return 1
    fi
    
    # 设置npm镜像源
    log_info "设置npm镜像源..."
    npm config set registry https://registry.npmmirror.com
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install --production=false
    
    # 构建前端
    log_info "构建前端项目..."
    npm run build:prod
    
    # 复制构建结果
    if [[ -d "dist" ]]; then
        log_info "复制前端构建结果..."
        cp -r dist/* "$DEPLOY_ROOT/nginx/html/"
        log_info "前端构建完成"
    else
        log_error "前端构建失败，未找到dist目录"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
}

# 函数：构建后端
build_backend() {
    if [[ "$SKIP_BACKEND" == "true" ]]; then
        log_info "跳过后端构建"
        return 0
    fi
    
    log_info "开始构建后端..."
    
    local backend_dir="$PROJECT_ROOT/RuoYi-Cloud"
    
    if [[ ! -d "$backend_dir" ]]; then
        log_error "未找到后端项目目录: $backend_dir"
        return 1
    fi
    
    cd "$backend_dir"
    
    # 检查pom.xml是否存在
    if [[ ! -f "pom.xml" ]]; then
        log_error "未找到pom.xml文件"
        return 1
    fi
    
    # 设置Maven镜像源
    log_info "配置Maven镜像源..."
    local maven_settings="$HOME/.m2/settings.xml"
    create_directory "$HOME/.m2"
    
    if [[ ! -f "$maven_settings" ]]; then
        cat > "$maven_settings" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <mirrors>
    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>*</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
  </mirrors>
</settings>
EOF
    fi
    
    # 清理并编译
    log_info "清理项目..."
    mvn clean
    
    log_info "编译后端项目..."
    mvn package -Dmaven.test.skip=true
    
    # 复制JAR文件
    log_info "复制JAR文件..."
    
    local services=(
        "ruoyi-gateway:gateway"
        "ruoyi-auth:auth"
        "ruoyi-modules/ruoyi-system:modules/system"
        "ruoyi-modules/ruoyi-gen:modules/gen"
        "ruoyi-modules/ruoyi-job:modules/job"
        "ruoyi-modules/ruoyi-file:modules/file"
        "ruoyi-visual/ruoyi-monitor:visual/monitor"
    )
    
    for service_info in "${services[@]}"; do
        local source_path=$(echo "$service_info" | cut -d':' -f1)
        local target_path=$(echo "$service_info" | cut -d':' -f2)
        local jar_name=$(basename "$source_path")
        
        local source_jar="$source_path/target/ruoyi-${jar_name}.jar"
        local target_dir="$DEPLOY_ROOT/application/$target_path"
        
        if [[ -f "$source_jar" ]]; then
            cp "$source_jar" "$target_dir/app.jar"
            log_info "复制 $jar_name JAR文件成功"
        else
            log_error "未找到JAR文件: $source_jar"
            return 1
        fi
    done
    
    log_info "后端构建完成"
    cd "$SCRIPT_DIR"
}

# 函数：复制SQL脚本
copy_sql_scripts() {
    if [[ "$SKIP_SQL" == "true" ]]; then
        log_info "跳过SQL脚本复制"
        return 0
    fi
    
    log_info "复制SQL脚本..."
    
    local sql_dir="$PROJECT_ROOT/RuoYi-Cloud/sql"
    
    if [[ -d "$sql_dir" ]]; then
        cp "$sql_dir"/*.sql "$DEPLOY_ROOT/sql/" 2>/dev/null || true
        log_info "SQL脚本复制完成"
    else
        log_warn "未找到SQL脚本目录: $sql_dir"
    fi
}

# 函数：生成Dockerfile
generate_dockerfiles() {
    log_info "生成Dockerfile..."
    
    local services=(
        "gateway"
        "auth"
        "modules/system"
        "modules/gen"
        "modules/job"
        "modules/file"
        "visual/monitor"
    )
    
    for service in "${services[@]}"; do
        local dockerfile_path="$DEPLOY_ROOT/application/$service/Dockerfile"
        
        cat > "$dockerfile_path" << 'EOF'
FROM openjdk:8-jre-alpine

LABEL maintainer="RuoYi-Cloud"

# 设置时区
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建应用目录
RUN mkdir -p /home/<USER>

# 设置工作目录
WORKDIR /home/<USER>

# 复制应用JAR文件
COPY app.jar /home/<USER>/app.jar

# 暴露端口
EXPOSE 8080

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JVM_OPTS -jar app.jar"]
EOF
        
        log_info "生成Dockerfile: $service"
    done
}

# 函数：生成Nginx配置
generate_nginx_config() {
    log_info "生成Nginx配置..."
    
    cat > "$DEPLOY_ROOT/nginx/conf/nginx.conf" << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    include /etc/nginx/conf.d/*.conf;
}
EOF

    cat > "$DEPLOY_ROOT/nginx/conf.d/ruoyi.conf" << 'EOF'
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        index index.html index.htm;
    }
    
    # API代理
    location /prod-api/ {
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://ruoyi-gateway:8080/;
    }
    
    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
EOF

    log_info "Nginx配置生成完成"
}

# 主函数
main() {
    local CHECK_ONLY=false
    local BUILD_ONLY=false
    local DEPLOY_ONLY=false
    local FORCE_INSTALL=false
    local SKIP_FRONTEND=false
    local SKIP_BACKEND=false
    local SKIP_SQL=false
    local DATABASE_TYPE_PARAM=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -b|--build-only)
                BUILD_ONLY=true
                shift
                ;;
            -d|--deploy-only)
                DEPLOY_ONLY=true
                shift
                ;;
            -f|--force)
                FORCE_INSTALL=true
                shift
                ;;
            --skip-frontend)
                SKIP_FRONTEND=true
                shift
                ;;
            --skip-backend)
                SKIP_BACKEND=true
                shift
                ;;
            --skip-sql)
                SKIP_SQL=true
                shift
                ;;
            --database=*)
                DATABASE_TYPE_PARAM="${1#*=}"
                if [[ "$DATABASE_TYPE_PARAM" != "mysql" && "$DATABASE_TYPE_PARAM" != "dameng" ]]; then
                    log_error "不支持的数据库类型: $DATABASE_TYPE_PARAM，支持的类型: mysql, dameng"
                    exit 1
                fi
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示欢迎信息
    echo "========================================"
    echo "    RuoYi-Cloud 应用一键部署脚本"
    echo "========================================"
    echo ""
    
    # 检查构建环境
    check_build_environment

    if [[ "$CHECK_ONLY" == "true" ]]; then
        log_info "构建环境检查完成，满足部署要求"
        exit 0
    fi

    # 选择数据库类型
    select_database_type

    # 创建目录结构
    create_application_directories
    
    if [[ "$DEPLOY_ONLY" != "true" ]]; then
        # 构建应用
        log_info "开始构建应用..."
        
        # 构建前端
        build_frontend
        
        # 构建后端
        build_backend
        
        # 复制SQL脚本
        copy_sql_scripts
        
        # 生成Dockerfile
        generate_dockerfiles
        
        # 生成Nginx配置
        generate_nginx_config
        
        log_info "应用构建完成"
    fi
    
    if [[ "$BUILD_ONLY" != "true" ]]; then
        # 部署应用
        log_info "开始部署应用..."
        
        # 使用docker-compose部署
        cd "$SCRIPT_DIR"

        # 设置环境变量
        export DEPLOY_ROOT
        export DATA_ROOT
        export REDIS_PASSWORD
        export JVM_OPTS
        export SELECTED_DATABASE

        # 根据选择的数据库设置连接信息
        if [[ "$SELECTED_DATABASE" == "dameng" ]]; then
            export DATABASE_HOST="ruoyi-dameng"
            export DATABASE_PORT="$DAMENG_PORT_NUM"
            export DATABASE_NAME="$DAMENG_DATABASE"
            export DATABASE_USERNAME="$DAMENG_USER"
            export DATABASE_PASSWORD="$DAMENG_PASSWORD"
            export DATABASE_URL="jdbc:dm://ruoyi-dameng:${DAMENG_PORT_NUM}/${DAMENG_DATABASE}"
        else
            export DATABASE_HOST="ruoyi-mysql"
            export DATABASE_PORT="3306"
            export DATABASE_NAME="$MYSQL_DATABASE"
            export DATABASE_USERNAME="root"
            export DATABASE_PASSWORD="$MYSQL_ROOT_PASSWORD"
            export DATABASE_URL="*****************************/${MYSQL_DATABASE}?characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC"
        fi

        # 启动应用服务
        docker-compose -f docker-compose-application.yml up -d
        
        log_info "应用部署完成"
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 30
        
        # 检查服务状态
        log_info "检查服务状态..."
        docker-compose -f docker-compose-application.yml ps
    fi
    
    log_info "RuoYi-Cloud应用部署完成！"
    echo ""
    echo "========================================"
    echo "           部署完成信息"
    echo "========================================"
    echo "前端访问地址: http://$(hostname -I | awk '{print $1}'):80"
    echo "网关地址: http://$(hostname -I | awk '{print $1}'):8080"
    echo "认证服务: http://$(hostname -I | awk '{print $1}'):9200"
    echo "系统服务: http://$(hostname -I | awk '{print $1}'):9201"
    echo "代码生成: http://$(hostname -I | awk '{print $1}'):9202"
    echo "定时任务: http://$(hostname -I | awk '{print $1}'):9203"
    echo "文件服务: http://$(hostname -I | awk '{print $1}'):9300"
    echo "监控中心: http://$(hostname -I | awk '{print $1}'):9100"
    echo ""
    echo "默认账号: admin"
    echo "默认密码: admin123"
    echo "========================================"
}

# 执行主函数
main "$@"
