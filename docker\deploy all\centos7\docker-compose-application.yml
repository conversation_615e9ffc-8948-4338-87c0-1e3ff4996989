version: '3.8'

networks:
  ruoyi-network:
    external: true

services:
  ruoyi-gateway:
    container_name: ruoyi-gateway
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/gateway
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      REDIS_HOST: ruoyi-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-RuoYi@2024}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx512m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/gateway:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
      - ruoyi-redis
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_GATEWAY:-512m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-auth:
    container_name: ruoyi-auth
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/auth
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9200:9200"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      REDIS_HOST: ruoyi-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-RuoYi@2024}
      DATABASE_HOST: ${DATABASE_HOST:-ruoyi-mysql}
      DATABASE_PORT: ${DATABASE_PORT:-3306}
      DATABASE_NAME: ${DATABASE_NAME:-ry-cloud}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-root}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-RuoYi@2024}
      DATABASE_URL: ${DATABASE_URL:-***********************************************************************************************************************************************************************}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx512m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/auth:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
      - ruoyi-redis
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_AUTH:-512m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-modules-system:
    container_name: ruoyi-modules-system
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/modules/system
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9201:9201"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      REDIS_HOST: ruoyi-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-RuoYi@2024}
      MYSQL_HOST: ruoyi-mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ry-cloud}
      MYSQL_USERNAME: root
      MYSQL_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx512m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/system:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
      - ruoyi-redis
      - ruoyi-mysql
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_SYSTEM:-512m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9201/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-modules-gen:
    container_name: ruoyi-modules-gen
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/modules/gen
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9202:9202"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      MYSQL_HOST: ruoyi-mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ry-cloud}
      MYSQL_USERNAME: root
      MYSQL_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx256m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/gen:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
      - ruoyi-mysql
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_GEN:-256m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9202/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-modules-job:
    container_name: ruoyi-modules-job
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/modules/job
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9203:9203"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      MYSQL_HOST: ruoyi-mysql
      MYSQL_PORT: 3306
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ry-cloud}
      MYSQL_USERNAME: root
      MYSQL_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx256m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/job:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
      - ruoyi-mysql
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_JOB:-256m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9203/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-modules-file:
    container_name: ruoyi-modules-file
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/modules/file
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9300:9300"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx256m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/file:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
      - ${DATA_ROOT:-/home}/upload:/home/<USER>/uploadPath
    depends_on:
      - ruoyi-nacos
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_FILE:-256m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9300/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  ruoyi-visual-monitor:
    container_name: ruoyi-visual-monitor
    build:
      context: ${DEPLOY_ROOT:-/erdCloud}/application/visual/monitor
      dockerfile: Dockerfile
    restart: always
    ports:
      - "9100:9100"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_HOST: ruoyi-nacos
      NACOS_PORT: 8848
      JVM_OPTS: ${JVM_OPTS:--Xms256m -Xmx256m -XX:+UseG1GC}
      TZ: Asia/Shanghai
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/logs/monitor:/home/<USER>/logs
      - ${DEPLOY_ROOT:-/erdCloud}/config:/home/<USER>/config
    depends_on:
      - ruoyi-nacos
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_MONITOR:-256m}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9100/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
