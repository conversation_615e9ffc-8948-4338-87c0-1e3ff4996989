# 车辆管理系统菜单配置说明

## 📋 配置概述

为了让您能够在系统首页看到车辆管理菜单，需要执行以下步骤来配置菜单结构。

---

## 🚀 快速配置步骤

### 1. 执行菜单SQL脚本

**方法一：直接执行SQL文件**
```sql
-- 在数据库中执行以下文件
source sql/vehicle_menu_simple.sql;
```

**方法二：手动执行SQL语句**
打开数据库管理工具（如Navicat、phpMyAdmin等），执行 `sql/vehicle_menu_simple.sql` 文件中的所有SQL语句。

### 2. 重启后端服务

执行SQL后，需要重启后端服务以刷新菜单缓存：
```bash
# 停止服务
# 重新启动后端服务
```

### 3. 刷新前端页面

重新登录系统或刷新浏览器页面，菜单将会显示。

---

## 📊 菜单结构

### 🚗 车辆管理（主菜单）
```
车辆管理
├── 车辆信息          # 车辆基础信息管理
├── 用车申请          # 用车申请流程管理
├── 用车订单          # 订单确认和费用管理
├── 队伍信息          # 施工队伍信息管理
├── 维修记录          # 车辆维修保养记录
├── 违章记录          # 车辆违章处理记录
└── 需求计划          # 车辆需求计划管理
```

### 详细菜单说明

#### 1. 🚙 车辆信息
- **路径**: `/vehicle/info`
- **功能**: 管理所有车辆的基础信息
- **权限**: `vehicle:info:list`
- **子功能**: 查询、新增、修改、删除、导出

#### 2. 📝 用车申请  
- **路径**: `/vehicle/application`
- **功能**: 处理用车申请流程
- **权限**: `vehicle:application:list`
- **子功能**: 查询、新增、修改、删除、审批、调度、导出

#### 3. 📋 用车订单
- **路径**: `/vehicle/order`
- **功能**: 管理用车订单和费用
- **权限**: `vehicle:order:list`
- **子功能**: 查询、确认、退回、费用计算、导出

#### 4. 👥 队伍信息
- **路径**: `/vehicle/team`
- **功能**: 管理施工队伍信息
- **权限**: `vehicle:team:list`
- **子功能**: 查询、新增、修改、删除、导出

#### 5. 🔧 维修记录
- **路径**: `/vehicle/maintenance`
- **功能**: 记录车辆维修保养情况
- **权限**: `vehicle:maintenance:list`
- **子功能**: 查询、新增、修改、删除、导出

#### 6. ⚠️ 违章记录
- **路径**: `/vehicle/violation`
- **功能**: 管理车辆违章记录
- **权限**: `vehicle:violation:list`
- **子功能**: 查询、新增、修改、删除、处理、导出

#### 7. 📊 需求计划
- **路径**: `/vehicle/demand`
- **功能**: 制定和管理车辆需求计划
- **权限**: `vehicle:demand:list`
- **子功能**: 查询、新增、修改、删除、审批、导出

---

## 🔐 权限配置

### 菜单权限标识
```
vehicle:info:*          # 车辆信息相关权限
vehicle:application:*   # 用车申请相关权限
vehicle:order:*         # 用车订单相关权限
vehicle:team:*          # 队伍信息相关权限
vehicle:maintenance:*   # 维修记录相关权限
vehicle:violation:*     # 违章记录相关权限
vehicle:demand:*        # 需求计划相关权限
```

### 默认权限分配
- **admin角色**: 拥有所有车辆管理权限
- **其他角色**: 需要在系统管理->角色管理中手动分配权限

---

## 🎯 菜单显示效果

配置完成后，您将在系统左侧菜单栏看到：

```
🏠 首页
📊 系统监控
⚙️ 系统管理
🚗 车辆管理          ← 新增的主菜单
   ├── 🚙 车辆信息
   ├── 📝 用车申请
   ├── 📋 用车订单
   ├── 👥 队伍信息
   ├── 🔧 维修记录
   ├── ⚠️ 违章记录
   └── 📊 需求计划
```

---

## 🛠️ 故障排除

### 问题1: 菜单不显示
**解决方案**:
1. 确认SQL脚本执行成功
2. 重启后端服务
3. 清除浏览器缓存并重新登录
4. 检查用户角色是否有相应权限

### 问题2: 点击菜单报404错误
**解决方案**:
1. 确认前端路由配置正确
2. 确认Vue组件文件存在
3. 检查组件路径是否正确

### 问题3: 页面显示但功能异常
**解决方案**:
1. 确认后端Controller和Service已实现
2. 检查API接口是否正常
3. 查看浏览器控制台错误信息

---

## 📋 验证清单

配置完成后，请验证以下项目：

- [ ] 数据库中sys_menu表包含车辆管理相关记录
- [ ] 数据库中sys_role_menu表包含角色菜单关联记录
- [ ] 后端服务重启成功
- [ ] 登录系统后能看到"车辆管理"主菜单
- [ ] 能够展开车辆管理子菜单
- [ ] 点击子菜单能正常跳转到对应页面
- [ ] 页面功能正常（增删改查等）

---

## 🎉 配置完成

完成以上步骤后，您就可以正常使用车辆管理系统的所有功能了！

如果遇到任何问题，请检查：
1. 数据库连接是否正常
2. 后端服务是否正常运行
3. 前端服务是否正常运行
4. 用户权限是否正确配置
