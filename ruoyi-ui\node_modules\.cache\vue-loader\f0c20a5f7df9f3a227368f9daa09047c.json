{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\manager-approve.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\manager-approve.vue", "mtime": 1754144140594}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFBlbmRpbmdDb25maXJtT3JkZXJzLCBnZXRPcmRlciwgbWFuYWdlckFwcHJvdmVPcmRlciB9IGZyb20gIkAvYXBpL3ZlaGljbGUvb3JkZXIiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJNYW5hZ2VyQXBwcm92ZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOiuouWNleWIl+ihqAogICAgICBvcmRlckxpc3Q6IFtdLAogICAgICAvLyDnu5/orqHkv6Hmga8KICAgICAgc3RhdGlzdGljczogewogICAgICAgIHBlbmRpbmdDb3VudDogMCwKICAgICAgICBhcHByb3ZlZENvdW50OiAwLAogICAgICAgIHRvdGFsQ29zdDogMCwKICAgICAgICB0b2RheUNvdW50OiAwCiAgICAgIH0sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG9yZGVyU3RhdHVzOiAncGVuZGluZ19tYW5hZ2VyJwogICAgICB9LAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g6K+m5oOF5a+56K+d5qGGCiAgICAgIGRldGFpbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkZXRhaWxPcmRlcjoge30sCiAgICAgIC8vIOWuoeaJueWvueivneahhgogICAgICBhcHByb3ZlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGFwcHJvdmVMb2FkaW5nOiBmYWxzZSwKICAgICAgY3VycmVudE9yZGVyOiB7fSwKICAgICAgYXBwcm92ZUZvcm06IHsKICAgICAgICBhcHByb3ZlUmVzdWx0OiAnYXBwcm92ZScsCiAgICAgICAgYXBwcm92ZUNvbW1lbnQ6ICcnCiAgICAgIH0sCiAgICAgIC8vIOWuoeaJueihqOWNlemqjOivgeinhOWImQogICAgICBhcHByb3ZlUnVsZXM6IHsKICAgICAgICBhcHByb3ZlQ29tbWVudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeWuoeaJueaEj+ingSIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDUsIG1heDogNTAwLCBtZXNzYWdlOiAi6ZW/5bqm5ZyoIDUg5YiwIDUwMCDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMubG9hZFN0YXRpc3RpY3MoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LorqLljZXliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0ge307CiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siYmVnaW5BY3R1YWxFbmRUaW1lIl0gPSB0aGlzLmRhdGVSYW5nZVswXTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siZW5kQWN0dWFsRW5kVGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgCiAgICAgIGdldFBlbmRpbmdDb25maXJtT3JkZXJzKCdtYW5hZ2VyJykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5vcmRlckxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLmxlbmd0aDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog5Yqg6L2957uf6K6h5L+h5oGvICovCiAgICBsb2FkU3RhdGlzdGljcygpIHsKICAgICAgLy8gVE9ETzog6LCD55So57uf6K6h5o6l5Y+jCiAgICAgIHRoaXMuc3RhdGlzdGljcyA9IHsKICAgICAgICBwZW5kaW5nQ291bnQ6IDMsCiAgICAgICAgYXBwcm92ZWRDb3VudDogMjUsCiAgICAgICAgdG90YWxDb3N0OiAxNS42LAogICAgICAgIHRvZGF5Q291bnQ6IDIKICAgICAgfTsKICAgIH0sCiAgICAKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIAogICAgLyoqIOWIt+aWsOWIl+ihqCAqLwogICAgcmVmcmVzaExpc3QoKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmxvYWRTdGF0aXN0aWNzKCk7CiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIt+aWsOaIkOWKnyIpOwogICAgfSwKICAgIAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub3JkZXJJZCkKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgCiAgICAvKiog5p+l55yL6K+m5oOFICovCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICBnZXRPcmRlcihyb3cub3JkZXJJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZXRhaWxPcmRlciA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog5a6h5om56YCa6L+HICovCiAgICBoYW5kbGVBcHByb3ZlKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRPcmRlciA9IHJvdzsKICAgICAgdGhpcy5hcHByb3ZlRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuYXBwcm92ZUZvcm0gPSB7CiAgICAgICAgYXBwcm92ZVJlc3VsdDogJ2FwcHJvdmUnLAogICAgICAgIGFwcHJvdmVDb21tZW50OiAnJwogICAgICB9OwogICAgfSwKICAgIAogICAgLyoqIOmAgOWbnuS/ruaUuSAqLwogICAgaGFuZGxlUmVqZWN0KHJvdykgewogICAgICB0aGlzLmN1cnJlbnRPcmRlciA9IHJvdzsKICAgICAgdGhpcy5hcHByb3ZlRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuYXBwcm92ZUZvcm0gPSB7CiAgICAgICAgYXBwcm92ZVJlc3VsdDogJ3JlamVjdCcsCiAgICAgICAgYXBwcm92ZUNvbW1lbnQ6ICcnCiAgICAgIH07CiAgICB9LAogICAgCiAgICAvKiog5o+Q5Lqk5a6h5om5ICovCiAgICBzdWJtaXRBcHByb3ZhbCgpIHsKICAgICAgdGhpcy4kcmVmc1siYXBwcm92ZUZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFwcHJvdmVMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgICAgIG9yZGVySWQ6IHRoaXMuY3VycmVudE9yZGVyLm9yZGVySWQsCiAgICAgICAgICAgIGFwcHJvdmVSZXN1bHQ6IHRoaXMuYXBwcm92ZUZvcm0uYXBwcm92ZVJlc3VsdCwKICAgICAgICAgICAgYXBwcm92ZUNvbW1lbnQ6IHRoaXMuYXBwcm92ZUZvcm0uYXBwcm92ZUNvbW1lbnQKICAgICAgICAgIH07CiAgICAgICAgICAKICAgICAgICAgIG1hbmFnZXJBcHByb3ZlT3JkZXIoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWuoeaJueaIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmFwcHJvdmVEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5hcHByb3ZlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDmibnph4/lrqHmibkgKi8KICAgIGhhbmRsZUJhdGNoQXBwcm92ZSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlrqHmibnnmoTorqLljZUiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oYOehruiupOaJuemHj+WuoeaJuemAieS4reeahCAke3RoaXMuaWRzLmxlbmd0aH0g5Liq6K6i5Y2V77yfYCkudGhlbigoKSA9PiB7CiAgICAgICAgLy8gVE9ETzog6LCD55So5om56YeP5a6h5om55o6l5Y+jCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5om56YeP5a6h5om55oiQ5YqfIik7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAKICAgIC8qKiDmmL7npLrotLnnlKjliIbmnpAgKi8KICAgIHNob3dDb3N0QW5hbHlzaXMoKSB7CiAgICAgIHRoaXMuJG1vZGFsLm1zZ0luZm8oIui0ueeUqOWIhuaekOWKn+iDveW8gOWPkeS4rS4uLiIpOwogICAgfSwKICAgIAogICAgLyoqIOWvvOWHuuaKpeihqCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLiRtb2RhbC5tc2dJbmZvKCLlr7zlh7rlip/og73lvIDlj5HkuK0uLi4iKTsKICAgIH0sCiAgICAKICAgIC8qKiDojrflj5blrqHmibnmraXpqqQgKi8KICAgIGdldEFwcHJvdmFsU3RlcChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RlcE1hcCA9IHsKICAgICAgICAnZHJpdmVyX2ZpbmlzaGVkJzogMSwKICAgICAgICAndGVhbV9jb25maXJtZWQnOiAyLAogICAgICAgICdkaXNwYXRjaF9jb25maXJtZWQnOiAzLAogICAgICAgICdwZW5kaW5nX21hbmFnZXInOiAzLAogICAgICAgICdjb21wbGV0ZWQnOiA0CiAgICAgIH07CiAgICAgIHJldHVybiBzdGVwTWFwW3N0YXR1c10gfHwgMDsKICAgIH0sCiAgICAKICAgIC8qKiDorqHnrpfnlKjovabml7bplb8gKi8KICAgIGNhbGN1bGF0ZUR1cmF0aW9uKHN0YXJ0VGltZSwgZW5kVGltZSkgewogICAgICBpZiAoIXN0YXJ0VGltZSB8fCAhZW5kVGltZSkgcmV0dXJuICcw5bCP5pe2JzsKICAgICAgCiAgICAgIGNvbnN0IGRpZmYgPSBuZXcgRGF0ZShlbmRUaW1lKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShzdGFydFRpbWUpLmdldFRpbWUoKTsKICAgICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKGRpZmYgLyAoMTAwMCAqIDYwICogNjApKTsKICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKGRpZmYgJSAoMTAwMCAqIDYwICogNjApKSAvICgxMDAwICogNjApKTsKICAgICAgCiAgICAgIHJldHVybiBgJHtob3Vyc33lsI/ml7Yke21pbnV0ZXN95YiG6ZKfYDsKICAgIH0sCiAgICAKICAgIC8qKiDojrflj5botLnnlKjmib/mi4XmlrnmlofmnKwgKi8KICAgIGdldENvc3RCZWFyZXJUZXh0KGNvc3RCZWFyZXIpIHsKICAgICAgY29uc3QgYmVhcmVyTWFwID0gewogICAgICAgICdwcm9qZWN0JzogJ+mhueebruaJv+aLhScsCiAgICAgICAgJ3RlYW0nOiAn6Zif5LyN5om/5ouFJwogICAgICB9OwogICAgICByZXR1cm4gYmVhcmVyTWFwW2Nvc3RCZWFyZXJdIHx8ICfmnKrnn6UnOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLwogICAgZ2V0U3RhdHVzVGFnVHlwZShzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICdwZW5kaW5nX21hbmFnZXInOiAnd2FybmluZycsCiAgICAgICAgJ2NvbXBsZXRlZCc6ICdzdWNjZXNzJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICdwZW5kaW5nX21hbmFnZXInOiAn5b6F5Li7566h5a6h5om5JywKICAgICAgICAnY29tcGxldGVkJzogJ+W3suWujOaIkCcKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["manager-approve.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+UA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "manager-approve.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">机械主管 - 最终审批</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待审批订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.approvedCount }}</div>\n              <div class=\"stat-label\">已审批订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.totalCost }}</div>\n              <div class=\"stat-label\">总费用(万元)</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日审批</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"待主管审批\" value=\"pending_manager\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"费用范围\" prop=\"costRange\">\n          <el-select v-model=\"queryParams.costRange\" placeholder=\"请选择费用范围\" clearable @change=\"getList\">\n            <el-option label=\"1000元以下\" value=\"under_1000\"></el-option>\n            <el-option label=\"1000-5000元\" value=\"1000_5000\"></el-option>\n            <el-option label=\"5000-10000元\" value=\"5000_10000\"></el-option>\n            <el-option label=\"10000元以上\" value=\"over_10000\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆重量\" prop=\"vehicleWeight\">\n          <el-select v-model=\"queryParams.vehicleWeight\" placeholder=\"请选择重量范围\" clearable @change=\"getList\">\n            <el-option label=\"50吨及以上\" value=\"over_50\"></el-option>\n            <el-option label=\"50吨以下\" value=\"under_50\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchApprove\"\n            v-hasPermi=\"['vehicle:order:manager-approve']\"\n          >批量审批</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-pie-chart\"\n            size=\"mini\"\n            @click=\"showCostAnalysis\"\n          >费用分析</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-download\"\n            size=\"mini\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['vehicle:order:export']\"\n          >导出报表</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n            <div style=\"color: #E6A23C; font-size: 12px; font-weight: bold;\">\n              重量：{{ scope.row.vehicleWeight }}吨\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"队伍信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.driverName }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"费用信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>\n              <div style=\"color: #67C23A; font-weight: bold; font-size: 16px;\">￥{{ scope.row.totalCost }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ getCostBearerText(scope.row.costBearer) }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.costUnit === 'hour' ? '按小时' : '按天' }}计费</div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'pending_manager'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleApprove(scope.row)\"\n              v-hasPermi=\"['vehicle:order:manager-approve']\"\n            >审批通过</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'pending_manager'\"\n              size=\"mini\"\n              type=\"warning\"\n              @click=\"handleReject(scope.row)\"\n              v-hasPermi=\"['vehicle:order:manager-reject']\"\n            >退回</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"1000px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"车辆重量\">\n          <span style=\"color: #E6A23C; font-weight: bold;\">{{ detailOrder.vehicleWeight }}吨</span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用详情\">\n          <div>\n            <div>总费用：<span style=\"color: #67C23A; font-weight: bold; font-size: 18px;\">￥{{ detailOrder.totalCost }}</span></div>\n            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>\n            <div>计量单位：{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>\n            <div>单价：￥{{ detailOrder.unitPrice }}/{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>\n            <div>计算时间：{{ parseTime(detailOrder.costCalculateTime) }}</div>\n          </div>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 审批流程 -->\n      <div style=\"margin-top: 20px;\">\n        <h4>审批流程</h4>\n        <el-steps :active=\"getApprovalStep(detailOrder.orderStatus)\" finish-status=\"success\">\n          <el-step title=\"司机完成\" :description=\"parseTime(detailOrder.actualEndTime)\"></el-step>\n          <el-step title=\"队伍确认\" :description=\"parseTime(detailOrder.teamConfirmTime)\"></el-step>\n          <el-step title=\"调度确认\" :description=\"parseTime(detailOrder.dispatchConfirmTime)\"></el-step>\n          <el-step title=\"主管审批\" :description=\"parseTime(detailOrder.managerConfirmTime)\"></el-step>\n        </el-steps>\n      </div>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 审批操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'pending_manager'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">主管审批</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleApprove(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 审批通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"warning\" size=\"medium\" @click=\"handleReject(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-close\"></i> 退回修改\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 审批对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"approveDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"approveForm\" :model=\"approveForm\" :rules=\"approveRules\" label-width=\"100px\">\n        <el-form-item label=\"审批结果\" prop=\"approveResult\">\n          <el-radio-group v-model=\"approveForm.approveResult\">\n            <el-radio label=\"approve\">审批通过</el-radio>\n            <el-radio label=\"reject\">退回修改</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"审批意见\" prop=\"approveComment\">\n          <el-input\n            v-model=\"approveForm.approveComment\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入审批意见\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"approveDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitApproval\" :loading=\"approveLoading\">确认审批</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, managerApproveOrder } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"ManagerApprove\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        approvedCount: 0,\n        totalCost: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'pending_manager'\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 审批对话框\n      approveDialogVisible: false,\n      approveLoading: false,\n      currentOrder: {},\n      approveForm: {\n        approveResult: 'approve',\n        approveComment: ''\n      },\n      // 审批表单验证规则\n      approveRules: {\n        approveComment: [\n          { required: true, message: \"请输入审批意见\", trigger: \"blur\" },\n          { min: 5, max: 500, message: \"长度在 5 到 500 个字符\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('manager').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 3,\n        approvedCount: 25,\n        totalCost: 15.6,\n        todayCount: 2\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 审批通过 */\n    handleApprove(row) {\n      this.currentOrder = row;\n      this.approveDialogVisible = true;\n      this.approveForm = {\n        approveResult: 'approve',\n        approveComment: ''\n      };\n    },\n    \n    /** 退回修改 */\n    handleReject(row) {\n      this.currentOrder = row;\n      this.approveDialogVisible = true;\n      this.approveForm = {\n        approveResult: 'reject',\n        approveComment: ''\n      };\n    },\n    \n    /** 提交审批 */\n    submitApproval() {\n      this.$refs[\"approveForm\"].validate(valid => {\n        if (valid) {\n          this.approveLoading = true;\n          const data = {\n            orderId: this.currentOrder.orderId,\n            approveResult: this.approveForm.approveResult,\n            approveComment: this.approveForm.approveComment\n          };\n          \n          managerApproveOrder(data).then(response => {\n            this.$modal.msgSuccess(\"审批成功\");\n            this.approveDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.approveLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 批量审批 */\n    handleBatchApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审批的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认批量审批选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量审批接口\n        this.$modal.msgSuccess(\"批量审批成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 显示费用分析 */\n    showCostAnalysis() {\n      this.$modal.msgInfo(\"费用分析功能开发中...\");\n    },\n    \n    /** 导出报表 */\n    handleExport() {\n      this.$modal.msgInfo(\"导出功能开发中...\");\n    },\n    \n    /** 获取审批步骤 */\n    getApprovalStep(status) {\n      const stepMap = {\n        'driver_finished': 1,\n        'team_confirmed': 2,\n        'dispatch_confirmed': 3,\n        'pending_manager': 3,\n        'completed': 4\n      };\n      return stepMap[status] || 0;\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取费用承担方文本 */\n    getCostBearerText(costBearer) {\n      const bearerMap = {\n        'project': '项目承担',\n        'team': '队伍承担'\n      };\n      return bearerMap[costBearer] || '未知';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending_manager': 'warning',\n        'completed': 'success'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending_manager': '待主管审批',\n        'completed': '已完成'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n</style>\n"]}]}