package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleMaintenance;

/**
 * 车辆维修记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface IVehicleMaintenanceService 
{
    /**
     * 查询车辆维修记录
     * 
     * @param maintenanceId 车辆维修记录主键
     * @return 车辆维修记录
     */
    public VehicleMaintenance selectVehicleMaintenanceByMaintenanceId(Long maintenanceId);

    /**
     * 查询车辆维修记录列表
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 车辆维修记录集合
     */
    public List<VehicleMaintenance> selectVehicleMaintenanceList(VehicleMaintenance vehicleMaintenance);

    /**
     * 新增车辆维修记录
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 结果
     */
    public int insertVehicleMaintenance(VehicleMaintenance vehicleMaintenance);

    /**
     * 修改车辆维修记录
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 结果
     */
    public int updateVehicleMaintenance(VehicleMaintenance vehicleMaintenance);

    /**
     * 批量删除车辆维修记录
     * 
     * @param maintenanceIds 需要删除的车辆维修记录主键集合
     * @return 结果
     */
    public int deleteVehicleMaintenanceByMaintenanceIds(Long[] maintenanceIds);

    /**
     * 删除车辆维修记录信息
     * 
     * @param maintenanceId 车辆维修记录主键
     * @return 结果
     */
    public int deleteVehicleMaintenanceByMaintenanceId(Long maintenanceId);

    /**
     * 根据车辆ID查询维修记录
     * 
     * @param vehicleId 车辆ID
     * @return 维修记录集合
     */
    public List<VehicleMaintenance> selectVehicleMaintenanceByVehicleId(Long vehicleId);

    /**
     * 根据状态查询维修记录
     * 
     * @param status 状态
     * @return 维修记录集合
     */
    public List<VehicleMaintenance> selectVehicleMaintenanceByStatus(String status);

    /**
     * 查询即将到期的维修记录
     * 
     * @return 维修记录集合
     */
    public List<VehicleMaintenance> selectUpcomingMaintenance();
}
