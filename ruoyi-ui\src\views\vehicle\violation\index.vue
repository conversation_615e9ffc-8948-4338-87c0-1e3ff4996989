<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车辆ID" prop="vehicleId">
        <el-select v-model="queryParams.vehicleId" placeholder="请选择车辆" clearable>
          <el-option
            v-for="vehicle in vehicleOptions"
            :key="vehicle.vehicleId"
            :label="vehicle.vehicleModel"
            :value="vehicle.vehicleId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="违章类型" prop="violationType">
        <el-select v-model="queryParams.violationType" placeholder="请选择违章类型" clearable>
          <el-option label="超速" value="超速"></el-option>
          <el-option label="违停" value="违停"></el-option>
          <el-option label="闯红灯" value="闯红灯"></el-option>
          <el-option label="其他" value="其他"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable>
          <el-option label="未处理" value="pending"></el-option>
          <el-option label="处理中" value="processing"></el-option>
          <el-option label="已处理" value="processed"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="违章时间">
        <el-date-picker
          v-model="daterangeViolationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vehicle:violation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vehicle:violation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vehicle:violation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:violation:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchProcess"
          v-hasPermi="['vehicle:violation:process']"
        >批量处理</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="violationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="违章ID" align="center" prop="violationId" />
      <el-table-column label="车辆信息" align="center" prop="vehicleInfo" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
          <div style="color: #909399; font-size: 12px;">
            {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="违章类型" align="center" prop="violationType" />
      <el-table-column label="违章时间" align="center" prop="violationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.violationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="违章地点" align="center" prop="violationLocation" />
      <el-table-column label="罚款金额" align="center" prop="penaltyAmount">
        <template slot-scope="scope">
          <span style="color: #E6A23C;">¥{{ scope.row.penaltyAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.violation_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:violation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:violation:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.status === 'pending'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row)"
            v-hasPermi="['vehicle:violation:process']"
          >处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改违章记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="车辆" prop="vehicleId">
          <el-select v-model="form.vehicleId" placeholder="请选择车辆">
            <el-option
              v-for="vehicle in vehicleOptions"
              :key="vehicle.vehicleId"
              :label="vehicle.vehicleModel"
              :value="vehicle.vehicleId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违章类型" prop="violationType">
          <el-select v-model="form.violationType" placeholder="请选择违章类型">
            <el-option label="超速" value="超速"></el-option>
            <el-option label="违停" value="违停"></el-option>
            <el-option label="闯红灯" value="闯红灯"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违章时间" prop="violationTime">
          <el-date-picker clearable
            v-model="form.violationTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择违章时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="违章地点" prop="violationLocation">
          <el-input v-model="form.violationLocation" placeholder="请输入违章地点" />
        </el-form-item>
        <el-form-item label="罚款金额" prop="penaltyAmount">
          <el-input v-model="form.penaltyAmount" placeholder="请输入罚款金额" />
        </el-form-item>
        <el-form-item label="违章描述" prop="violationDescription">
          <el-input v-model="form.violationDescription" type="textarea" placeholder="请输入违章描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listViolation, getViolation, delViolation, addViolation, updateViolation, processViolation, batchProcessViolation } from "@/api/vehicle/violation";
import { listInfo } from "@/api/vehicle/info";

export default {
  name: "Violation",
  dicts: ['violation_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 违章记录表格数据
      violationList: [],
      // 车辆选项
      vehicleOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 违章时间时间范围
      daterangeViolationTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleId: null,
        violationType: null,
        violationTime: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        vehicleId: [
          { required: true, message: "车辆不能为空", trigger: "change" }
        ],
        violationType: [
          { required: true, message: "违章类型不能为空", trigger: "change" }
        ],
        violationTime: [
          { required: true, message: "违章时间不能为空", trigger: "blur" }
        ],
        violationLocation: [
          { required: true, message: "违章地点不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getVehicleOptions();
  },
  methods: {
    /** 查询违章记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeViolationTime && '' != this.daterangeViolationTime) {
        this.queryParams.params["beginViolationTime"] = this.daterangeViolationTime[0];
        this.queryParams.params["endViolationTime"] = this.daterangeViolationTime[1];
      }
      listViolation(this.queryParams).then(response => {
        this.violationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取车辆选项 */
    getVehicleOptions() {
      listInfo().then(response => {
        this.vehicleOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        violationId: null,
        vehicleId: null,
        violationType: null,
        violationTime: null,
        violationLocation: null,
        violationDescription: null,
        penaltyAmount: null,
        status: "pending",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeViolationTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.violationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加违章记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const violationId = row.violationId || this.ids
      getViolation(violationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改违章记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.violationId != null) {
            updateViolation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addViolation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const violationIds = row.violationId || this.ids;
      this.$modal.confirm('是否确认删除违章记录编号为"' + violationIds + '"的数据项？').then(function() {
        return delViolation(violationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 处理违章记录 */
    handleProcess(row) {
      this.$modal.confirm('是否确认处理该违章记录？').then(function() {
        return processViolation(row.violationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("处理成功");
      }).catch(() => {});
    },
    /** 批量处理违章记录 */
    handleBatchProcess() {
      this.$modal.confirm('是否确认批量处理选中的违章记录？').then(() => {
        return batchProcessViolation(this.ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量处理成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/violation/export', {
        ...this.queryParams
      }, `violation_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
