<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleInfoMapper">
    
    <resultMap type="VehicleInfo" id="VehicleInfoResult">
        <result property="vehicleId"        column="vehicle_id"        />
        <result property="vehicleType"      column="vehicle_type"      />
        <result property="vehicleModel"     column="vehicle_model"     />
        <result property="unitName"         column="unit_name"         />
        <result property="licensePlate"     column="license_plate"     />
        <result property="driverName"       column="driver_name"       />
        <result property="driverPhone"      column="driver_phone"      />
        <result property="commanderName"    column="commander_name"    />
        <result property="commanderPhone"   column="commander_phone"   />
        <result property="entryTime"        column="entry_time"        />
        <result property="projectPhase"     column="project_phase"     />
        <result property="vehicleStatus"    column="vehicle_status"    />
        <result property="shiftConfirmer"   column="shift_confirmer"   />
        <result property="costUnit"         column="cost_unit"         />
        <result property="vehicleWeight"    column="vehicle_weight"    />
        <result property="unitPrice"        column="unit_price"        />
        <result property="costPerHour"      column="cost_per_hour"     />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <sql id="selectVehicleInfoVo">
        select vehicle_id, vehicle_type, vehicle_model, unit_name, license_plate, driver_name, driver_phone, commander_name, commander_phone, entry_time, project_phase, vehicle_status, shift_confirmer, cost_unit, vehicle_weight, unit_price, cost_per_hour, create_by, create_time, update_by, update_time, remark from vehicle_info
    </sql>

    <select id="selectVehicleInfoList" parameterType="VehicleInfo" resultMap="VehicleInfoResult">
        <include refid="selectVehicleInfoVo"/>
        <where>  
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="vehicleModel != null  and vehicleModel != ''"> and vehicle_model like concat('%', #{vehicleModel}, '%')</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="projectPhase != null  and projectPhase != ''"> and project_phase = #{projectPhase}</if>
            <if test="vehicleStatus != null  and vehicleStatus != ''"> and vehicle_status = #{vehicleStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleInfoByVehicleId" parameterType="Long" resultMap="VehicleInfoResult">
        <include refid="selectVehicleInfoVo"/>
        where vehicle_id = #{vehicleId}
    </select>

    <select id="selectVehicleInfoByStatus" parameterType="String" resultMap="VehicleInfoResult">
        <include refid="selectVehicleInfoVo"/>
        where vehicle_status = #{vehicleStatus}
        order by create_time desc
    </select>

    <select id="selectVehicleInfoByType" parameterType="String" resultMap="VehicleInfoResult">
        <include refid="selectVehicleInfoVo"/>
        where vehicle_type = #{vehicleType}
        order by create_time desc
    </select>

    <select id="selectAvailableVehicleList" resultMap="VehicleInfoResult">
        <include refid="selectVehicleInfoVo"/>
        where vehicle_status = '可用'
        order by create_time desc
    </select>
        
    <insert id="insertVehicleInfo" parameterType="VehicleInfo" useGeneratedKeys="true" keyProperty="vehicleId">
        insert into vehicle_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="driverPhone != null">driver_phone,</if>
            <if test="commanderName != null">commander_name,</if>
            <if test="commanderPhone != null">commander_phone,</if>
            <if test="entryTime != null">entry_time,</if>
            <if test="projectPhase != null">project_phase,</if>
            <if test="vehicleStatus != null">vehicle_status,</if>
            <if test="shiftConfirmer != null">shift_confirmer,</if>
            <if test="costUnit != null">cost_unit,</if>
            <if test="vehicleWeight != null">vehicle_weight,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="costPerHour != null">cost_per_hour,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">#{vehicleModel},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="driverPhone != null">#{driverPhone},</if>
            <if test="commanderName != null">#{commanderName},</if>
            <if test="commanderPhone != null">#{commanderPhone},</if>
            <if test="entryTime != null">#{entryTime},</if>
            <if test="projectPhase != null">#{projectPhase},</if>
            <if test="vehicleStatus != null">#{vehicleStatus},</if>
            <if test="shiftConfirmer != null">#{shiftConfirmer},</if>
            <if test="costUnit != null">#{costUnit},</if>
            <if test="vehicleWeight != null">#{vehicleWeight},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="costPerHour != null">#{costPerHour},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleInfo" parameterType="VehicleInfo">
        update vehicle_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model = #{vehicleModel},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="driverPhone != null">driver_phone = #{driverPhone},</if>
            <if test="commanderName != null">commander_name = #{commanderName},</if>
            <if test="commanderPhone != null">commander_phone = #{commanderPhone},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="projectPhase != null">project_phase = #{projectPhase},</if>
            <if test="vehicleStatus != null">vehicle_status = #{vehicleStatus},</if>
            <if test="shiftConfirmer != null">shift_confirmer = #{shiftConfirmer},</if>
            <if test="costUnit != null">cost_unit = #{costUnit},</if>
            <if test="vehicleWeight != null">vehicle_weight = #{vehicleWeight},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="costPerHour != null">cost_per_hour = #{costPerHour},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where vehicle_id = #{vehicleId}
    </update>

    <delete id="deleteVehicleInfoByVehicleId" parameterType="Long">
        delete from vehicle_info where vehicle_id = #{vehicleId}
    </delete>

    <delete id="deleteVehicleInfoByVehicleIds" parameterType="String">
        delete from vehicle_info where vehicle_id in 
        <foreach item="vehicleId" collection="array" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </delete>
</mapper>
