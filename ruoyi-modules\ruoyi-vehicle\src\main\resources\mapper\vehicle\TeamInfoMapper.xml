<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.TeamInfoMapper">
    
    <resultMap type="TeamInfo" id="TeamInfoResult">
        <result property="teamId"    column="team_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="teamCode"    column="team_code"    />
        <result property="teamLeader"    column="team_leader"    />
        <result property="leaderPhone"    column="leader_phone"    />
        <result property="teamType"    column="team_type"    />
        <result property="workArea"    column="work_area"    />
        <result property="memberCount"    column="member_count"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeamInfoVo">
        select team_id, team_name, team_code, team_leader, leader_phone, team_type, 
               work_area, member_count, status, create_by, create_time, update_by, update_time, remark 
        from team_info
    </sql>

    <select id="selectTeamInfoList" parameterType="TeamInfo" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        <where>  
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="teamCode != null  and teamCode != ''"> and team_code = #{teamCode}</if>
            <if test="teamLeader != null  and teamLeader != ''"> and team_leader like concat('%', #{teamLeader}, '%')</if>
            <if test="teamType != null  and teamType != ''"> and team_type = #{teamType}</if>
            <if test="workArea != null  and workArea != ''"> and work_area like concat('%', #{workArea}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeamInfoByTeamId" parameterType="Long" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        where team_id = #{teamId}
    </select>

    <select id="selectTeamInfoByTeamCode" parameterType="String" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        where team_code = #{teamCode}
    </select>

    <select id="selectTeamInfoByStatus" parameterType="String" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        where status = #{status}
        order by team_name asc
    </select>

    <select id="selectTeamInfoByType" parameterType="String" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        where team_type = #{teamType}
        order by team_name asc
    </select>

    <select id="selectActiveTeams" resultMap="TeamInfoResult">
        <include refid="selectTeamInfoVo"/>
        where status = 'active'
        order by team_name asc
    </select>

    <select id="selectTeamOptions" resultType="map">
        select team_id as value, team_name as label, team_leader, leader_phone
        from team_info
        where status = 'active'
        order by team_name asc
    </select>
        
    <insert id="insertTeamInfo" parameterType="TeamInfo" useGeneratedKeys="true" keyProperty="teamId">
        insert into team_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name,</if>
            <if test="teamCode != null and teamCode != ''">team_code,</if>
            <if test="teamLeader != null and teamLeader != ''">team_leader,</if>
            <if test="leaderPhone != null and leaderPhone != ''">leader_phone,</if>
            <if test="teamType != null and teamType != ''">team_type,</if>
            <if test="workArea != null and workArea != ''">work_area,</if>
            <if test="memberCount != null">member_count,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">#{teamName},</if>
            <if test="teamCode != null and teamCode != ''">#{teamCode},</if>
            <if test="teamLeader != null and teamLeader != ''">#{teamLeader},</if>
            <if test="leaderPhone != null and leaderPhone != ''">#{leaderPhone},</if>
            <if test="teamType != null and teamType != ''">#{teamType},</if>
            <if test="workArea != null and workArea != ''">#{workArea},</if>
            <if test="memberCount != null">#{memberCount},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTeamInfo" parameterType="TeamInfo">
        update team_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null and teamName != ''">team_name = #{teamName},</if>
            <if test="teamCode != null and teamCode != ''">team_code = #{teamCode},</if>
            <if test="teamLeader != null and teamLeader != ''">team_leader = #{teamLeader},</if>
            <if test="leaderPhone != null and leaderPhone != ''">leader_phone = #{leaderPhone},</if>
            <if test="teamType != null and teamType != ''">team_type = #{teamType},</if>
            <if test="workArea != null and workArea != ''">work_area = #{workArea},</if>
            <if test="memberCount != null">member_count = #{memberCount},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where team_id = #{teamId}
    </update>

    <delete id="deleteTeamInfoByTeamId" parameterType="Long">
        delete from team_info where team_id = #{teamId}
    </delete>

    <delete id="deleteTeamInfoByTeamIds" parameterType="String">
        delete from team_info where team_id in 
        <foreach item="teamId" collection="array" open="(" separator="," close=")">
            #{teamId}
        </foreach>
    </delete>
</mapper>
