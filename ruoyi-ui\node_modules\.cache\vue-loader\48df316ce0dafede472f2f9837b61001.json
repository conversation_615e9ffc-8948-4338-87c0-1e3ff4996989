{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue", "mtime": 1754144052418}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFBlbmRpbmdDb25maXJtT3JkZXJzLCBnZXRPcmRlciwgZGlzcGF0Y2hDb25maXJtT3JkZXIsIGNhbGN1bGF0ZU9yZGVyQ29zdCB9IGZyb20gIkAvYXBpL3ZlaGljbGUvb3JkZXIiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJEaXNwYXRjaENvbmZpcm0iLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDorqLljZXliJfooagKICAgICAgb3JkZXJMaXN0OiBbXSwKICAgICAgLy8g57uf6K6h5L+h5oGvCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICBwZW5kaW5nQ291bnQ6IDAsCiAgICAgICAgY29uZmlybWVkQ291bnQ6IDAsCiAgICAgICAgb3ZlcjUwVG9uQ291bnQ6IDAsCiAgICAgICAgdG9kYXlDb3VudDogMAogICAgICB9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBvcmRlclN0YXR1czogJ3RlYW1fY29uZmlybWVkJwogICAgICB9LAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g6K+m5oOF5a+56K+d5qGGCiAgICAgIGRldGFpbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkZXRhaWxPcmRlcjoge30sCiAgICAgIC8vIOi0ueeUqOiuoeeul+WvueivneahhgogICAgICBjb3N0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGNvc3RMb2FkaW5nOiBmYWxzZSwKICAgICAgY3VycmVudE9yZGVyOiB7fSwKICAgICAgY29zdEZvcm06IHsKICAgICAgICBjb3N0VW5pdDogJ2hvdXInLAogICAgICAgIHVuaXRQcmljZTogMCwKICAgICAgICBjb3N0QmVhcmVyOiAncHJvamVjdCcKICAgICAgfSwKICAgICAgLy8g6LS555So6K6h566X6KGo5Y2V6aqM6K+B6KeE5YiZCiAgICAgIGNvc3RSdWxlczogewogICAgICAgIHVuaXRQcmljZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeWNleS7tyIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLjAxLCBtZXNzYWdlOiAi5Y2V5Lu35b+F6aG75aSn5LqOMCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBjb3N0QmVhcmVyOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6LS555So5om/5ouF5pa5IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMubG9hZFN0YXRpc3RpY3MoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LorqLljZXliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0ge307CiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siYmVnaW5BY3R1YWxFbmRUaW1lIl0gPSB0aGlzLmRhdGVSYW5nZVswXTsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtc1siZW5kQWN0dWFsRW5kVGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgCiAgICAgIGdldFBlbmRpbmdDb25maXJtT3JkZXJzKCdkaXNwYXRjaCcpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMub3JkZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS5sZW5ndGg7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOWKoOi9vee7n+iuoeS/oeaBryAqLwogICAgbG9hZFN0YXRpc3RpY3MoKSB7CiAgICAgIC8vIFRPRE86IOiwg+eUqOe7n+iuoeaOpeWPowogICAgICB0aGlzLnN0YXRpc3RpY3MgPSB7CiAgICAgICAgcGVuZGluZ0NvdW50OiA4LAogICAgICAgIGNvbmZpcm1lZENvdW50OiAxNSwKICAgICAgICBvdmVyNTBUb25Db3VudDogMywKICAgICAgICB0b2RheUNvdW50OiA1CiAgICAgIH07CiAgICB9LAogICAgCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAKICAgIC8qKiDliLfmlrDliJfooaggKi8KICAgIHJlZnJlc2hMaXN0KCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgdGhpcy5sb2FkU3RhdGlzdGljcygpOwogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliLfmlrDmiJDlip8iKTsKICAgIH0sCiAgICAKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9yZGVySWQpCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIAogICAgLyoqIOafpeeci+ivpuaDhSAqLwogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgZ2V0T3JkZXIocm93Lm9yZGVySWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZGV0YWlsT3JkZXIgPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOehruiupOiuouWNlSAqLwogICAgaGFuZGxlQ29uZmlybShyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6K+l6K6i5Y2V55qE6LS555So5ZKM55So6L2m5oOF5Ya177yfJykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGRpc3BhdGNoQ29uZmlybU9yZGVyKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi56Gu6K6k5oiQ5YqfIik7CiAgICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAKICAgIC8qKiDotLnnlKjorqHnrpcgKi8KICAgIGhhbmRsZUNvc3RDYWxjdWxhdGUocm93KSB7CiAgICAgIHRoaXMuY3VycmVudE9yZGVyID0gcm93OwogICAgICB0aGlzLmNvc3REaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5jb3N0Rm9ybSA9IHsKICAgICAgICBjb3N0VW5pdDogJ2hvdXInLAogICAgICAgIHVuaXRQcmljZTogMCwKICAgICAgICBjb3N0QmVhcmVyOiByb3cudmVoaWNsZVdlaWdodCA+PSA1MCA/ICdwcm9qZWN0JyA6ICd0ZWFtJwogICAgICB9OwogICAgfSwKICAgIAogICAgLyoqIOaPkOS6pOi0ueeUqOiuoeeulyAqLwogICAgc3VibWl0Q29zdENhbGN1bGF0aW9uKCkgewogICAgICB0aGlzLiRyZWZzWyJjb3N0Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuY29zdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICAgICAgb3JkZXJJZDogdGhpcy5jdXJyZW50T3JkZXIub3JkZXJJZCwKICAgICAgICAgICAgY29zdFVuaXQ6IHRoaXMuY29zdEZvcm0uY29zdFVuaXQsCiAgICAgICAgICAgIHVuaXRQcmljZTogdGhpcy5jb3N0Rm9ybS51bml0UHJpY2UsCiAgICAgICAgICAgIGNvc3RCZWFyZXI6IHRoaXMuY29zdEZvcm0uY29zdEJlYXJlciwKICAgICAgICAgICAgdG90YWxDb3N0OiB0aGlzLmNhbGN1bGF0ZVRvdGFsQ29zdCgpCiAgICAgICAgICB9OwogICAgICAgICAgCiAgICAgICAgICBjYWxjdWxhdGVPcmRlckNvc3QoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIui0ueeUqOiuoeeul+aIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmNvc3REaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuZGV0YWlsRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy5jb3N0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDorqHnrpfmgLvotLnnlKggKi8KICAgIGNhbGN1bGF0ZVRvdGFsQ29zdCgpIHsKICAgICAgaWYgKCF0aGlzLmN1cnJlbnRPcmRlci5hY3R1YWxTdGFydFRpbWUgfHwgIXRoaXMuY3VycmVudE9yZGVyLmFjdHVhbEVuZFRpbWUgfHwgIXRoaXMuY29zdEZvcm0udW5pdFByaWNlKSB7CiAgICAgICAgcmV0dXJuIDA7CiAgICAgIH0KICAgICAgCiAgICAgIGNvbnN0IGRpZmYgPSBuZXcgRGF0ZSh0aGlzLmN1cnJlbnRPcmRlci5hY3R1YWxFbmRUaW1lKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZSh0aGlzLmN1cnJlbnRPcmRlci5hY3R1YWxTdGFydFRpbWUpLmdldFRpbWUoKTsKICAgICAgbGV0IGR1cmF0aW9uID0gMDsKICAgICAgCiAgICAgIGlmICh0aGlzLmNvc3RGb3JtLmNvc3RVbml0ID09PSAnaG91cicpIHsKICAgICAgICBkdXJhdGlvbiA9IE1hdGguY2VpbChkaWZmIC8gKDEwMDAgKiA2MCAqIDYwKSk7IC8vIOWQkeS4iuWPluaVtOWwj+aXtgogICAgICB9IGVsc2UgewogICAgICAgIGR1cmF0aW9uID0gTWF0aC5jZWlsKGRpZmYgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOyAvLyDlkJHkuIrlj5bmlbTlpKkKICAgICAgfQogICAgICAKICAgICAgcmV0dXJuIChkdXJhdGlvbiAqIHRoaXMuY29zdEZvcm0udW5pdFByaWNlKS50b0ZpeGVkKDIpOwogICAgfSwKICAgIAogICAgLyoqIOaJuemHj+ehruiupCAqLwogICAgaGFuZGxlQmF0Y2hDb25maXJtKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeehruiupOeahOiuouWNlSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybShg56Gu6K6k6YCJ5Lit55qEICR7dGhpcy5pZHMubGVuZ3RofSDkuKrorqLljZXvvJ9gKS50aGVuKCgpID0+IHsKICAgICAgICAvLyBUT0RPOiDosIPnlKjmibnph4/noa7orqTmjqXlj6MKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmibnph4/noa7orqTmiJDlip8iKTsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIAogICAgLyoqIOaYvuekuui0ueeUqOiuoeeulyAqLwogICAgc2hvd0Nvc3RDYWxjdWxhdGlvbigpIHsKICAgICAgdGhpcy4kbW9kYWwubXNnSW5mbygi6LS555So6K6h566X5Yqf6IO95byA5Y+R5LitLi4uIik7CiAgICB9LAogICAgCiAgICAvKiog5a+85Ye65pWw5o2uICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuJG1vZGFsLm1zZ0luZm8oIuWvvOWHuuWKn+iDveW8gOWPkeS4rS4uLiIpOwogICAgfSwKICAgIAogICAgLyoqIOiuoeeul+eUqOi9puaXtumVvyAqLwogICAgY2FsY3VsYXRlRHVyYXRpb24oc3RhcnRUaW1lLCBlbmRUaW1lKSB7CiAgICAgIGlmICghc3RhcnRUaW1lIHx8ICFlbmRUaW1lKSByZXR1cm4gJzDlsI/ml7YnOwogICAgICAKICAgICAgY29uc3QgZGlmZiA9IG5ldyBEYXRlKGVuZFRpbWUpLmdldFRpbWUoKSAtIG5ldyBEYXRlKHN0YXJ0VGltZSkuZ2V0VGltZSgpOwogICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IoZGlmZiAvICgxMDAwICogNjAgKiA2MCkpOwogICAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoZGlmZiAlICgxMDAwICogNjAgKiA2MCkpIC8gKDEwMDAgKiA2MCkpOwogICAgICAKICAgICAgcmV0dXJuIGAke2hvdXJzfeWwj+aXtiR7bWludXRlc33liIbpkp9gOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlui0ueeUqOaJv+aLheaWueaWh+acrCAqLwogICAgZ2V0Q29zdEJlYXJlclRleHQoY29zdEJlYXJlcikgewogICAgICBjb25zdCBiZWFyZXJNYXAgPSB7CiAgICAgICAgJ3Byb2plY3QnOiAn6aG555uu5om/5ouFJywKICAgICAgICAndGVhbSc6ICfpmJ/kvI3mib/mi4UnCiAgICAgIH07CiAgICAgIHJldHVybiBiZWFyZXJNYXBbY29zdEJlYXJlcl0gfHwgJ+acquefpSc7CiAgICB9LAogICAgCiAgICAvKiog6I635Y+W54q25oCB5qCH562+57G75Z6LICovCiAgICBnZXRTdGF0dXNUYWdUeXBlKHN0YXR1cykgewogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7CiAgICAgICAgJ3RlYW1fY29uZmlybWVkJzogJ3dhcm5pbmcnLAogICAgICAgICdkaXNwYXRjaF9jb25maXJtZWQnOiAnc3VjY2VzcycsCiAgICAgICAgJ3BlbmRpbmdfbWFuYWdlcic6ICdwcmltYXJ5JwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICd0ZWFtX2NvbmZpcm1lZCc6ICfpmJ/kvI3lt7Lnoa7orqQnLAogICAgICAgICdkaXNwYXRjaF9jb25maXJtZWQnOiAn6LCD5bqm5bey56Gu6K6kJywKICAgICAgICAncGVuZGluZ19tYW5hZ2VyJzogJ+W+heS4u+euoeWuoeaJuScKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6UnOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["dispatch-confirm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dispatch-confirm.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">项目调度室 - 台班确认</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.confirmedCount }}</div>\n              <div class=\"stat-label\">已确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.over50TonCount }}</div>\n              <div class=\"stat-label\">超50吨待审</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日处理</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n            <el-option label=\"调度已确认\" value=\"dispatch_confirmed\"></el-option>\n            <el-option label=\"待主管审批\" value=\"pending_manager\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆重量\" prop=\"vehicleWeight\">\n          <el-select v-model=\"queryParams.vehicleWeight\" placeholder=\"请选择重量范围\" clearable @change=\"getList\">\n            <el-option label=\"50吨以下\" value=\"under_50\"></el-option>\n            <el-option label=\"50吨及以上\" value=\"over_50\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"费用承担\" prop=\"costBearer\">\n          <el-select v-model=\"queryParams.costBearer\" placeholder=\"请选择费用承担方\" clearable @change=\"getList\">\n            <el-option label=\"项目承担\" value=\"project\"></el-option>\n            <el-option label=\"队伍承担\" value=\"team\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchConfirm\"\n            v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n          >批量确认</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-money\"\n            size=\"mini\"\n            @click=\"showCostCalculation\"\n          >费用计算</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-download\"\n            size=\"mini\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['vehicle:order:export']\"\n          >导出数据</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n            <div style=\"color: #E6A23C; font-size: 12px;\" v-if=\"scope.row.vehicleWeight >= 50\">\n              重量：{{ scope.row.vehicleWeight }}吨\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"队伍信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.driverName }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"费用信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.totalCost\">\n              <div style=\"color: #67C23A; font-weight: bold;\">￥{{ scope.row.totalCost }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ getCostBearerText(scope.row.costBearer) }}</div>\n            </div>\n            <div v-else style=\"color: #E6A23C;\">待计算</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleConfirm(scope.row)\"\n              v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n            >确认</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleCostCalculate(scope.row)\"\n              v-hasPermi=\"['vehicle:order:cost-calculate']\"\n            >计算费用</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"车辆重量\">{{ detailOrder.vehicleWeight }}吨</el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用信息\">\n          <div v-if=\"detailOrder.totalCost\">\n            <div>总费用：<span style=\"color: #67C23A; font-weight: bold;\">￥{{ detailOrder.totalCost }}</span></div>\n            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>\n            <div>计量单位：{{ detailOrder.costUnit }}</div>\n            <div>单价：￥{{ detailOrder.unitPrice }}</div>\n          </div>\n          <div v-else style=\"color: #E6A23C;\">费用待计算</div>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 确认操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'team_confirmed'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">调度室确认</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleCostCalculate(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-money\"></i> 计算费用\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleConfirm(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 确认订单\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 费用计算对话框 -->\n    <el-dialog title=\"费用计算\" :visible.sync=\"costDialogVisible\" width=\"600px\" append-to-body>\n      <el-form ref=\"costForm\" :model=\"costForm\" :rules=\"costRules\" label-width=\"120px\">\n        <el-form-item label=\"车辆信息\">\n          <div class=\"cost-info\">\n            <div>车辆型号：{{ currentOrder.vehicleInfo ? currentOrder.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div>车辆重量：{{ currentOrder.vehicleWeight }}吨</div>\n            <div>用车时长：{{ calculateDuration(currentOrder.actualStartTime, currentOrder.actualEndTime) }}</div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"计量单位\" prop=\"costUnit\">\n          <el-radio-group v-model=\"costForm.costUnit\">\n            <el-radio label=\"hour\">小时</el-radio>\n            <el-radio label=\"day\">天</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"单价\" prop=\"unitPrice\">\n          <el-input-number\n            v-model=\"costForm.unitPrice\"\n            :precision=\"2\"\n            :min=\"0\"\n            :max=\"99999\"\n            placeholder=\"请输入单价\"\n            style=\"width: 200px;\">\n          </el-input-number>\n          <span style=\"margin-left: 10px;\">元/{{ costForm.costUnit === 'hour' ? '小时' : '天' }}</span>\n        </el-form-item>\n        \n        <el-form-item label=\"费用承担方\" prop=\"costBearer\">\n          <el-radio-group v-model=\"costForm.costBearer\">\n            <el-radio label=\"project\">项目承担</el-radio>\n            <el-radio label=\"team\">队伍承担</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"预计总费用\">\n          <div class=\"cost-preview\">\n            <span style=\"font-size: 18px; color: #67C23A; font-weight: bold;\">\n              ￥{{ calculateTotalCost() }}\n            </span>\n          </div>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"costDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitCostCalculation\" :loading=\"costLoading\">确认计算</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, dispatchConfirmOrder, calculateOrderCost } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"DispatchConfirm\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        confirmedCount: 0,\n        over50TonCount: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'team_confirmed'\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 费用计算对话框\n      costDialogVisible: false,\n      costLoading: false,\n      currentOrder: {},\n      costForm: {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: 'project'\n      },\n      // 费用计算表单验证规则\n      costRules: {\n        unitPrice: [\n          { required: true, message: \"请输入单价\", trigger: \"blur\" },\n          { type: 'number', min: 0.01, message: \"单价必须大于0\", trigger: \"blur\" }\n        ],\n        costBearer: [\n          { required: true, message: \"请选择费用承担方\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('dispatch').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 8,\n        confirmedCount: 15,\n        over50TonCount: 3,\n        todayCount: 5\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 确认订单 */\n    handleConfirm(row) {\n      this.$modal.confirm('确认该订单的费用和用车情况？').then(() => {\n        return dispatchConfirmOrder(row.orderId);\n      }).then(() => {\n        this.$modal.msgSuccess(\"确认成功\");\n        this.detailDialogVisible = false;\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 费用计算 */\n    handleCostCalculate(row) {\n      this.currentOrder = row;\n      this.costDialogVisible = true;\n      this.costForm = {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: row.vehicleWeight >= 50 ? 'project' : 'team'\n      };\n    },\n    \n    /** 提交费用计算 */\n    submitCostCalculation() {\n      this.$refs[\"costForm\"].validate(valid => {\n        if (valid) {\n          this.costLoading = true;\n          const data = {\n            orderId: this.currentOrder.orderId,\n            costUnit: this.costForm.costUnit,\n            unitPrice: this.costForm.unitPrice,\n            costBearer: this.costForm.costBearer,\n            totalCost: this.calculateTotalCost()\n          };\n          \n          calculateOrderCost(data).then(response => {\n            this.$modal.msgSuccess(\"费用计算成功\");\n            this.costDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.costLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 计算总费用 */\n    calculateTotalCost() {\n      if (!this.currentOrder.actualStartTime || !this.currentOrder.actualEndTime || !this.costForm.unitPrice) {\n        return 0;\n      }\n      \n      const diff = new Date(this.currentOrder.actualEndTime).getTime() - new Date(this.currentOrder.actualStartTime).getTime();\n      let duration = 0;\n      \n      if (this.costForm.costUnit === 'hour') {\n        duration = Math.ceil(diff / (1000 * 60 * 60)); // 向上取整小时\n      } else {\n        duration = Math.ceil(diff / (1000 * 60 * 60 * 24)); // 向上取整天\n      }\n      \n      return (duration * this.costForm.unitPrice).toFixed(2);\n    },\n    \n    /** 批量确认 */\n    handleBatchConfirm() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要确认的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量确认接口\n        this.$modal.msgSuccess(\"批量确认成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 显示费用计算 */\n    showCostCalculation() {\n      this.$modal.msgInfo(\"费用计算功能开发中...\");\n    },\n    \n    /** 导出数据 */\n    handleExport() {\n      this.$modal.msgInfo(\"导出功能开发中...\");\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取费用承担方文本 */\n    getCostBearerText(costBearer) {\n      const bearerMap = {\n        'project': '项目承担',\n        'team': '队伍承担'\n      };\n      return bearerMap[costBearer] || '未知';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'team_confirmed': 'warning',\n        'dispatch_confirmed': 'success',\n        'pending_manager': 'primary'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'team_confirmed': '队伍已确认',\n        'dispatch_confirmed': '调度已确认',\n        'pending_manager': '待主管审批'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.cost-info {\n  background: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  line-height: 1.8;\n}\n\n.cost-preview {\n  background: #f0f9ff;\n  padding: 15px;\n  border-radius: 4px;\n  text-align: center;\n  border: 1px solid #b3d8ff;\n}\n</style>\n"]}]}