package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 用车订单对象 vehicle_order
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 申请ID */
    @Excel(name = "申请ID")
    private Long applicationId;

    /** 申请信息 */
    private VehicleApplication applicationInfo;

    /** 车辆ID */
    @Excel(name = "车辆ID")
    private Long vehicleId;

    /** 车辆信息 */
    private VehicleInfo vehicleInfo;

    /** 队伍ID */
    @Excel(name = "队伍ID")
    private Long teamId;

    /** 队伍信息 */
    private TeamInfo teamInfo;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    private String driverName;

    /** 用车地点 */
    @Excel(name = "用车地点")
    private String usageLocation;

    /** 计划开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date plannedStartTime;

    /** 计划结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date plannedEndTime;

    /** 实际开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartTime;

    /** 实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;

    /** 开始拍照URL */
    @Excel(name = "开始拍照URL")
    private String startPhotoUrl;

    /** 结束拍照URL */
    @Excel(name = "结束拍照URL")
    private String endPhotoUrl;

    /** 订单状态 */
    @Excel(name = "订单状态", readConverterExp = "待开始=pending,进行中=running,司机已结束=driver_finished,队伍已确认=team_confirmed,调度室已确认=dispatch_confirmed,主管已确认=manager_confirmed,已完成=completed")
    private String orderStatus;

    /** 队伍确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "队伍确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date teamConfirmTime;

    /** 队伍确认人 */
    @Excel(name = "队伍确认人")
    private String teamConfirmPerson;

    /** 调度室确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "调度室确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dispatchConfirmTime;

    /** 调度室确认人 */
    @Excel(name = "调度室确认人")
    private String dispatchConfirmPerson;

    /** 主管确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "主管确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date managerConfirmTime;

    /** 主管确认人 */
    @Excel(name = "主管确认人")
    private String managerConfirmPerson;

    /** 退回原因 */
    @Excel(name = "退回原因")
    private String rejectReason;

    /** 车辆重量（吨） */
    @Excel(name = "车辆重量")
    private BigDecimal vehicleWeight;

    /** 费用计量单位 */
    @Excel(name = "费用计量单位")
    private String costUnit;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 实际用车时长 */
    @Excel(name = "实际用车时长")
    private BigDecimal actualDuration;

    /** 总费用 */
    @Excel(name = "总费用")
    private BigDecimal totalCost;

    /** 费用承担方 */
    @Excel(name = "费用承担方")
    private String costBearer;

    /** 费用状态 */
    @Excel(name = "费用状态")
    private String costStatus;

    /** 费用计算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "费用计算时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date costCalculateTime;

    /** 费用确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "费用确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date costConfirmTime;

    /** 费用确认人 */
    @Excel(name = "费用确认人")
    private String costConfirmPerson;

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    @NotNull(message = "申请ID不能为空")
    public Long getApplicationId() 
    {
        return applicationId;
    }

    public void setApplicationInfo(VehicleApplication applicationInfo) 
    {
        this.applicationInfo = applicationInfo;
    }

    public VehicleApplication getApplicationInfo() 
    {
        return applicationInfo;
    }

    public void setVehicleId(Long vehicleId) 
    {
        this.vehicleId = vehicleId;
    }

    @NotNull(message = "车辆ID不能为空")
    public Long getVehicleId() 
    {
        return vehicleId;
    }

    public void setVehicleInfo(VehicleInfo vehicleInfo) 
    {
        this.vehicleInfo = vehicleInfo;
    }

    public VehicleInfo getVehicleInfo() 
    {
        return vehicleInfo;
    }

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    @NotNull(message = "队伍ID不能为空")
    public Long getTeamId() 
    {
        return teamId;
    }

    public void setTeamInfo(TeamInfo teamInfo) 
    {
        this.teamInfo = teamInfo;
    }

    public TeamInfo getTeamInfo() 
    {
        return teamInfo;
    }

    public void setDriverName(String driverName) 
    {
        this.driverName = driverName;
    }

    @NotBlank(message = "司机姓名不能为空")
    @Size(min = 0, max = 50, message = "司机姓名长度不能超过50个字符")
    public String getDriverName() 
    {
        return driverName;
    }

    public void setUsageLocation(String usageLocation) 
    {
        this.usageLocation = usageLocation;
    }

    @NotBlank(message = "用车地点不能为空")
    @Size(min = 0, max = 200, message = "用车地点长度不能超过200个字符")
    public String getUsageLocation() 
    {
        return usageLocation;
    }

    public void setPlannedStartTime(Date plannedStartTime) 
    {
        this.plannedStartTime = plannedStartTime;
    }

    @NotNull(message = "计划开始时间不能为空")
    public Date getPlannedStartTime() 
    {
        return plannedStartTime;
    }

    public void setPlannedEndTime(Date plannedEndTime) 
    {
        this.plannedEndTime = plannedEndTime;
    }

    @NotNull(message = "计划结束时间不能为空")
    public Date getPlannedEndTime() 
    {
        return plannedEndTime;
    }

    public void setActualStartTime(Date actualStartTime) 
    {
        this.actualStartTime = actualStartTime;
    }

    public Date getActualStartTime() 
    {
        return actualStartTime;
    }

    public void setActualEndTime(Date actualEndTime) 
    {
        this.actualEndTime = actualEndTime;
    }

    public Date getActualEndTime() 
    {
        return actualEndTime;
    }

    public void setStartPhotoUrl(String startPhotoUrl) 
    {
        this.startPhotoUrl = startPhotoUrl;
    }

    @Size(min = 0, max = 500, message = "开始拍照URL长度不能超过500个字符")
    public String getStartPhotoUrl() 
    {
        return startPhotoUrl;
    }

    public void setEndPhotoUrl(String endPhotoUrl) 
    {
        this.endPhotoUrl = endPhotoUrl;
    }

    @Size(min = 0, max = 500, message = "结束拍照URL长度不能超过500个字符")
    public String getEndPhotoUrl() 
    {
        return endPhotoUrl;
    }

    public void setOrderStatus(String orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    @Size(min = 0, max = 20, message = "订单状态长度不能超过20个字符")
    public String getOrderStatus() 
    {
        return orderStatus;
    }

    public void setTeamConfirmTime(Date teamConfirmTime) 
    {
        this.teamConfirmTime = teamConfirmTime;
    }

    public Date getTeamConfirmTime() 
    {
        return teamConfirmTime;
    }

    public void setTeamConfirmPerson(String teamConfirmPerson) 
    {
        this.teamConfirmPerson = teamConfirmPerson;
    }

    @Size(min = 0, max = 50, message = "队伍确认人长度不能超过50个字符")
    public String getTeamConfirmPerson() 
    {
        return teamConfirmPerson;
    }

    public void setDispatchConfirmTime(Date dispatchConfirmTime) 
    {
        this.dispatchConfirmTime = dispatchConfirmTime;
    }

    public Date getDispatchConfirmTime() 
    {
        return dispatchConfirmTime;
    }

    public void setDispatchConfirmPerson(String dispatchConfirmPerson) 
    {
        this.dispatchConfirmPerson = dispatchConfirmPerson;
    }

    @Size(min = 0, max = 50, message = "调度室确认人长度不能超过50个字符")
    public String getDispatchConfirmPerson() 
    {
        return dispatchConfirmPerson;
    }

    public void setManagerConfirmTime(Date managerConfirmTime) 
    {
        this.managerConfirmTime = managerConfirmTime;
    }

    public Date getManagerConfirmTime() 
    {
        return managerConfirmTime;
    }

    public void setManagerConfirmPerson(String managerConfirmPerson) 
    {
        this.managerConfirmPerson = managerConfirmPerson;
    }

    @Size(min = 0, max = 50, message = "主管确认人长度不能超过50个字符")
    public String getManagerConfirmPerson() 
    {
        return managerConfirmPerson;
    }

    public void setRejectReason(String rejectReason) 
    {
        this.rejectReason = rejectReason;
    }

    public String getRejectReason() 
    {
        return rejectReason;
    }

    public void setVehicleWeight(BigDecimal vehicleWeight) 
    {
        this.vehicleWeight = vehicleWeight;
    }

    public BigDecimal getVehicleWeight()
    {
        return vehicleWeight;
    }

    public void setCostUnit(String costUnit)
    {
        this.costUnit = costUnit;
    }

    public String getCostUnit()
    {
        return costUnit;
    }

    public void setUnitPrice(BigDecimal unitPrice)
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice()
    {
        return unitPrice;
    }

    public void setActualDuration(BigDecimal actualDuration)
    {
        this.actualDuration = actualDuration;
    }

    public BigDecimal getActualDuration()
    {
        return actualDuration;
    }

    public void setTotalCost(BigDecimal totalCost)
    {
        this.totalCost = totalCost;
    }

    public BigDecimal getTotalCost()
    {
        return totalCost;
    }

    public void setCostBearer(String costBearer)
    {
        this.costBearer = costBearer;
    }

    public String getCostBearer()
    {
        return costBearer;
    }

    public void setCostStatus(String costStatus)
    {
        this.costStatus = costStatus;
    }

    public String getCostStatus()
    {
        return costStatus;
    }

    public void setCostCalculateTime(Date costCalculateTime)
    {
        this.costCalculateTime = costCalculateTime;
    }

    public Date getCostCalculateTime()
    {
        return costCalculateTime;
    }

    public void setCostConfirmTime(Date costConfirmTime)
    {
        this.costConfirmTime = costConfirmTime;
    }

    public Date getCostConfirmTime()
    {
        return costConfirmTime;
    }

    public void setCostConfirmPerson(String costConfirmPerson)
    {
        this.costConfirmPerson = costConfirmPerson;
    }

    public String getCostConfirmPerson()
    {
        return costConfirmPerson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("applicationId", getApplicationId())
            .append("vehicleId", getVehicleId())
            .append("teamId", getTeamId())
            .append("driverName", getDriverName())
            .append("usageLocation", getUsageLocation())
            .append("plannedStartTime", getPlannedStartTime())
            .append("plannedEndTime", getPlannedEndTime())
            .append("actualStartTime", getActualStartTime())
            .append("actualEndTime", getActualEndTime())
            .append("startPhotoUrl", getStartPhotoUrl())
            .append("endPhotoUrl", getEndPhotoUrl())
            .append("orderStatus", getOrderStatus())
            .append("teamConfirmTime", getTeamConfirmTime())
            .append("teamConfirmPerson", getTeamConfirmPerson())
            .append("dispatchConfirmTime", getDispatchConfirmTime())
            .append("dispatchConfirmPerson", getDispatchConfirmPerson())
            .append("managerConfirmTime", getManagerConfirmTime())
            .append("managerConfirmPerson", getManagerConfirmPerson())
            .append("rejectReason", getRejectReason())
            .append("vehicleWeight", getVehicleWeight())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
