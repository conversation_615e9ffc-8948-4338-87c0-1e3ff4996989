{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\approve.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\approve.vue", "mtime": 1754142746752}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["approve.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "approve.vue", "sourceRoot": "src/views/vehicle/demand", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">需求计划审批</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <!-- 申请信息展示 -->\n      <el-descriptions title=\"申请信息\" :column=\"2\" border>\n        <el-descriptions-item label=\"计划标题\" :span=\"2\">{{ form.planTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆类型\">{{ form.vehicleType }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆型号\">{{ form.vehicleModel }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求单位\">{{ form.demandUnit }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求数量\">{{ form.demandQuantity }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求开始时间\">{{ parseTime(form.demandStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求结束时间\">{{ parseTime(form.demandEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请人\">{{ form.applicant }}</el-descriptions-item>\n        <el-descriptions-item label=\"联系电话\">{{ form.applicantPhone }}</el-descriptions-item>\n        <el-descriptions-item label=\"当前状态\">\n          <el-tag :type=\"getStatusTagType(form.approvalStatus)\">\n            {{ getStatusText(form.approvalStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"申请时间\">{{ parseTime(form.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用途说明\" :span=\"2\">{{ form.usagePurpose }}</el-descriptions-item>\n        <el-descriptions-item v-if=\"form.remark\" label=\"备注\" :span=\"2\">{{ form.remark }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 审批流程展示 -->\n      <div style=\"margin-top: 20px;\">\n        <h3>审批流程</h3>\n        <el-steps :active=\"currentStep\" finish-status=\"success\" align-center>\n          <el-step title=\"项目调度室审批\" :description=\"getStepDescription('pending_level1')\"></el-step>\n          <el-step title=\"机械主管审批\" :description=\"getStepDescription('pending_level2')\"></el-step>\n          <el-step title=\"经营部门审批\" :description=\"getStepDescription('pending_level3')\"></el-step>\n          <el-step title=\"审批完成\" :description=\"getStepDescription('approved')\"></el-step>\n        </el-steps>\n      </div>\n\n      <!-- 审批操作 -->\n      <div v-if=\"canCurrentUserApprove\" style=\"margin-top: 30px;\">\n        <el-divider content-position=\"left\">审批操作</el-divider>\n        <el-form ref=\"approvalForm\" :model=\"approvalForm\" :rules=\"approvalRules\" label-width=\"100px\">\n          <el-form-item label=\"审批结果\" prop=\"approvalStatus\">\n            <el-radio-group v-model=\"approvalForm.approvalStatus\">\n              <el-radio label=\"approved\">通过</el-radio>\n              <el-radio label=\"rejected\">拒绝</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"审批意见\" prop=\"approvalComments\">\n            <el-input\n              v-model=\"approvalForm.approvalComments\"\n              type=\"textarea\"\n              :rows=\"4\"\n              placeholder=\"请输入审批意见\"\n              maxlength=\"500\"\n              show-word-limit />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApproval\" :loading=\"submitLoading\">\n              <i class=\"el-icon-check\"></i> 提交审批\n            </el-button>\n            <el-button @click=\"resetApprovalForm\">\n              <i class=\"el-icon-refresh-left\"></i> 重置\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 审批历史 -->\n      <div v-if=\"approvalHistory.length > 0\" style=\"margin-top: 30px;\">\n        <el-divider content-position=\"left\">审批历史</el-divider>\n        <el-timeline>\n          <el-timeline-item\n            v-for=\"(item, index) in approvalHistory\"\n            :key=\"index\"\n            :timestamp=\"parseTime(item.approvalTime)\"\n            placement=\"top\">\n            <el-card>\n              <h4>{{ item.approverName }} - {{ item.approvalLevel }}</h4>\n              <p>审批结果：\n                <el-tag :type=\"item.approvalStatus === 'approved' ? 'success' : 'danger'\" size=\"mini\">\n                  {{ item.approvalStatus === 'approved' ? '通过' : '拒绝' }}\n                </el-tag>\n              </p>\n              <p v-if=\"item.approvalComments\">审批意见：{{ item.approvalComments }}</p>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getDemand, approveDemand } from \"@/api/vehicle/demand\";\n\nexport default {\n  name: \"DemandApprove\",\n  data() {\n    return {\n      // 申请信息\n      form: {},\n      // 审批表单\n      approvalForm: {\n        approvalStatus: 'approved',\n        approvalComments: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 当前步骤\n      currentStep: 0,\n      // 是否可以审批\n      canCurrentUserApprove: false,\n      // 审批历史\n      approvalHistory: [],\n      // 审批表单验证规则\n      approvalRules: {\n        approvalStatus: [\n          { required: true, message: \"请选择审批结果\", trigger: \"change\" }\n        ],\n        approvalComments: [\n          { required: true, message: \"审批意见不能为空\", trigger: \"blur\" },\n          { min: 5, max: 500, message: \"长度在 5 到 500 个字符\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    const planId = this.$route.params.planId;\n    if (planId) {\n      this.loadPlanDetail(planId);\n    }\n  },\n  methods: {\n    /** 加载计划详情 */\n    async loadPlanDetail(planId) {\n      try {\n        const response = await getDemand(planId);\n        this.form = response.data;\n        this.updateCurrentStep();\n        this.checkApprovalPermission();\n        // TODO: 加载审批历史\n        // this.loadApprovalHistory(planId);\n      } catch (error) {\n        this.$modal.msgError(\"加载计划详情失败\");\n        this.goBack();\n      }\n    },\n    \n    /** 更新当前步骤 */\n    updateCurrentStep() {\n      const statusStepMap = {\n        'pending_level1': 0,\n        'pending_level2': 1,\n        'pending_level3': 2,\n        'approved': 3,\n        'rejected': -1\n      };\n      this.currentStep = statusStepMap[this.form.approvalStatus] || 0;\n    },\n    \n    /** 检查审批权限 */\n    checkApprovalPermission() {\n      // TODO: 根据当前用户角色和审批状态判断是否可以审批\n      // 这里简化处理，实际应该根据用户角色判断\n      const currentStatus = this.form.approvalStatus;\n      this.canCurrentUserApprove = ['pending_level1', 'pending_level2', 'pending_level3'].includes(currentStatus);\n    },\n    \n    /** 提交审批 */\n    submitApproval() {\n      this.$refs[\"approvalForm\"].validate(valid => {\n        if (valid) {\n          this.submitLoading = true;\n          const planId = this.$route.params.planId;\n          \n          approveDemand(planId, this.approvalForm).then(response => {\n            this.$modal.msgSuccess(\"审批提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 重置审批表单 */\n    resetApprovalForm() {\n      this.$refs[\"approvalForm\"].resetFields();\n    },\n    \n    /** 获取步骤描述 */\n    getStepDescription(status) {\n      if (this.form.approvalStatus === 'rejected') {\n        return '审批被拒绝';\n      }\n      \n      const statusStepMap = {\n        'pending_level1': 0,\n        'pending_level2': 1,\n        'pending_level3': 2,\n        'approved': 3\n      };\n      \n      const currentStepIndex = statusStepMap[this.form.approvalStatus];\n      const targetStepIndex = statusStepMap[status];\n      \n      if (targetStepIndex < currentStepIndex) {\n        return '已完成';\n      } else if (targetStepIndex === currentStepIndex) {\n        return '进行中';\n      } else {\n        return '待处理';\n      }\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'draft': 'info',\n        'pending_level1': 'warning',\n        'pending_level2': 'warning', \n        'pending_level3': 'warning',\n        'approved': 'success',\n        'rejected': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'draft': '草稿',\n        'pending_level1': '待项目调度室审批',\n        'pending_level2': '待机械主管审批',\n        'pending_level3': '待经营部门审批',\n        'approved': '已通过',\n        'rejected': '已拒绝'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/demand');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-descriptions {\n  margin-bottom: 20px;\n}\n\n.el-steps {\n  margin: 20px 0;\n}\n\n.el-timeline {\n  padding-left: 20px;\n}\n</style>\n"]}]}