# 🚗 车辆管理系统完整部署指南

## 📋 部署概述

本指南将帮助您完整部署车辆管理系统，包括数据库初始化、菜单配置、Nacos配置和服务启动。

---

## 🚀 第一步：数据库初始化

### 1.1 执行主数据库脚本
```sql
-- 执行若依主数据库初始化脚本
source sql/ry_20250523.sql;

-- 执行车辆管理数据库脚本
source sql/vehicle_tables.sql;
```

### 1.2 执行菜单配置脚本
```sql
-- 执行车辆管理菜单配置
source sql/vehicle_menu_final.sql;
```

### 1.3 执行Nacos配置初始化
```sql
-- 执行Nacos配置数据库初始化（已包含车辆模块配置）
source sql/ry_config_20250224.sql;
```

---

## 🔧 第二步：服务配置验证

### 2.1 验证Nacos配置
访问 `http://**************:8848/nacos`，确认以下配置存在：

- ✅ `ruoyi-gateway-dev.yml` - 包含车辆模块路由
- ✅ `ruoyi-vehicle-dev.yml` - 车辆模块配置
- ✅ `ruoyi-system-dev.yml` - 系统模块配置
- ✅ `application-dev.yml` - 通用配置
- ✅ `sentinel-ruoyi-gateway` - 包含车辆模块限流规则

### 2.2 关键配置检查

**网关路由配置**（ruoyi-gateway-dev.yml）：
```yaml
routes:
  # 车辆管理模块
  - id: ruoyi-vehicle
    uri: lb://ruoyi-vehicle
    predicates:
      - Path=/vehicle/**
    filters:
      - StripPrefix=1
```

**车辆模块配置**（ruoyi-vehicle-dev.yml）：
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        master:
          url: jdbc:mysql://**************:3306/ry-cloud?...
          username: root
          password: RuoYi@2024
```

---

## 🚀 第三步：服务启动

### 3.1 启动顺序
按以下顺序启动服务：

1. **Nacos服务** (端口: 8848)
2. **Redis服务** (端口: 6379)
3. **MySQL服务** (端口: 3306)
4. **认证服务** `ruoyi-auth` (端口: 9200)
5. **系统服务** `ruoyi-system` (端口: 9201)
6. **车辆服务** `ruoyi-vehicle` (端口: 9204) ⭐
7. **网关服务** `ruoyi-gateway` (端口: 8080)
8. **前端服务** `ruoyi-ui` (端口: 80)

### 3.2 验证服务注册
在Nacos控制台的 `服务管理` -> `服务列表` 中，确认以下服务正常注册：

- ✅ `ruoyi-auth`
- ✅ `ruoyi-system`
- ✅ `ruoyi-vehicle` ⭐
- ✅ `ruoyi-gateway`

---

## 🧪 第四步：功能测试

### 4.1 API接口测试
```bash
# 测试车辆信息接口
curl "http://localhost/dev-api/vehicle/info/list?pageNum=1&pageSize=10"

# 测试用车申请接口
curl "http://localhost/dev-api/vehicle/application/list?pageNum=1&pageSize=10"

# 测试队伍信息接口
curl "http://localhost/dev-api/vehicle/team/list?pageNum=1&pageSize=10"
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [],
  "total": 0
}
```

### 4.2 前端页面测试
1. 访问 `http://localhost`
2. 登录系统（admin/admin123）
3. 检查左侧菜单是否显示"车辆管理"
4. 逐一点击子菜单，确认页面正常加载

---

## 🎯 第五步：菜单权限验证

### 5.1 菜单结构检查
登录后应该看到：
```
🚗 车辆管理
   ├── 🚙 车辆信息
   ├── 📝 用车申请
   ├── 📋 用车订单
   ├── 👥 队伍信息
   ├── 🔧 维修记录
   ├── ⚠️ 违章记录
   └── 📊 需求计划
```

### 5.2 权限功能检查
每个页面应该包含：
- ✅ 数据列表显示
- ✅ 搜索功能
- ✅ 新增按钮
- ✅ 修改按钮
- ✅ 删除按钮
- ✅ 导出按钮

---

## 🔍 故障排除

### 问题1：API返回404错误
**原因**：服务未注册或路由配置错误
**解决**：
1. 检查Nacos服务列表
2. 确认网关路由配置
3. 重启相关服务

### 问题2：菜单不显示
**原因**：菜单SQL未执行或权限未分配
**解决**：
1. 重新执行 `sql/vehicle_menu_final.sql`
2. 检查 `sys_role_menu` 表中的权限分配
3. 重启后端服务

### 问题3：数据库连接失败
**原因**：数据库配置错误
**解决**：
1. 检查Nacos中的数据库配置
2. 确认数据库服务正常运行
3. 验证用户名密码正确

### 问题4：服务启动失败
**原因**：端口冲突或依赖服务未启动
**解决**：
1. 检查端口占用情况
2. 按正确顺序启动服务
3. 查看服务启动日志

---

## ✅ 部署完成检查清单

部署完成后，请逐项检查：

### 数据库层面
- [ ] 主数据库 `ry-cloud` 创建成功
- [ ] 车辆管理相关表创建成功
- [ ] 菜单数据插入成功
- [ ] Nacos配置数据库 `ry-config` 初始化成功

### 服务层面
- [ ] 所有服务在Nacos中正常注册
- [ ] 所有服务健康状态为UP
- [ ] 网关路由配置正确
- [ ] API接口返回正常

### 前端层面
- [ ] 车辆管理菜单正常显示
- [ ] 所有子菜单可以正常访问
- [ ] 页面功能正常使用
- [ ] 权限控制正常

---

## 🎉 部署成功

如果以上检查项都通过，恭喜您！车辆管理系统已经成功部署。

您现在可以：
- 🚙 管理车辆基础信息
- 📝 处理用车申请流程
- 📋 管理用车订单
- 👥 维护队伍信息
- 🔧 记录维修保养
- ⚠️ 处理违章记录
- 📊 制定需求计划

系统已经准备好为您的车辆管理工作提供全面支持！
