package com.ruoyi.vehicle.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.vehicle.domain.TeamInfo;

/**
 * 队伍信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface TeamInfoMapper 
{
    /**
     * 查询队伍信息
     * 
     * @param teamId 队伍信息主键
     * @return 队伍信息
     */
    public TeamInfo selectTeamInfoByTeamId(Long teamId);

    /**
     * 查询队伍信息列表
     * 
     * @param teamInfo 队伍信息
     * @return 队伍信息集合
     */
    public List<TeamInfo> selectTeamInfoList(TeamInfo teamInfo);

    /**
     * 新增队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    public int insertTeamInfo(TeamInfo teamInfo);

    /**
     * 修改队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    public int updateTeamInfo(TeamInfo teamInfo);

    /**
     * 删除队伍信息
     * 
     * @param teamId 队伍信息主键
     * @return 结果
     */
    public int deleteTeamInfoByTeamId(Long teamId);

    /**
     * 批量删除队伍信息
     * 
     * @param teamIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeamInfoByTeamIds(Long[] teamIds);

    /**
     * 根据队伍编码查询队伍信息
     * 
     * @param teamCode 队伍编码
     * @return 队伍信息
     */
    public TeamInfo selectTeamInfoByTeamCode(String teamCode);

    /**
     * 根据状态查询队伍信息
     *
     * @param status 状态
     * @return 队伍信息集合
     */
    public List<TeamInfo> selectTeamInfoByStatus(String status);

    /**
     * 根据队伍类型查询队伍信息
     *
     * @param teamType 队伍类型
     * @return 队伍信息集合
     */
    public List<TeamInfo> selectTeamInfoByType(String teamType);

    /**
     * 查询活跃队伍
     * 
     * @return 队伍信息集合
     */
    public List<TeamInfo> selectActiveTeams();

    /**
     * 查询队伍选项（用于下拉选择）
     * 
     * @return 队伍选项集合
     */
    public List<Map<String, Object>> selectTeamOptions();
}
