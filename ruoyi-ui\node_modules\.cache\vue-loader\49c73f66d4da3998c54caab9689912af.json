{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\apply.vue?vue&type=style&index=0&id=50c1c722&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\apply.vue", "mtime": 1754142812526}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZCB7CiAgbWFyZ2luOiAyMHB4Owp9CgouZWwtZm9ybS1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiAyMnB4Owp9CgouZWwtYnV0dG9uIHsKICBtYXJnaW4tcmlnaHQ6IDEwcHg7Cn0K"}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4WA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/vehicle/application", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">机械用车申请</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\" size=\"medium\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请标题\" prop=\"applicationTitle\">\n              <el-input v-model=\"form.applicationTitle\" placeholder=\"请输入申请标题\" maxlength=\"100\" show-word-limit />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请人\" prop=\"applicant\">\n              <el-input v-model=\"form.applicant\" placeholder=\"请输入申请人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\" @change=\"handleVehicleTypeChange\">\n                <el-option\n                  v-for=\"type in vehicleTypeOptions\"\n                  :key=\"type.dictValue\"\n                  :label=\"type.dictLabel\"\n                  :value=\"type.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-select v-model=\"form.vehicleModel\" placeholder=\"请选择车辆型号\" :disabled=\"!form.vehicleType\">\n                <el-option\n                  v-for=\"model in vehicleModelOptions\"\n                  :key=\"model.dictValue\"\n                  :label=\"model.dictLabel\"\n                  :value=\"model.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"队伍信息\" prop=\"teamId\">\n              <el-select v-model=\"form.teamId\" placeholder=\"请选择队伍\" @change=\"handleTeamChange\">\n                <el-option\n                  v-for=\"team in teamOptions\"\n                  :key=\"team.teamId\"\n                  :label=\"team.teamName\"\n                  :value=\"team.teamId\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"applicantPhone\">\n              <el-input v-model=\"form.applicantPhone\" placeholder=\"请输入联系电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"用车地点\" prop=\"usageLocation\">\n              <el-input v-model=\"form.usageLocation\" placeholder=\"请输入详细的用车地点\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"用车开始时间\" prop=\"startTime\">\n              <el-date-picker\n                v-model=\"form.startTime\"\n                type=\"datetime\"\n                placeholder=\"请选择用车开始时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用车结束时间\" prop=\"endTime\">\n              <el-date-picker\n                v-model=\"form.endTime\"\n                type=\"datetime\"\n                placeholder=\"请选择用车结束时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"施工作业说明\" prop=\"workDescription\">\n          <el-input\n            v-model=\"form.workDescription\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请详细说明施工作业内容\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息\"\n            maxlength=\"200\"\n            show-word-limit />\n        </el-form-item>\n\n        <!-- 车辆忙闲状态展示 -->\n        <el-form-item label=\"车辆状态\">\n          <el-button type=\"text\" @click=\"showVehicleStatus\" icon=\"el-icon-view\">\n            查看当前车辆忙闲状态\n          </el-button>\n        </el-form-item>\n\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">\n            <i class=\"el-icon-check\"></i> 提交申请\n          </el-button>\n          <el-button @click=\"resetForm\">\n            <i class=\"el-icon-refresh-left\"></i> 重置\n          </el-button>\n          <el-button @click=\"saveDraft\">\n            <i class=\"el-icon-document\"></i> 保存草稿\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 车辆状态对话框 -->\n    <el-dialog title=\"车辆忙闲状态\" :visible.sync=\"statusDialogVisible\" width=\"800px\">\n      <el-table :data=\"vehicleStatusList\" v-loading=\"statusLoading\">\n        <el-table-column label=\"车辆类型\" prop=\"vehicleType\" />\n        <el-table-column label=\"车辆型号\" prop=\"vehicleModel\" />\n        <el-table-column label=\"车牌号\" prop=\"licensePlate\" />\n        <el-table-column label=\"司机\" prop=\"driverName\" />\n        <el-table-column label=\"状态\" prop=\"vehicleStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getVehicleStatusType(scope.row.vehicleStatus)\">\n              {{ getVehicleStatusText(scope.row.vehicleStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"当前位置\" prop=\"currentLocation\" />\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { submitApplication, addApplication } from \"@/api/vehicle/application\";\nimport { getTeamOptions } from \"@/api/vehicle/team\";\nimport { getAvailableVehicles } from \"@/api/vehicle/application\";\nimport { getDicts } from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"ApplicationApply\",\n  data() {\n    return {\n      // 表单数据\n      form: {\n        applicationTitle: '',\n        vehicleType: '',\n        vehicleModel: '',\n        usageLocation: '',\n        workDescription: '',\n        startTime: '',\n        endTime: '',\n        teamId: null,\n        applicant: '',\n        applicantPhone: '',\n        remark: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 选项数据\n      vehicleTypeOptions: [],\n      vehicleModelOptions: [],\n      teamOptions: [],\n      // 车辆状态对话框\n      statusDialogVisible: false,\n      statusLoading: false,\n      vehicleStatusList: [],\n      // 表单验证规则\n      rules: {\n        applicationTitle: [\n          { required: true, message: \"申请标题不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: \"长度在 2 到 100 个字符\", trigger: \"blur\" }\n        ],\n        vehicleType: [\n          { required: true, message: \"请选择车辆类型\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"请选择车辆型号\", trigger: \"change\" }\n        ],\n        usageLocation: [\n          { required: true, message: \"用车地点不能为空\", trigger: \"blur\" }\n        ],\n        workDescription: [\n          { required: true, message: \"施工作业说明不能为空\", trigger: \"blur\" },\n          { min: 10, max: 500, message: \"长度在 10 到 500 个字符\", trigger: \"blur\" }\n        ],\n        startTime: [\n          { required: true, message: \"请选择用车开始时间\", trigger: \"change\" }\n        ],\n        endTime: [\n          { required: true, message: \"请选择用车结束时间\", trigger: \"change\" }\n        ],\n        teamId: [\n          { required: true, message: \"请选择队伍\", trigger: \"change\" }\n        ],\n        applicant: [\n          { required: true, message: \"申请人不能为空\", trigger: \"blur\" }\n        ],\n        applicantPhone: [\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadOptions();\n  },\n  methods: {\n    /** 加载选项数据 */\n    async loadOptions() {\n      try {\n        // 加载车辆类型字典\n        const vehicleTypeRes = await getDicts(\"vehicle_type\");\n        this.vehicleTypeOptions = vehicleTypeRes.data;\n        \n        // 加载队伍选项\n        const teamRes = await getTeamOptions();\n        this.teamOptions = teamRes.data;\n      } catch (error) {\n        this.$modal.msgError(\"加载选项数据失败\");\n      }\n    },\n    \n    /** 车辆类型变化处理 */\n    async handleVehicleTypeChange(value) {\n      this.form.vehicleModel = '';\n      this.vehicleModelOptions = [];\n      \n      if (value) {\n        try {\n          // 根据车辆类型加载对应的型号\n          const modelRes = await getDicts(\"vehicle_model_\" + value);\n          this.vehicleModelOptions = modelRes.data;\n        } catch (error) {\n          console.warn(\"加载车辆型号失败:\", error);\n        }\n      }\n    },\n    \n    /** 队伍变化处理 */\n    handleTeamChange(teamId) {\n      const selectedTeam = this.teamOptions.find(team => team.teamId === teamId);\n      if (selectedTeam) {\n        this.form.applicant = selectedTeam.teamLeader;\n        this.form.applicantPhone = selectedTeam.leaderPhone;\n      }\n    },\n    \n    /** 显示车辆状态 */\n    async showVehicleStatus() {\n      this.statusDialogVisible = true;\n      this.statusLoading = true;\n      \n      try {\n        const response = await getAvailableVehicles({\n          vehicleType: this.form.vehicleType,\n          startTime: this.form.startTime,\n          endTime: this.form.endTime\n        });\n        this.vehicleStatusList = response.data;\n      } catch (error) {\n        this.$modal.msgError(\"获取车辆状态失败\");\n      } finally {\n        this.statusLoading = false;\n      }\n    },\n    \n    /** 提交表单 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 验证时间范围\n          if (new Date(this.form.endTime) <= new Date(this.form.startTime)) {\n            this.$modal.msgError(\"用车结束时间必须大于开始时间\");\n            return;\n          }\n          \n          this.submitLoading = true;\n          submitApplication(this.form).then(response => {\n            this.$modal.msgSuccess(\"用车申请提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 保存草稿 */\n    saveDraft() {\n      const draftData = { ...this.form, approvalStatus: 'draft' };\n      addApplication(draftData).then(response => {\n        this.$modal.msgSuccess(\"草稿保存成功\");\n      });\n    },\n    \n    /** 重置表单 */\n    resetForm() {\n      this.$refs[\"form\"].resetFields();\n      this.vehicleModelOptions = [];\n    },\n    \n    /** 获取车辆状态类型 */\n    getVehicleStatusType(status) {\n      const statusMap = {\n        'available': 'success',\n        'busy': 'warning',\n        'maintenance': 'info',\n        'fault': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取车辆状态文本 */\n    getVehicleStatusText(status) {\n      const statusMap = {\n        'available': '可用',\n        'busy': '使用中',\n        'maintenance': '维护中',\n        'fault': '故障'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/application');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-form-item {\n  margin-bottom: 22px;\n}\n\n.el-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}