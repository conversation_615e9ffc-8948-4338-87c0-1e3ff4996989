{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\driver.vue?vue&type=style&index=0&id=4cd6e5b4&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\driver.vue", "mtime": 1754143015329}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbjogMjBweDsKfQoKLnN0YXQtY2FyZCB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKfQoKLnN0YXQtY2FyZDpob3ZlciB7CiAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwwLDAsMC4xMik7Cn0KCi5zdGF0LWl0ZW0gewogIHBhZGRpbmc6IDIwcHg7Cn0KCi5zdGF0LXZhbHVlIHsKICBmb250LXNpemU6IDI4cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICM0MDlFRkY7CiAgbWFyZ2luLWJvdHRvbTogOHB4Owp9Cgouc3RhdC1sYWJlbCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjNjA2MjY2Owp9CgoubWIyMCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnVwbG9hZC10aXAgewogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzk5OTsKICBtYXJnaW4tdG9wOiA1cHg7Cn0KCi50aW1lLWluZm8gewogIGJhY2tncm91bmQ6ICNmNWY3ZmE7CiAgcGFkZGluZzogMTVweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgbGluZS1oZWlnaHQ6IDEuODsKfQoKLnBob3RvLXNlY3Rpb24gewogIHRleHQtYWxpZ246IGNlbnRlcjsKfQoKLnBob3RvLXNlY3Rpb24gaDUgewogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgY29sb3I6ICM2MDYyNjY7Cn0K"}, {"version": 3, "sources": ["driver.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgjBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "driver.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">司机端 - 我的订单</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 订单统计 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待开始</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.runningCount }}</div>\n              <div class=\"stat-label\">进行中</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.finishedCount }}</div>\n              <div class=\"stat-label\">已完成</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日订单</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"待开始\" value=\"pending\"></el-option>\n            <el-option label=\"进行中\" value=\"running\"></el-option>\n            <el-option label=\"司机已结束\" value=\"driver_finished\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"用车日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @row-click=\"handleRowClick\">\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"计划时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.plannedStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.plannedEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"实际时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.actualStartTime\">\n              开始：{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}\n            </div>\n            <div v-if=\"scope.row.actualEndTime\" style=\"color: #909399; font-size: 12px;\">\n              结束：{{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n            <div v-if=\"!scope.row.actualStartTime\" style=\"color: #E6A23C;\">未开始</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              v-if=\"scope.row.orderStatus === 'pending'\"\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleStart(scope.row)\"\n            >开始用车</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'running'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleFinish(scope.row)\"\n            >结束用车</el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 开始用车对话框 -->\n    <el-dialog title=\"开始用车\" :visible.sync=\"startDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"startForm\" :model=\"startForm\" :rules=\"startRules\" label-width=\"100px\">\n        <el-form-item label=\"订单信息\">\n          <el-descriptions :column=\"1\" size=\"small\">\n            <el-descriptions-item label=\"用车地点\">{{ currentOrder.usageLocation }}</el-descriptions-item>\n            <el-descriptions-item label=\"计划时间\">\n              {{ parseTime(currentOrder.plannedStartTime) }} 至 {{ parseTime(currentOrder.plannedEndTime) }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </el-form-item>\n        \n        <el-form-item label=\"开始拍照\" prop=\"startPhoto\">\n          <el-upload\n            ref=\"startUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :file-list=\"startFileList\"\n            :on-success=\"handleStartPhotoSuccess\"\n            :on-remove=\"handleStartPhotoRemove\"\n            :before-upload=\"beforePhotoUpload\"\n            list-type=\"picture-card\"\n            accept=\"image/*\">\n            <i class=\"el-icon-plus\"></i>\n          </el-upload>\n          <div class=\"upload-tip\">请拍摄车辆和作业现场照片</div>\n        </el-form-item>\n        \n        <el-form-item label=\"备注\">\n          <el-input\n            v-model=\"startForm.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入开始用车备注\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"startDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStart\" :loading=\"startLoading\">确认开始</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 结束用车对话框 -->\n    <el-dialog title=\"结束用车\" :visible.sync=\"finishDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"finishForm\" :model=\"finishForm\" :rules=\"finishRules\" label-width=\"100px\">\n        <el-form-item label=\"用车时长\">\n          <div class=\"time-info\">\n            <div>开始时间：{{ parseTime(currentOrder.actualStartTime) }}</div>\n            <div>当前时间：{{ parseTime(new Date()) }}</div>\n            <div style=\"color: #409EFF; font-weight: bold;\">\n              用车时长：{{ calculateDuration(currentOrder.actualStartTime, new Date()) }}\n            </div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"结束拍照\" prop=\"endPhoto\">\n          <el-upload\n            ref=\"finishUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :file-list=\"finishFileList\"\n            :on-success=\"handleFinishPhotoSuccess\"\n            :on-remove=\"handleFinishPhotoRemove\"\n            :before-upload=\"beforePhotoUpload\"\n            list-type=\"picture-card\"\n            accept=\"image/*\">\n            <i class=\"el-icon-plus\"></i>\n          </el-upload>\n          <div class=\"upload-tip\">请拍摄车辆和作业完成照片</div>\n        </el-form-item>\n        \n        <el-form-item label=\"工作总结\">\n          <el-input\n            v-model=\"finishForm.workSummary\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请简要总结本次用车工作内容\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"finishDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitFinish\" :loading=\"finishLoading\">确认结束</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划开始时间\">{{ parseTime(detailOrder.plannedStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划结束时间\">{{ parseTime(detailOrder.plannedEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">\n          {{ detailOrder.actualStartTime ? parseTime(detailOrder.actualStartTime) : '未开始' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">\n          {{ detailOrder.actualEndTime ? parseTime(detailOrder.actualEndTime) : '未结束' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"队伍信息\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 照片展示 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrder, getOrder, startOrder, finishOrder } from \"@/api/vehicle/order\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"DriverOrder\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        runningCount: 0,\n        finishedCount: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: null,\n        driverName: this.$store.state.user.name // 当前登录司机\n      },\n      // 日期范围\n      dateRange: [],\n      // 当前订单\n      currentOrder: {},\n      detailOrder: {},\n      // 开始用车对话框\n      startDialogVisible: false,\n      startLoading: false,\n      startForm: {\n        startPhotoUrl: '',\n        remark: ''\n      },\n      startFileList: [],\n      // 结束用车对话框\n      finishDialogVisible: false,\n      finishLoading: false,\n      finishForm: {\n        endPhotoUrl: '',\n        workSummary: ''\n      },\n      finishFileList: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      // 上传配置\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/vehicle/order/upload-photo\",\n      uploadHeaders: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      // 表单验证规则\n      startRules: {\n        startPhoto: [\n          { required: true, message: \"请上传开始照片\", trigger: \"change\" }\n        ]\n      },\n      finishRules: {\n        endPhoto: [\n          { required: true, message: \"请上传结束照片\", trigger: \"change\" }\n        ],\n        workSummary: [\n          { required: true, message: \"请填写工作总结\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginPlannedStartTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endPlannedStartTime\"] = this.dateRange[1];\n      }\n      \n      listOrder(this.queryParams).then(response => {\n        this.orderList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 3,\n        runningCount: 1,\n        finishedCount: 8,\n        todayCount: 2\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    /** 行点击事件 */\n    handleRowClick(row) {\n      this.handleView(row);\n    },\n    \n    /** 开始用车 */\n    handleStart(row) {\n      this.currentOrder = row;\n      this.startDialogVisible = true;\n      this.startForm = {\n        startPhotoUrl: '',\n        remark: ''\n      };\n      this.startFileList = [];\n    },\n    \n    /** 结束用车 */\n    handleFinish(row) {\n      this.currentOrder = row;\n      this.finishDialogVisible = true;\n      this.finishForm = {\n        endPhotoUrl: '',\n        workSummary: ''\n      };\n      this.finishFileList = [];\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 提交开始用车 */\n    submitStart() {\n      this.$refs[\"startForm\"].validate(valid => {\n        if (valid) {\n          this.startLoading = true;\n          startOrder(this.currentOrder.orderId, this.startForm.startPhotoUrl).then(response => {\n            this.$modal.msgSuccess(\"开始用车成功\");\n            this.startDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.startLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 提交结束用车 */\n    submitFinish() {\n      this.$refs[\"finishForm\"].validate(valid => {\n        if (valid) {\n          this.finishLoading = true;\n          finishOrder(this.currentOrder.orderId, this.finishForm.endPhotoUrl).then(response => {\n            this.$modal.msgSuccess(\"结束用车成功，等待队伍确认\");\n            this.finishDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.finishLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 开始照片上传成功 */\n    handleStartPhotoSuccess(response, file) {\n      this.startForm.startPhotoUrl = response.url;\n    },\n    \n    /** 开始照片移除 */\n    handleStartPhotoRemove() {\n      this.startForm.startPhotoUrl = '';\n    },\n    \n    /** 结束照片上传成功 */\n    handleFinishPhotoSuccess(response, file) {\n      this.finishForm.endPhotoUrl = response.url;\n    },\n    \n    /** 结束照片移除 */\n    handleFinishPhotoRemove() {\n      this.finishForm.endPhotoUrl = '';\n    },\n    \n    /** 上传前检查 */\n    beforePhotoUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0;\n      const isLt5M = file.size / 1024 / 1024 < 5;\n      \n      if (!isImage) {\n        this.$modal.msgError('只能上传图片文件!');\n        return false;\n      }\n      if (!isLt5M) {\n        this.$modal.msgError('上传图片大小不能超过 5MB!');\n        return false;\n      }\n      return true;\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending': 'warning',\n        'running': 'primary',\n        'driver_finished': 'success',\n        'team_confirmed': 'success',\n        'dispatch_confirmed': 'success',\n        'completed': 'success'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待开始',\n        'running': '进行中',\n        'driver_finished': '司机已结束',\n        'team_confirmed': '队伍已确认',\n        'dispatch_confirmed': '调度已确认',\n        'completed': '已完成'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.time-info {\n  background: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  line-height: 1.8;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n</style>\n"]}]}