<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 系统概览 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 18px; font-weight: bold;">机械车辆管理系统</span>
            <el-button style="float: right; padding: 3px 0" type="text">系统概览</el-button>
          </div>
          <div class="system-overview">
            <p style="font-size: 16px; color: #666; margin-bottom: 20px;">
              本系统是一个基于RuoYi-Cloud微服务架构的机械车辆管理平台，提供车辆信息管理、需求计划、用车申请、订单管理等全流程服务。
            </p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #409EFF;">
              <i class="el-icon-truck" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalVehicles }}</div>
              <div class="stat-label">车辆总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #67C23A;">
              <i class="el-icon-check" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.availableVehicles }}</div>
              <div class="stat-label">可用车辆</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #E6A23C;">
              <i class="el-icon-document" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingOrders }}</div>
              <div class="stat-label">待处理订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #F56C6C;">
              <i class="el-icon-warning" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.maintenanceVehicles }}</div>
              <div class="stat-label">维修车辆</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 功能模块 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold;">功能模块</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8" v-for="module in functionModules" :key="module.name">
              <div class="function-module" @click="navigateTo(module.path)">
                <div class="module-icon">
                  <i :class="module.icon" style="font-size: 32px; color: #409EFF;"></i>
                </div>
                <div class="module-content">
                  <h3>{{ module.name }}</h3>
                  <p>{{ module.description }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统特性 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold;">系统特性</span>
          </div>
          <ul class="feature-list">
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 车辆信息全生命周期管理</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 多级审批流程支持</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 实时消息通知（钉钉集成）</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 台班统计与分析</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 违章记录管理</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 维修记录跟踪</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 批量审批功能</li>
            <li><i class="el-icon-check" style="color: #67C23A;"></i> 数据导入导出</li>
          </ul>
        </el-card>
      </el-col>
      <!-- 技术架构 -->
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold;">技术架构</span>
          </div>
          <ul class="tech-list">
            <li><strong>后端框架：</strong>Spring Boot + Spring Cloud</li>
            <li><strong>前端框架：</strong>Vue.js + Element UI</li>
            <li><strong>数据库：</strong>MySQL</li>
            <li><strong>缓存：</strong>Redis</li>
            <li><strong>注册中心：</strong>Nacos</li>
            <li><strong>网关：</strong>Spring Cloud Gateway</li>
            <li><strong>认证：</strong>Spring Security + JWT</li>
            <li><strong>消息通知：</strong>钉钉API</li>
          </ul>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 业务流程 -->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 16px; font-weight: bold;">主要业务流程</span>
          </div>
          <el-steps :active="4" align-center>
            <el-step title="需求计划" description="队伍提交车辆需求计划"></el-step>
            <el-step title="审批流程" description="项目调度室→机械主管→经营部门"></el-step>
            <el-step title="用车申请" description="队伍提交具体用车申请"></el-step>
            <el-step title="调度安排" description="调度室分配车辆和司机"></el-step>
            <el-step title="订单执行" description="司机执行用车任务"></el-step>
            <el-step title="台班确认" description="多级确认完成台班"></el-step>
          </el-steps>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "VehicleDashboard",
  data() {
    return {
      // 统计数据
      statistics: {
        totalVehicles: 0,
        availableVehicles: 0,
        pendingOrders: 0,
        maintenanceVehicles: 0
      },
      // 功能模块
      functionModules: [
        {
          name: "车辆信息管理",
          description: "管理机械车辆基本信息、违章记录、维修记录",
          icon: "el-icon-truck",
          path: "/vehicle/info"
        },
        {
          name: "需求计划管理",
          description: "车辆需求计划申请与审批流程管理",
          icon: "el-icon-document-add",
          path: "/vehicle/demand"
        },
        {
          name: "用车申请管理",
          description: "机械用车申请与调度安排管理",
          icon: "el-icon-edit-outline",
          path: "/vehicle/application"
        },
        {
          name: "订单管理",
          description: "用车订单全流程跟踪与管理",
          icon: "el-icon-s-order",
          path: "/vehicle/order"
        },
        {
          name: "台班审批",
          description: "批量台班审批与确认功能",
          icon: "el-icon-s-check",
          path: "/vehicle/approval"
        },
        {
          name: "统计分析",
          description: "车辆台班统计与多维度分析",
          icon: "el-icon-s-data",
          path: "/vehicle/statistics"
        }
      ]
    };
  },
  created() {
    this.getStatistics();
  },
  methods: {
    /** 获取统计数据 */
    getStatistics() {
      // 模拟数据，实际应该调用API
      this.statistics = {
        totalVehicles: 156,
        availableVehicles: 128,
        pendingOrders: 23,
        maintenanceVehicles: 8
      };
    },
    /** 导航到指定页面 */
    navigateTo(path) {
      this.$router.push(path);
    }
  }
};
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.function-module {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.function-module:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.module-icon {
  margin-right: 20px;
}

.module-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.module-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.feature-list, .tech-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li, .tech-list li {
  padding: 8px 0;
  border-bottom: 1px solid #F5F7FA;
}

.feature-list li:last-child, .tech-list li:last-child {
  border-bottom: none;
}

.feature-list i {
  margin-right: 8px;
}
</style>
