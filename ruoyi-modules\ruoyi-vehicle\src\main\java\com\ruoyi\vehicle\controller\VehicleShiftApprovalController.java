package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleShiftApproval;
import com.ruoyi.vehicle.service.IVehicleShiftApprovalService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 台班审批Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/approval")
public class VehicleShiftApprovalController extends BaseController
{
    @Autowired
    private IVehicleShiftApprovalService vehicleShiftApprovalService;

    /**
     * 查询台班审批列表
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleShiftApproval vehicleShiftApproval)
    {
        startPage();
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectVehicleShiftApprovalList(vehicleShiftApproval);
        return getDataTable(list);
    }

    /**
     * 导出台班审批列表
     */
    @RequiresPermissions("vehicle:approval:export")
    @Log(title = "台班审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleShiftApproval vehicleShiftApproval)
    {
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectVehicleShiftApprovalList(vehicleShiftApproval);
        ExcelUtil<VehicleShiftApproval> util = new ExcelUtil<VehicleShiftApproval>(VehicleShiftApproval.class);
        util.exportExcel(response, list, "台班审批数据");
    }

    /**
     * 获取台班审批详细信息
     */
    @RequiresPermissions("vehicle:approval:query")
    @GetMapping(value = "/{approvalId}")
    public AjaxResult getInfo(@PathVariable("approvalId") Long approvalId)
    {
        return success(vehicleShiftApprovalService.selectVehicleShiftApprovalByApprovalId(approvalId));
    }

    /**
     * 新增台班审批
     */
    @RequiresPermissions("vehicle:approval:add")
    @Log(title = "台班审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleShiftApproval vehicleShiftApproval)
    {
        vehicleShiftApproval.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleShiftApprovalService.insertVehicleShiftApproval(vehicleShiftApproval));
    }

    /**
     * 修改台班审批
     */
    @RequiresPermissions("vehicle:approval:edit")
    @Log(title = "台班审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleShiftApproval vehicleShiftApproval)
    {
        vehicleShiftApproval.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleShiftApprovalService.updateVehicleShiftApproval(vehicleShiftApproval));
    }

    /**
     * 删除台班审批
     */
    @RequiresPermissions("vehicle:approval:remove")
    @Log(title = "台班审批", businessType = BusinessType.DELETE)
    @DeleteMapping("/{approvalIds}")
    public AjaxResult remove(@PathVariable Long[] approvalIds)
    {
        return toAjax(vehicleShiftApprovalService.deleteVehicleShiftApprovalByApprovalIds(approvalIds));
    }

    /**
     * 审批处理
     */
    @RequiresPermissions("vehicle:approval:process")
    @Log(title = "审批处理", businessType = BusinessType.UPDATE)
    @PutMapping("/process/{approvalId}")
    public AjaxResult process(@PathVariable Long approvalId, @RequestBody VehicleShiftApproval vehicleShiftApproval)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleShiftApprovalService.processApproval(
            approvalId, 
            vehicleShiftApproval.getApprovalStatus(), 
            vehicleShiftApproval.getApprovalComments(), 
            operName
        ));
    }

    /**
     * 批量审批处理
     */
    @RequiresPermissions("vehicle:approval:process")
    @Log(title = "批量审批处理", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-process")
    public AjaxResult batchProcess(@RequestBody VehicleShiftApproval vehicleShiftApproval)
    {
        String operName = SecurityUtils.getUsername();
        Long[] approvalIds = vehicleShiftApproval.getApprovalId() != null ? 
            new Long[]{vehicleShiftApproval.getApprovalId()} : new Long[0];
        
        int result = vehicleShiftApprovalService.batchProcessApproval(
            approvalIds, 
            vehicleShiftApproval.getApprovalStatus(), 
            vehicleShiftApproval.getApprovalComments(), 
            operName
        );
        
        return success("批量审批完成，成功处理 " + result + " 条审批");
    }

    /**
     * 查询我的待审批列表
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/my-pending")
    public AjaxResult getMyPendingApprovals()
    {
        String approver = SecurityUtils.getUsername();
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectPendingApprovalsByApprover(approver);
        return success(list);
    }

    /**
     * 根据审批级别查询待审批列表
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/pending-by-level/{approvalLevel}")
    public AjaxResult getPendingByLevel(@PathVariable Integer approvalLevel)
    {
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectPendingApprovalsByLevel(approvalLevel);
        return success(list);
    }

    /**
     * 根据业务ID和类型查询审批记录
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/business/{businessType}/{businessId}")
    public AjaxResult getByBusinessIdAndType(@PathVariable String businessType, @PathVariable Long businessId)
    {
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectApprovalsByBusinessIdAndType(businessId, businessType);
        return success(list);
    }

    /**
     * 获取审批统计信息
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        String approver = SecurityUtils.getUsername();
        Object statistics = vehicleShiftApprovalService.getApprovalStatistics(approver);
        return success(statistics);
    }

    /**
     * 查询超时未审批的记录
     */
    @RequiresPermissions("vehicle:approval:list")
    @GetMapping("/timeout/{timeoutHours}")
    public AjaxResult getTimeoutApprovals(@PathVariable Integer timeoutHours)
    {
        List<VehicleShiftApproval> list = vehicleShiftApprovalService.selectTimeoutApprovals(timeoutHours);
        return success(list);
    }

    /**
     * 创建审批记录
     */
    @RequiresPermissions("vehicle:approval:add")
    @Log(title = "创建审批记录", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createApproval(@RequestBody VehicleShiftApproval vehicleShiftApproval)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleShiftApprovalService.createApprovalRecord(
            vehicleShiftApproval.getBusinessType(),
            vehicleShiftApproval.getBusinessId(),
            vehicleShiftApproval.getApprovalLevel(),
            vehicleShiftApproval.getApprover(),
            operName
        ));
    }
}
