<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleMaintenanceMapper">
    
    <resultMap type="VehicleMaintenance" id="VehicleMaintenanceResult">
        <result property="maintenanceId"    column="maintenance_id"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="maintenanceType"    column="maintenance_type"    />
        <result property="maintenanceDate"    column="maintenance_date"    />
        <result property="maintenanceLocation"    column="maintenance_location"    />
        <result property="maintenanceDescription"    column="maintenance_description"    />
        <result property="maintenanceCost"    column="maintenance_cost"    />
        <result property="maintenanceCompany"    column="maintenance_company"    />
        <result property="status"    column="status"    />
        <result property="nextMaintenanceDate"    column="next_maintenance_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleMaintenanceVo">
        select maintenance_id, vehicle_id, maintenance_type, maintenance_date, maintenance_location, 
               maintenance_description, maintenance_cost, maintenance_company, status, next_maintenance_date, 
               create_by, create_time, update_by, update_time, remark 
        from vehicle_maintenance
    </sql>

    <select id="selectVehicleMaintenanceList" parameterType="VehicleMaintenance" resultMap="VehicleMaintenanceResult">
        <include refid="selectVehicleMaintenanceVo"/>
        <where>  
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="maintenanceType != null  and maintenanceType != ''"> and maintenance_type = #{maintenanceType}</if>
            <if test="maintenanceLocation != null  and maintenanceLocation != ''"> and maintenance_location like concat('%', #{maintenanceLocation}, '%')</if>
            <if test="maintenanceCompany != null  and maintenanceCompany != ''"> and maintenance_company like concat('%', #{maintenanceCompany}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginMaintenanceDate != null and params.beginMaintenanceDate != ''"><!-- 开始时间检索 -->
                and date_format(maintenance_date,'%y%m%d') &gt;= date_format(#{params.beginMaintenanceDate},'%y%m%d')
            </if>
            <if test="params.endMaintenanceDate != null and params.endMaintenanceDate != ''"><!-- 结束时间检索 -->
                and date_format(maintenance_date,'%y%m%d') &lt;= date_format(#{params.endMaintenanceDate},'%y%m%d')
            </if>
        </where>
        order by maintenance_date desc
    </select>
    
    <select id="selectVehicleMaintenanceByMaintenanceId" parameterType="Long" resultMap="VehicleMaintenanceResult">
        <include refid="selectVehicleMaintenanceVo"/>
        where maintenance_id = #{maintenanceId}
    </select>

    <select id="selectVehicleMaintenanceByVehicleId" parameterType="Long" resultMap="VehicleMaintenanceResult">
        <include refid="selectVehicleMaintenanceVo"/>
        where vehicle_id = #{vehicleId}
        order by maintenance_date desc
    </select>

    <select id="selectVehicleMaintenanceByStatus" parameterType="String" resultMap="VehicleMaintenanceResult">
        <include refid="selectVehicleMaintenanceVo"/>
        where status = #{status}
        order by maintenance_date desc
    </select>

    <select id="selectUpcomingMaintenance" resultMap="VehicleMaintenanceResult">
        <include refid="selectVehicleMaintenanceVo"/>
        where next_maintenance_date &lt;= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        and status = 'completed'
        order by next_maintenance_date asc
    </select>
        
    <insert id="insertVehicleMaintenance" parameterType="VehicleMaintenance" useGeneratedKeys="true" keyProperty="maintenanceId">
        insert into vehicle_maintenance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="maintenanceType != null and maintenanceType != ''">maintenance_type,</if>
            <if test="maintenanceDate != null">maintenance_date,</if>
            <if test="maintenanceLocation != null and maintenanceLocation != ''">maintenance_location,</if>
            <if test="maintenanceDescription != null">maintenance_description,</if>
            <if test="maintenanceCost != null">maintenance_cost,</if>
            <if test="maintenanceCompany != null and maintenanceCompany != ''">maintenance_company,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="nextMaintenanceDate != null">next_maintenance_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="maintenanceType != null and maintenanceType != ''">#{maintenanceType},</if>
            <if test="maintenanceDate != null">#{maintenanceDate},</if>
            <if test="maintenanceLocation != null and maintenanceLocation != ''">#{maintenanceLocation},</if>
            <if test="maintenanceDescription != null">#{maintenanceDescription},</if>
            <if test="maintenanceCost != null">#{maintenanceCost},</if>
            <if test="maintenanceCompany != null and maintenanceCompany != ''">#{maintenanceCompany},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="nextMaintenanceDate != null">#{nextMaintenanceDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleMaintenance" parameterType="VehicleMaintenance">
        update vehicle_maintenance
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="maintenanceType != null and maintenanceType != ''">maintenance_type = #{maintenanceType},</if>
            <if test="maintenanceDate != null">maintenance_date = #{maintenanceDate},</if>
            <if test="maintenanceLocation != null and maintenanceLocation != ''">maintenance_location = #{maintenanceLocation},</if>
            <if test="maintenanceDescription != null">maintenance_description = #{maintenanceDescription},</if>
            <if test="maintenanceCost != null">maintenance_cost = #{maintenanceCost},</if>
            <if test="maintenanceCompany != null and maintenanceCompany != ''">maintenance_company = #{maintenanceCompany},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="nextMaintenanceDate != null">next_maintenance_date = #{nextMaintenanceDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where maintenance_id = #{maintenanceId}
    </update>

    <delete id="deleteVehicleMaintenanceByMaintenanceId" parameterType="Long">
        delete from vehicle_maintenance where maintenance_id = #{maintenanceId}
    </delete>

    <delete id="deleteVehicleMaintenanceByMaintenanceIds" parameterType="String">
        delete from vehicle_maintenance where maintenance_id in 
        <foreach item="maintenanceId" collection="array" open="(" separator="," close=")">
            #{maintenanceId}
        </foreach>
    </delete>
</mapper>
