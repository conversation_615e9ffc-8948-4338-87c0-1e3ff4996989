<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">队伍确认 - 待确认订单</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待确认订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.confirmedCount }}</div>
              <div class="stat-label">已确认订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.rejectedCount }}</div>
              <div class="stat-label">异议订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.todayCount }}</div>
              <div class="stat-label">今日处理</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择状态" clearable @change="getList">
            <el-option label="司机已结束" value="driver_finished"></el-option>
            <el-option label="队伍已确认" value="team_confirmed"></el-option>
            <el-option label="异议退回" value="rejected"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆类型" prop="vehicleType">
          <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable @change="getList">
            <el-option
              v-for="dict in dict.type.vehicle_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="完成日期">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getList">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchConfirm"
            v-hasPermi="['vehicle:order:team-confirm']"
          >批量确认</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-close"
            size="mini"
            :disabled="multiple"
            @click="handleBatchReject"
            v-hasPermi="['vehicle:order:reject']"
          >批量异议</el-button>
        </el-col>
      </el-row>

      <!-- 订单列表 -->
      <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单ID" align="center" prop="orderId" width="80" />
        <el-table-column label="车辆信息" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">
              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="司机" align="center" prop="driverName" width="100" />
        <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
        <el-table-column label="实际用车时间" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>
            <div style="color: #909399; font-size: 12px;">
              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用车时长" align="center" width="100">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold;">
              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="mini">
              {{ getStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看详情</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'driver_finished'"
              size="mini"
              type="success"
              @click="handleConfirm(scope.row)"
              v-hasPermi="['vehicle:order:team-confirm']"
            >确认</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'driver_finished'"
              size="mini"
              type="warning"
              @click="handleReject(scope.row)"
              v-hasPermi="['vehicle:order:reject']"
            >异议</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailDialogVisible" width="900px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ detailOrder.orderId }}</el-descriptions-item>
        <el-descriptions-item label="车辆信息">
          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}
          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})
        </el-descriptions-item>
        <el-descriptions-item label="司机">{{ detailOrder.driverName }}</el-descriptions-item>
        <el-descriptions-item label="队伍">
          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="用车地点" :span="2">{{ detailOrder.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="计划开始时间">{{ parseTime(detailOrder.plannedStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="计划结束时间">{{ parseTime(detailOrder.plannedEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际开始时间">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际结束时间">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="用车时长">
          <span style="color: #409EFF; font-weight: bold;">
            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusTagType(detailOrder.orderStatus)">
            {{ getStatusText(detailOrder.orderStatus) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 作业照片 -->
      <div v-if="detailOrder.startPhotoUrl || detailOrder.endPhotoUrl" style="margin-top: 20px;">
        <h4>作业照片</h4>
        <el-row :gutter="20">
          <el-col :span="12" v-if="detailOrder.startPhotoUrl">
            <div class="photo-section">
              <h5>开始照片</h5>
              <el-image
                :src="detailOrder.startPhotoUrl"
                :preview-src-list="[detailOrder.startPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="12" v-if="detailOrder.endPhotoUrl">
            <div class="photo-section">
              <h5>结束照片</h5>
              <el-image
                :src="detailOrder.endPhotoUrl"
                :preview-src-list="[detailOrder.endPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 确认操作 -->
      <div v-if="detailOrder.orderStatus === 'driver_finished'" style="margin-top: 20px;">
        <el-divider content-position="left">确认操作</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button type="success" size="medium" @click="handleConfirm(detailOrder)" style="width: 100%;">
              <i class="el-icon-check"></i> 确认订单
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="warning" size="medium" @click="handleReject(detailOrder)" style="width: 100%;">
              <i class="el-icon-close"></i> 提出异议
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 异议对话框 -->
    <el-dialog title="提出异议" :visible.sync="rejectDialogVisible" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="异议原因" prop="rejectReason">
          <el-select v-model="rejectForm.rejectReason" placeholder="请选择异议原因">
            <el-option label="用车时间不符" value="time_mismatch"></el-option>
            <el-option label="用车地点不符" value="location_mismatch"></el-option>
            <el-option label="作业内容不符" value="work_mismatch"></el-option>
            <el-option label="照片不清晰" value="photo_unclear"></el-option>
            <el-option label="其他原因" value="other"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="详细说明" prop="rejectDescription">
          <el-input
            v-model="rejectForm.rejectDescription"
            type="textarea"
            :rows="4"
            placeholder="请详细说明异议原因"
            maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
        <el-button type="warning" @click="submitReject" :loading="rejectLoading">提交异议</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPendingConfirmOrders, getOrder, teamConfirmOrder, rejectOrder } from "@/api/vehicle/order";

export default {
  name: "TeamConfirm",
  dicts: ['vehicle_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 订单列表
      orderList: [],
      // 统计信息
      statistics: {
        pendingCount: 0,
        confirmedCount: 0,
        rejectedCount: 0,
        todayCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderStatus: 'driver_finished',
        teamId: this.$store.state.user.teamId // 当前用户所属队伍
      },
      // 日期范围
      dateRange: [],
      // 详情对话框
      detailDialogVisible: false,
      detailOrder: {},
      // 异议对话框
      rejectDialogVisible: false,
      rejectLoading: false,
      currentOrder: {},
      rejectForm: {
        rejectReason: '',
        rejectDescription: ''
      },
      // 异议表单验证规则
      rejectRules: {
        rejectReason: [
          { required: true, message: "请选择异议原因", trigger: "change" }
        ],
        rejectDescription: [
          { required: true, message: "请详细说明异议原因", trigger: "blur" },
          { min: 10, max: 500, message: "长度在 10 到 500 个字符", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.params["beginActualEndTime"] = this.dateRange[0];
        this.queryParams.params["endActualEndTime"] = this.dateRange[1];
      }
      
      getPendingConfirmOrders('team').then(response => {
        this.orderList = response.data;
        this.total = response.data.length;
        this.loading = false;
      });
    },
    
    /** 加载统计信息 */
    loadStatistics() {
      // TODO: 调用统计接口
      this.statistics = {
        pendingCount: 5,
        confirmedCount: 12,
        rejectedCount: 2,
        todayCount: 3
      };
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新列表 */
    refreshList() {
      this.getList();
      this.loadStatistics();
      this.$modal.msgSuccess("刷新成功");
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.multiple = !selection.length
    },
    
    /** 查看详情 */
    handleView(row) {
      getOrder(row.orderId).then(response => {
        this.detailOrder = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 确认订单 */
    handleConfirm(row) {
      this.$modal.confirm('确认该订单的用车情况属实？').then(() => {
        return teamConfirmOrder(row.orderId);
      }).then(() => {
        this.$modal.msgSuccess("确认成功");
        this.detailDialogVisible = false;
        this.getList();
      }).catch(() => {});
    },
    
    /** 提出异议 */
    handleReject(row) {
      this.currentOrder = row;
      this.rejectDialogVisible = true;
      this.rejectForm = {
        rejectReason: '',
        rejectDescription: ''
      };
    },
    
    /** 提交异议 */
    submitReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          this.rejectLoading = true;
          const data = {
            rejectReason: `${this.getRejectReasonText(this.rejectForm.rejectReason)}: ${this.rejectForm.rejectDescription}`
          };
          
          rejectOrder(this.currentOrder.orderId, data).then(response => {
            this.$modal.msgSuccess("异议提交成功");
            this.rejectDialogVisible = false;
            this.detailDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.rejectLoading = false;
          });
        }
      });
    },
    
    /** 批量确认 */
    handleBatchConfirm() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要确认的订单");
        return;
      }
      
      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {
        // TODO: 调用批量确认接口
        this.$modal.msgSuccess("批量确认成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 批量异议 */
    handleBatchReject() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要提出异议的订单");
        return;
      }
      this.$modal.msgInfo("批量异议功能开发中...");
    },
    
    /** 计算用车时长 */
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '0小时';
      
      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      return `${hours}小时${minutes}分钟`;
    },
    
    /** 获取异议原因文本 */
    getRejectReasonText(reason) {
      const reasonMap = {
        'time_mismatch': '用车时间不符',
        'location_mismatch': '用车地点不符',
        'work_mismatch': '作业内容不符',
        'photo_unclear': '照片不清晰',
        'other': '其他原因'
      };
      return reasonMap[reason] || '未知原因';
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'driver_finished': 'warning',
        'team_confirmed': 'success',
        'rejected': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'driver_finished': '司机已结束',
        'team_confirmed': '队伍已确认',
        'rejected': '异议退回'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.mb20 {
  margin-bottom: 20px;
}

.photo-section {
  text-align: center;
}

.photo-section h5 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
