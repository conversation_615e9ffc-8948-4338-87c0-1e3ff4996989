{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\index.vue", "mtime": 1754142701700}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demand", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "demandList", "detailOpen", "detailForm", "daterangeCreateTime", "queryParams", "pageNum", "pageSize", "planTitle", "vehicleType", "approvalStatus", "created", "getList", "methods", "_this", "params", "listDemand", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "planId", "length", "handleAdd", "$router", "push", "handleUpdate", "row", "handleView", "_this2", "<PERSON><PERSON><PERSON><PERSON>", "handleApprove", "handleDelete", "_this3", "planIds", "$modal", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "msgSuccess", "catch", "handleMyPlans", "_this4", "getMyDemands", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "getStatusTagType", "status", "statusMap", "getStatusText", "canApprove", "includes"], "sources": ["src/views/vehicle/demand/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"计划标题\" prop=\"planTitle\">\n        <el-input\n          v-model=\"queryParams.planTitle\"\n          placeholder=\"请输入计划标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"审批状态\" prop=\"approvalStatus\">\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择审批状态\" clearable>\n          <el-option label=\"草稿\" value=\"draft\"></el-option>\n          <el-option label=\"待项目调度室审批\" value=\"pending_level1\"></el-option>\n          <el-option label=\"待机械主管审批\" value=\"pending_level2\"></el-option>\n          <el-option label=\"待经营部门审批\" value=\"pending_level3\"></el-option>\n          <el-option label=\"已通过\" value=\"approved\"></el-option>\n          <el-option label=\"已拒绝\" value=\"rejected\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"申请时间\">\n        <el-date-picker\n          v-model=\"daterangeCreateTime\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:demand:add']\"\n        >新增申请</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:demand:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:demand:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:demand:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-view\"\n          size=\"mini\"\n          @click=\"handleMyPlans\"\n          v-hasPermi=\"['vehicle:demand:list']\"\n        >我的申请</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"demandList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"计划ID\" align=\"center\" prop=\"planId\" width=\"80\" />\n      <el-table-column label=\"计划标题\" align=\"center\" prop=\"planTitle\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleType }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.vehicleModel }}</div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"需求单位\" align=\"center\" prop=\"demandUnit\" />\n      <el-table-column label=\"需求数量\" align=\"center\" prop=\"demandQuantity\" width=\"80\" />\n      <el-table-column label=\"需求时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <div>{{ parseTime(scope.row.demandStartTime, '{m}-{d} {h}:{i}') }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            至 {{ parseTime(scope.row.demandEndTime, '{m}-{d} {h}:{i}') }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicant\" width=\"100\" />\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"getStatusTagType(scope.row.approvalStatus)\"\n            size=\"mini\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:query']\"\n          >查看</el-button>\n          <el-button\n            v-if=\"scope.row.approvalStatus === 'draft'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:edit']\"\n          >修改</el-button>\n          <el-button\n            v-if=\"canApprove(scope.row.approvalStatus)\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleApprove(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:approve']\"\n          >审批</el-button>\n          <el-button\n            v-if=\"scope.row.approvalStatus === 'draft'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:demand:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 查看详情对话框 -->\n    <el-dialog title=\"需求计划详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"计划标题\" :span=\"2\">{{ detailForm.planTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆类型\">{{ detailForm.vehicleType }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆型号\">{{ detailForm.vehicleModel }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求单位\">{{ detailForm.demandUnit }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求数量\">{{ detailForm.demandQuantity }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求开始时间\">{{ parseTime(detailForm.demandStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求结束时间\">{{ parseTime(detailForm.demandEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请人\">{{ detailForm.applicant }}</el-descriptions-item>\n        <el-descriptions-item label=\"联系电话\">{{ detailForm.applicantPhone }}</el-descriptions-item>\n        <el-descriptions-item label=\"审批状态\">\n          <el-tag :type=\"getStatusTagType(detailForm.approvalStatus)\">\n            {{ getStatusText(detailForm.approvalStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"申请时间\">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用途说明\" :span=\"2\">{{ detailForm.usagePurpose }}</el-descriptions-item>\n        <el-descriptions-item v-if=\"detailForm.approvalComments\" label=\"审批意见\" :span=\"2\">\n          {{ detailForm.approvalComments }}\n        </el-descriptions-item>\n        <el-descriptions-item v-if=\"detailForm.remark\" label=\"备注\" :span=\"2\">{{ detailForm.remark }}</el-descriptions-item>\n      </el-descriptions>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listDemand, getDemand, delDemand, getMyDemands } from \"@/api/vehicle/demand\";\n\nexport default {\n  name: \"Demand\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 需求计划表格数据\n      demandList: [],\n      // 是否显示详情弹出层\n      detailOpen: false,\n      // 详情表单参数\n      detailForm: {},\n      // 申请时间时间范围\n      daterangeCreateTime: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        planTitle: null,\n        vehicleType: null,\n        approvalStatus: null\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询需求计划列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {\n        this.queryParams.params[\"beginCreateTime\"] = this.daterangeCreateTime[0];\n        this.queryParams.params[\"endCreateTime\"] = this.daterangeCreateTime[1];\n      }\n      listDemand(this.queryParams).then(response => {\n        this.demandList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.daterangeCreateTime = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.planId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.$router.push('/vehicle/demand/apply');\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const planId = row.planId || this.ids[0];\n      this.$router.push('/vehicle/demand/edit/' + planId);\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      getDemand(row.planId).then(response => {\n        this.detailForm = response.data;\n        this.detailOpen = true;\n      });\n    },\n    /** 审批按钮操作 */\n    handleApprove(row) {\n      this.$router.push('/vehicle/demand/approve/' + row.planId);\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const planIds = row.planId || this.ids;\n      this.$modal.confirm('是否确认删除需求计划编号为\"' + planIds + '\"的数据项？').then(function() {\n        return delDemand(planIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 我的申请 */\n    handleMyPlans() {\n      getMyDemands().then(response => {\n        this.demandList = response.data;\n        this.total = response.data.length;\n        this.$modal.msgSuccess(\"已切换到我的申请\");\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/demand/export', {\n        ...this.queryParams\n      }, `demand_${new Date().getTime()}.xlsx`)\n    },\n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'draft': 'info',\n        'pending_level1': 'warning',\n        'pending_level2': 'warning', \n        'pending_level3': 'warning',\n        'approved': 'success',\n        'rejected': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'draft': '草稿',\n        'pending_level1': '待项目调度室审批',\n        'pending_level2': '待机械主管审批',\n        'pending_level3': '待经营部门审批',\n        'approved': '已通过',\n        'rejected': '已拒绝'\n      };\n      return statusMap[status] || '未知';\n    },\n    /** 判断是否可以审批 */\n    canApprove(status) {\n      return ['pending_level1', 'pending_level2', 'pending_level3'].includes(status);\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;AAqNA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,mBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAnB,OAAA;MACA,KAAAU,WAAA,CAAAU,MAAA;MACA,iBAAAX,mBAAA,eAAAA,mBAAA;QACA,KAAAC,WAAA,CAAAU,MAAA,2BAAAX,mBAAA;QACA,KAAAC,WAAA,CAAAU,MAAA,yBAAAX,mBAAA;MACA;MACA,IAAAY,kBAAA,OAAAX,WAAA,EAAAY,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAb,UAAA,GAAAiB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAd,KAAA,GAAAkB,QAAA,CAAAlB,KAAA;QACAc,KAAA,CAAAnB,OAAA;MACA;IACA;IACA,aACAyB,WAAA,WAAAA,YAAA;MACA,KAAAf,WAAA,CAAAC,OAAA;MACA,KAAAM,OAAA;IACA;IACA,aACAS,UAAA,WAAAA,WAAA;MACA,KAAAjB,mBAAA;MACA,KAAAkB,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5B,GAAA,GAAA4B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,KAAA9B,MAAA,GAAA2B,SAAA,CAAAI,MAAA;MACA,KAAA9B,QAAA,IAAA0B,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAN,MAAA,GAAAM,GAAA,CAAAN,MAAA,SAAA/B,GAAA;MACA,KAAAkC,OAAA,CAAAC,IAAA,2BAAAJ,MAAA;IACA;IACA,aACAO,UAAA,WAAAA,WAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,iBAAA,EAAAH,GAAA,CAAAN,MAAA,EAAAV,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAAhC,UAAA,GAAAe,QAAA,CAAAxB,IAAA;QACAyC,MAAA,CAAAjC,UAAA;MACA;IACA;IACA,aACAmC,aAAA,WAAAA,cAAAJ,GAAA;MACA,KAAAH,OAAA,CAAAC,IAAA,8BAAAE,GAAA,CAAAN,MAAA;IACA;IACA,aACAW,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,OAAA,GAAAP,GAAA,CAAAN,MAAA,SAAA/B,GAAA;MACA,KAAA6C,MAAA,CAAAC,OAAA,oBAAAF,OAAA,aAAAvB,IAAA;QACA,WAAA0B,iBAAA,EAAAH,OAAA;MACA,GAAAvB,IAAA;QACAsB,MAAA,CAAA3B,OAAA;QACA2B,MAAA,CAAAE,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA,IAAA/B,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA9C,UAAA,GAAAiB,QAAA,CAAAxB,IAAA;QACAqD,MAAA,CAAA/C,KAAA,GAAAkB,QAAA,CAAAxB,IAAA,CAAAkC,MAAA;QACAmB,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA;IACA;IACA,aACAK,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA/C,WAAA,aAAAgD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,eACAC,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA,aACAE,aAAA,WAAAA,cAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA,eACAG,UAAA,WAAAA,WAAAH,MAAA;MACA,8DAAAI,QAAA,CAAAJ,MAAA;IACA;EACA;AACA", "ignoreList": []}]}