-- 机械车辆管理系统数据库表设计

-- 1. 机械车辆信息表
CREATE TABLE `vehicle_info` (
  `vehicle_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '车辆ID',
  `vehicle_type` varchar(50) NOT NULL COMMENT '车辆类型',
  `vehicle_model` varchar(100) NOT NULL COMMENT '车辆型号',
  `unit_name` varchar(200) NOT NULL COMMENT '单位名称',
  `license_plate` varchar(20) DEFAULT NULL COMMENT '车牌号码',
  `driver_name` varchar(50) DEFAULT NULL COMMENT '司机姓名',
  `driver_phone` varchar(20) DEFAULT NULL COMMENT '司机电话',
  `commander_name` varchar(50) DEFAULT NULL COMMENT '指挥姓名',
  `commander_phone` varchar(20) DEFAULT NULL COMMENT '指挥电话',
  `entry_time` datetime DEFAULT NULL COMMENT '车辆入场时间',
  `project_phase` varchar(10) DEFAULT NULL COMMENT '一期/二期',
  `vehicle_status` varchar(20) DEFAULT '可用' COMMENT '车辆状态（可用、故障、维护、退场）',
  `shift_confirmer` varchar(200) DEFAULT NULL COMMENT '台班确认人（可多选）',
  `cost_unit` varchar(10) DEFAULT '天' COMMENT '费用计量单位（天或小时）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`vehicle_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='机械车辆信息表';

-- 2. 车辆违章记录表
CREATE TABLE `vehicle_violation` (
  `violation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '违章记录ID',
  `vehicle_id` bigint(20) NOT NULL COMMENT '车辆ID',
  `violation_time` datetime NOT NULL COMMENT '违章时间',
  `violation_location` varchar(200) DEFAULT NULL COMMENT '违章地点',
  `violation_type` varchar(100) NOT NULL COMMENT '违章类型',
  `violation_description` text COMMENT '违章描述',
  `penalty_amount` decimal(10,2) DEFAULT NULL COMMENT '罚款金额',
  `status` varchar(20) DEFAULT '未处理' COMMENT '处理状态（未处理、已处理）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`violation_id`),
  KEY `idx_vehicle_id` (`vehicle_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='车辆违章记录表';

-- 3. 车辆维修记录表
CREATE TABLE `vehicle_maintenance` (
  `maintenance_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '维修记录ID',
  `vehicle_id` bigint(20) NOT NULL COMMENT '车辆ID',
  `maintenance_date` date NOT NULL COMMENT '维修日期',
  `maintenance_type` varchar(50) NOT NULL COMMENT '维修类型',
  `fault_description` text COMMENT '故障描述',
  `maintenance_content` text COMMENT '维修内容',
  `maintenance_cost` decimal(10,2) DEFAULT NULL COMMENT '维修费用',
  `maintenance_person` varchar(50) DEFAULT NULL COMMENT '维修人员',
  `maintenance_company` varchar(200) DEFAULT NULL COMMENT '维修公司',
  `status` varchar(20) DEFAULT '维修中' COMMENT '维修状态（维修中、已完成）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`maintenance_id`),
  KEY `idx_vehicle_id` (`vehicle_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='车辆维修记录表';

-- 4. 队伍信息表
CREATE TABLE `team_info` (
  `team_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '队伍ID',
  `team_name` varchar(100) NOT NULL COMMENT '队伍名称',
  `team_leader` varchar(50) NOT NULL COMMENT '队伍负责人',
  `leader_phone` varchar(20) DEFAULT NULL COMMENT '负责人联系方式',
  `team_type` varchar(50) DEFAULT NULL COMMENT '队伍类型',
  `status` varchar(20) DEFAULT '正常' COMMENT '状态（正常、停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='队伍信息表';

-- 5. 车辆需求计划表
CREATE TABLE `vehicle_demand_plan` (
  `plan_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `plan_title` varchar(200) NOT NULL COMMENT '计划标题',
  `vehicle_type` varchar(50) NOT NULL COMMENT '车辆类型',
  `vehicle_model` varchar(100) NOT NULL COMMENT '车辆型号',
  `demand_unit` varchar(200) NOT NULL COMMENT '需求单位',
  `demand_start_time` datetime NOT NULL COMMENT '需求开始时间',
  `demand_end_time` datetime NOT NULL COMMENT '需求结束时间',
  `usage_purpose` text COMMENT '用途说明',
  `team_id` bigint(20) NOT NULL COMMENT '队伍ID',
  `applicant` varchar(50) NOT NULL COMMENT '申请人',
  `applicant_phone` varchar(20) DEFAULT NULL COMMENT '申请人联系方式',
  `approval_status` varchar(20) DEFAULT '待审批' COMMENT '审批状态（待审批、项目调度室审批、机械主管审批、经营审批、已通过、已拒绝）',
  `current_approver` varchar(50) DEFAULT NULL COMMENT '当前审批人',
  `approval_comments` text COMMENT '审批意见',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`plan_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='车辆需求计划表';

-- 6. 机械用车申请表
CREATE TABLE `vehicle_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `application_title` varchar(200) NOT NULL COMMENT '申请标题',
  `vehicle_type` varchar(50) NOT NULL COMMENT '车辆类型',
  `vehicle_model` varchar(100) NOT NULL COMMENT '车辆型号',
  `usage_location` varchar(200) NOT NULL COMMENT '用车地点',
  `work_description` text COMMENT '施工作业说明',
  `start_time` datetime NOT NULL COMMENT '用车开始时间',
  `end_time` datetime NOT NULL COMMENT '用车结束时间',
  `team_id` bigint(20) NOT NULL COMMENT '队伍ID',
  `applicant` varchar(50) NOT NULL COMMENT '申请人',
  `applicant_phone` varchar(20) DEFAULT NULL COMMENT '申请人联系方式',
  `approval_status` varchar(20) DEFAULT '待审批' COMMENT '审批状态（待审批、已通过、已拒绝）',
  `assigned_vehicle_id` bigint(20) DEFAULT NULL COMMENT '分配的车辆ID',
  `assigned_driver` varchar(50) DEFAULT NULL COMMENT '分配的司机',
  `scheduler` varchar(50) DEFAULT NULL COMMENT '调度人员',
  `schedule_time` datetime DEFAULT NULL COMMENT '调度时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_assigned_vehicle_id` (`assigned_vehicle_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='机械用车申请表';

-- 7. 用车订单表
CREATE TABLE `vehicle_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `application_id` bigint(20) NOT NULL COMMENT '申请ID',
  `vehicle_id` bigint(20) NOT NULL COMMENT '车辆ID',
  `team_id` bigint(20) NOT NULL COMMENT '队伍ID',
  `driver_name` varchar(50) NOT NULL COMMENT '司机姓名',
  `usage_location` varchar(200) NOT NULL COMMENT '用车地点',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `start_photo_url` varchar(500) DEFAULT NULL COMMENT '开始拍照URL',
  `end_photo_url` varchar(500) DEFAULT NULL COMMENT '结束拍照URL',
  `order_status` varchar(20) DEFAULT '待开始' COMMENT '订单状态（待开始、进行中、司机已结束、队伍已确认、调度室已确认、主管已确认、已完成）',
  `team_confirm_time` datetime DEFAULT NULL COMMENT '队伍确认时间',
  `team_confirm_person` varchar(50) DEFAULT NULL COMMENT '队伍确认人',
  `dispatch_confirm_time` datetime DEFAULT NULL COMMENT '调度室确认时间',
  `dispatch_confirm_person` varchar(50) DEFAULT NULL COMMENT '调度室确认人',
  `manager_confirm_time` datetime DEFAULT NULL COMMENT '主管确认时间',
  `manager_confirm_person` varchar(50) DEFAULT NULL COMMENT '主管确认人',
  `reject_reason` text COMMENT '退回原因',
  `vehicle_weight` decimal(10,2) DEFAULT NULL COMMENT '车辆重量（吨）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`order_id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='用车订单表';

-- 8. 消息通知表
CREATE TABLE `vehicle_notification` (
  `notification_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `notification_type` varchar(50) NOT NULL COMMENT '通知类型（需求计划、用车申请、订单确认等）',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `recipient` varchar(50) NOT NULL COMMENT '接收人',
  `recipient_phone` varchar(20) DEFAULT NULL COMMENT '接收人电话',
  `send_status` varchar(20) DEFAULT '待发送' COMMENT '发送状态（待发送、已发送、发送失败）',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `read_status` varchar(20) DEFAULT '未读' COMMENT '阅读状态（未读、已读）',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `dingtalk_msg_id` varchar(100) DEFAULT NULL COMMENT '钉钉消息ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notification_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_recipient` (`recipient`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='消息通知表';

-- 9. 台班审批表
CREATE TABLE `vehicle_shift_approval` (
  `approval_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型（需求计划、用车订单）',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `approval_level` int(2) NOT NULL COMMENT '审批级别（1-项目调度室、2-机械主管、3-经营部门）',
  `approver` varchar(50) NOT NULL COMMENT '审批人',
  `approval_status` varchar(20) DEFAULT '待审批' COMMENT '审批状态（待审批、已通过、已拒绝）',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_comments` text COMMENT '审批意见',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`approval_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_approver` (`approver`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='台班审批表';

-- 10. 车辆类型字典表
CREATE TABLE `vehicle_type_dict` (
  `type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_code` varchar(50) NOT NULL COMMENT '类型编码',
  `type_name` varchar(100) NOT NULL COMMENT '类型名称',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父级ID',
  `sort_order` int(4) DEFAULT 0 COMMENT '排序',
  `status` varchar(20) DEFAULT '正常' COMMENT '状态（正常、停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`type_id`),
  UNIQUE KEY `uk_type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='车辆类型字典表';

-- 初始化车辆类型数据
INSERT INTO `vehicle_type_dict` (`type_code`, `type_name`, `parent_id`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('EXCAVATOR', '挖掘机', 0, 1, '正常', 'admin', NOW()),
('BULLDOZER', '推土机', 0, 2, '正常', 'admin', NOW()),
('LOADER', '装载机', 0, 3, '正常', 'admin', NOW()),
('CRANE', '起重机', 0, 4, '正常', 'admin', NOW()),
('TRUCK', '运输车', 0, 5, '正常', 'admin', NOW()),
('ROLLER', '压路机', 0, 6, '正常', 'admin', NOW()),
('MIXER', '混凝土搅拌车', 0, 7, '正常', 'admin', NOW()),
('PUMP', '泵车', 0, 8, '正常', 'admin', NOW());

-- 初始化队伍信息数据
INSERT INTO `team_info` (`team_name`, `team_leader`, `leader_phone`, `team_type`, `status`, `create_by`, `create_time`) VALUES
('第一施工队', '张三', '13800138001', '施工队伍', '正常', 'admin', NOW()),
('第二施工队', '李四', '13800138002', '施工队伍', '正常', 'admin', NOW()),
('第三施工队', '王五', '13800138003', '施工队伍', '正常', 'admin', NOW()),
('机械维修队', '赵六', '13800138004', '维修队伍', '正常', 'admin', NOW()),
('安全监督队', '钱七', '13800138005', '监督队伍', '正常', 'admin', NOW());
