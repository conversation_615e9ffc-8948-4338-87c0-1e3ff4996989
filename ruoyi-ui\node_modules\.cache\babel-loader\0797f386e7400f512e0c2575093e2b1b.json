{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\violation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\violation\\index.vue", "mtime": 1754141667786}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_violation", "require", "_info", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "violationList", "vehicleOptions", "title", "open", "daterangeViolationTime", "queryParams", "pageNum", "pageSize", "vehicleId", "violationType", "violationTime", "status", "form", "rules", "required", "message", "trigger", "violationLocation", "created", "getList", "getVehicleOptions", "methods", "_this", "params", "listViolation", "then", "response", "rows", "_this2", "listInfo", "cancel", "reset", "violationId", "violationDescription", "penaltyAmount", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this3", "getViolation", "submitForm", "_this4", "$refs", "validate", "valid", "updateViolation", "$modal", "msgSuccess", "addViolation", "handleDelete", "_this5", "violationIds", "confirm", "delViolation", "catch", "handleProcess", "_this6", "processViolation", "handleBatchProcess", "_this7", "batchProcessViolation", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/vehicle/violation/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"车辆ID\" prop=\"vehicleId\">\n        <el-select v-model=\"queryParams.vehicleId\" placeholder=\"请选择车辆\" clearable>\n          <el-option\n            v-for=\"vehicle in vehicleOptions\"\n            :key=\"vehicle.vehicleId\"\n            :label=\"vehicle.vehicleModel\"\n            :value=\"vehicle.vehicleId\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"违章类型\" prop=\"violationType\">\n        <el-select v-model=\"queryParams.violationType\" placeholder=\"请选择违章类型\" clearable>\n          <el-option label=\"超速\" value=\"超速\"></el-option>\n          <el-option label=\"违停\" value=\"违停\"></el-option>\n          <el-option label=\"闯红灯\" value=\"闯红灯\"></el-option>\n          <el-option label=\"其他\" value=\"其他\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"处理状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择处理状态\" clearable>\n          <el-option label=\"未处理\" value=\"pending\"></el-option>\n          <el-option label=\"处理中\" value=\"processing\"></el-option>\n          <el-option label=\"已处理\" value=\"processed\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"违章时间\">\n        <el-date-picker\n          v-model=\"daterangeViolationTime\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:violation:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:violation:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:violation:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:violation:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleBatchProcess\"\n          v-hasPermi=\"['vehicle:violation:process']\"\n        >批量处理</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"violationList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"违章ID\" align=\"center\" prop=\"violationId\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" prop=\"vehicleInfo\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"违章类型\" align=\"center\" prop=\"violationType\" />\n      <el-table-column label=\"违章时间\" align=\"center\" prop=\"violationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.violationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"违章地点\" align=\"center\" prop=\"violationLocation\" />\n      <el-table-column label=\"罚款金额\" align=\"center\" prop=\"penaltyAmount\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #E6A23C;\">¥{{ scope.row.penaltyAmount }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"处理状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.violation_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:remove']\"\n          >删除</el-button>\n          <el-button\n            v-if=\"scope.row.status === 'pending'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleProcess(scope.row)\"\n            v-hasPermi=\"['vehicle:violation:process']\"\n          >处理</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改违章记录对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"车辆\" prop=\"vehicleId\">\n          <el-select v-model=\"form.vehicleId\" placeholder=\"请选择车辆\">\n            <el-option\n              v-for=\"vehicle in vehicleOptions\"\n              :key=\"vehicle.vehicleId\"\n              :label=\"vehicle.vehicleModel\"\n              :value=\"vehicle.vehicleId\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"违章类型\" prop=\"violationType\">\n          <el-select v-model=\"form.violationType\" placeholder=\"请选择违章类型\">\n            <el-option label=\"超速\" value=\"超速\"></el-option>\n            <el-option label=\"违停\" value=\"违停\"></el-option>\n            <el-option label=\"闯红灯\" value=\"闯红灯\"></el-option>\n            <el-option label=\"其他\" value=\"其他\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"违章时间\" prop=\"violationTime\">\n          <el-date-picker clearable\n            v-model=\"form.violationTime\"\n            type=\"datetime\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            placeholder=\"请选择违章时间\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"违章地点\" prop=\"violationLocation\">\n          <el-input v-model=\"form.violationLocation\" placeholder=\"请输入违章地点\" />\n        </el-form-item>\n        <el-form-item label=\"罚款金额\" prop=\"penaltyAmount\">\n          <el-input v-model=\"form.penaltyAmount\" placeholder=\"请输入罚款金额\" />\n        </el-form-item>\n        <el-form-item label=\"违章描述\" prop=\"violationDescription\">\n          <el-input v-model=\"form.violationDescription\" type=\"textarea\" placeholder=\"请输入违章描述\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listViolation, getViolation, delViolation, addViolation, updateViolation, processViolation, batchProcessViolation } from \"@/api/vehicle/violation\";\nimport { listInfo } from \"@/api/vehicle/info\";\n\nexport default {\n  name: \"Violation\",\n  dicts: ['violation_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 违章记录表格数据\n      violationList: [],\n      // 车辆选项\n      vehicleOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 违章时间时间范围\n      daterangeViolationTime: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleId: null,\n        violationType: null,\n        violationTime: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        vehicleId: [\n          { required: true, message: \"车辆不能为空\", trigger: \"change\" }\n        ],\n        violationType: [\n          { required: true, message: \"违章类型不能为空\", trigger: \"change\" }\n        ],\n        violationTime: [\n          { required: true, message: \"违章时间不能为空\", trigger: \"blur\" }\n        ],\n        violationLocation: [\n          { required: true, message: \"违章地点不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getVehicleOptions();\n  },\n  methods: {\n    /** 查询违章记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.daterangeViolationTime && '' != this.daterangeViolationTime) {\n        this.queryParams.params[\"beginViolationTime\"] = this.daterangeViolationTime[0];\n        this.queryParams.params[\"endViolationTime\"] = this.daterangeViolationTime[1];\n      }\n      listViolation(this.queryParams).then(response => {\n        this.violationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 获取车辆选项 */\n    getVehicleOptions() {\n      listInfo().then(response => {\n        this.vehicleOptions = response.rows;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        violationId: null,\n        vehicleId: null,\n        violationType: null,\n        violationTime: null,\n        violationLocation: null,\n        violationDescription: null,\n        penaltyAmount: null,\n        status: \"pending\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.daterangeViolationTime = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.violationId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加违章记录\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const violationId = row.violationId || this.ids\n      getViolation(violationId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改违章记录\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.violationId != null) {\n            updateViolation(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addViolation(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const violationIds = row.violationId || this.ids;\n      this.$modal.confirm('是否确认删除违章记录编号为\"' + violationIds + '\"的数据项？').then(function() {\n        return delViolation(violationIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 处理违章记录 */\n    handleProcess(row) {\n      this.$modal.confirm('是否确认处理该违章记录？').then(function() {\n        return processViolation(row.violationId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"处理成功\");\n      }).catch(() => {});\n    },\n    /** 批量处理违章记录 */\n    handleBatchProcess() {\n      this.$modal.confirm('是否确认批量处理选中的违章记录？').then(() => {\n        return batchProcessViolation(this.ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"批量处理成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/violation/export', {\n        ...this.queryParams\n      }, `violation_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AAyNA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,sBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,SAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,aAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,iBAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA,eACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAA5B,OAAA;MACA,KAAAW,WAAA,CAAAkB,MAAA;MACA,iBAAAnB,sBAAA,eAAAA,sBAAA;QACA,KAAAC,WAAA,CAAAkB,MAAA,8BAAAnB,sBAAA;QACA,KAAAC,WAAA,CAAAkB,MAAA,4BAAAnB,sBAAA;MACA;MACA,IAAAoB,wBAAA,OAAAnB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAtB,aAAA,GAAA0B,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAvB,KAAA,GAAA2B,QAAA,CAAA3B,KAAA;QACAuB,KAAA,CAAA5B,OAAA;MACA;IACA;IACA,aACA0B,iBAAA,WAAAA,kBAAA;MAAA,IAAAQ,MAAA;MACA,IAAAC,cAAA,IAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA3B,cAAA,GAAAyB,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAG,MAAA,WAAAA,OAAA;MACA,KAAA3B,IAAA;MACA,KAAA4B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnB,IAAA;QACAoB,WAAA;QACAxB,SAAA;QACAC,aAAA;QACAC,aAAA;QACAO,iBAAA;QACAgB,oBAAA;QACAC,aAAA;QACAvB,MAAA;QACAwB,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAmB,UAAA,WAAAA,WAAA;MACA,KAAAlC,sBAAA;MACA,KAAAgC,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,WAAA;MAAA;MACA,KAAApC,MAAA,GAAA4C,SAAA,CAAAG,MAAA;MACA,KAAA9C,QAAA,IAAA2C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAA5B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAC,WAAA,GAAAc,GAAA,CAAAd,WAAA,SAAArC,GAAA;MACA,IAAAqD,uBAAA,EAAAhB,WAAA,EAAAP,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAAnC,IAAA,GAAAc,QAAA,CAAAjC,IAAA;QACAsD,MAAA,CAAA5C,IAAA;QACA4C,MAAA,CAAA7C,KAAA;MACA;IACA;IACA,WACA+C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtC,IAAA,CAAAoB,WAAA;YACA,IAAAsB,0BAAA,EAAAJ,MAAA,CAAAtC,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA/C,IAAA;cACA+C,MAAA,CAAA/B,OAAA;YACA;UACA;YACA,IAAAsC,uBAAA,EAAAP,MAAA,CAAAtC,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA/C,IAAA;cACA+C,MAAA,CAAA/B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAuC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,YAAA,GAAAd,GAAA,CAAAd,WAAA,SAAArC,GAAA;MACA,KAAA4D,MAAA,CAAAM,OAAA,oBAAAD,YAAA,aAAAnC,IAAA;QACA,WAAAqC,uBAAA,EAAAF,YAAA;MACA,GAAAnC,IAAA;QACAkC,MAAA,CAAAxC,OAAA;QACAwC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,aAAA,WAAAA,cAAAlB,GAAA;MAAA,IAAAmB,MAAA;MACA,KAAAV,MAAA,CAAAM,OAAA,iBAAApC,IAAA;QACA,WAAAyC,2BAAA,EAAApB,GAAA,CAAAd,WAAA;MACA,GAAAP,IAAA;QACAwC,MAAA,CAAA9C,OAAA;QACA8C,MAAA,CAAAV,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,eACAI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,MAAA,CAAAM,OAAA,qBAAApC,IAAA;QACA,WAAA4C,gCAAA,EAAAD,MAAA,CAAAzE,GAAA;MACA,GAAA8B,IAAA;QACA2C,MAAA,CAAAjD,OAAA;QACAiD,MAAA,CAAAb,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAO,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAAC,OAAA,MACA,KAAApE,WAAA,gBAAAqE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}