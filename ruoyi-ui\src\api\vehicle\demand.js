import request from '@/utils/request'

// 查询车辆需求计划列表
export function listDemand(query) {
  return request({
    url: '/vehicle/demand/list',
    method: 'get',
    params: query
  })
}

// 查询车辆需求计划详细
export function getDemand(planId) {
  return request({
    url: '/vehicle/demand/' + planId,
    method: 'get'
  })
}

// 新增车辆需求计划
export function addDemand(data) {
  return request({
    url: '/vehicle/demand',
    method: 'post',
    data: data
  })
}

// 修改车辆需求计划
export function updateDemand(data) {
  return request({
    url: '/vehicle/demand',
    method: 'put',
    data: data
  })
}

// 删除车辆需求计划
export function delDemand(planId) {
  return request({
    url: '/vehicle/demand/' + planId,
    method: 'delete'
  })
}

// 提交需求计划
export function submitDemand(data) {
  return request({
    url: '/vehicle/demand/submit',
    method: 'post',
    data: data
  })
}

// 审批需求计划
export function approveDemand(planId, data) {
  return request({
    url: '/vehicle/demand/approve/' + planId,
    method: 'put',
    data: data
  })
}

// 根据审批状态查询计划列表
export function getDemandByStatus(approvalStatus) {
  return request({
    url: '/vehicle/demand/status/' + approvalStatus,
    method: 'get'
  })
}

// 根据队伍ID查询计划列表
export function getDemandByTeamId(teamId) {
  return request({
    url: '/vehicle/demand/team/' + teamId,
    method: 'get'
  })
}

// 查询我的需求计划
export function getMyDemands() {
  return request({
    url: '/vehicle/demand/my-plans',
    method: 'get'
  })
}

// 查询待审批的计划列表
export function getPendingApprovalDemands() {
  return request({
    url: '/vehicle/demand/pending-approval',
    method: 'get'
  })
}

// 批量审批需求计划
export function batchApproveDemands(data) {
  return request({
    url: '/vehicle/demand/batch-approve',
    method: 'put',
    data: data
  })
}
