package com.ruoyi.vehicle.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 消息通知对象 vehicle_notification
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleNotification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 通知ID */
    private Long notificationId;

    /** 通知类型 */
    @Excel(name = "通知类型", readConverterExp = "需求计划=demand_plan,用车申请=application,订单确认=order_confirm")
    private String notificationType;

    /** 业务ID */
    @Excel(name = "业务ID")
    private Long businessId;

    /** 通知标题 */
    @Excel(name = "通知标题")
    private String title;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String content;

    /** 接收人 */
    @Excel(name = "接收人")
    private String recipient;

    /** 接收人电话 */
    @Excel(name = "接收人电话")
    private String recipientPhone;

    /** 发送状态 */
    @Excel(name = "发送状态", readConverterExp = "待发送=pending,已发送=sent,发送失败=failed")
    private String sendStatus;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 阅读状态 */
    @Excel(name = "阅读状态", readConverterExp = "未读=unread,已读=read")
    private String readStatus;

    /** 阅读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /** 钉钉消息ID */
    @Excel(name = "钉钉消息ID")
    private String dingtalkMsgId;

    public void setNotificationId(Long notificationId) 
    {
        this.notificationId = notificationId;
    }

    public Long getNotificationId() 
    {
        return notificationId;
    }

    public void setNotificationType(String notificationType) 
    {
        this.notificationType = notificationType;
    }

    @NotBlank(message = "通知类型不能为空")
    @Size(min = 0, max = 50, message = "通知类型长度不能超过50个字符")
    public String getNotificationType() 
    {
        return notificationType;
    }

    public void setBusinessId(Long businessId) 
    {
        this.businessId = businessId;
    }

    @NotNull(message = "业务ID不能为空")
    public Long getBusinessId() 
    {
        return businessId;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    @NotBlank(message = "通知标题不能为空")
    @Size(min = 0, max = 200, message = "通知标题长度不能超过200个字符")
    public String getTitle() 
    {
        return title;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setRecipient(String recipient) 
    {
        this.recipient = recipient;
    }

    @NotBlank(message = "接收人不能为空")
    @Size(min = 0, max = 50, message = "接收人长度不能超过50个字符")
    public String getRecipient() 
    {
        return recipient;
    }

    public void setRecipientPhone(String recipientPhone) 
    {
        this.recipientPhone = recipientPhone;
    }

    @Size(min = 0, max = 20, message = "接收人电话长度不能超过20个字符")
    public String getRecipientPhone() 
    {
        return recipientPhone;
    }

    public void setSendStatus(String sendStatus) 
    {
        this.sendStatus = sendStatus;
    }

    @Size(min = 0, max = 20, message = "发送状态长度不能超过20个字符")
    public String getSendStatus() 
    {
        return sendStatus;
    }

    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }

    public void setReadStatus(String readStatus) 
    {
        this.readStatus = readStatus;
    }

    @Size(min = 0, max = 20, message = "阅读状态长度不能超过20个字符")
    public String getReadStatus() 
    {
        return readStatus;
    }

    public void setReadTime(Date readTime) 
    {
        this.readTime = readTime;
    }

    public Date getReadTime() 
    {
        return readTime;
    }

    public void setDingtalkMsgId(String dingtalkMsgId) 
    {
        this.dingtalkMsgId = dingtalkMsgId;
    }

    @Size(min = 0, max = 100, message = "钉钉消息ID长度不能超过100个字符")
    public String getDingtalkMsgId() 
    {
        return dingtalkMsgId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("notificationId", getNotificationId())
            .append("notificationType", getNotificationType())
            .append("businessId", getBusinessId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("recipient", getRecipient())
            .append("recipientPhone", getRecipientPhone())
            .append("sendStatus", getSendStatus())
            .append("sendTime", getSendTime())
            .append("readStatus", getReadStatus())
            .append("readTime", getReadTime())
            .append("dingtalkMsgId", getDingtalkMsgId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
