# 🔧 API接口404错误修复指南

## 📋 问题分析

您遇到的 `http://localhost/dev-api/vehicle/application/list` 和 `http://localhost/dev-api/vehicle/info/list` 返回 `404 NOT_FOUND` 错误，主要原因如下：

### 🔍 **根本原因**：
1. **Nacos服务注册地址不一致**：车辆模块配置的是 `127.0.0.1:8848`，其他模块是 `**************:8848`
2. **网关路由配置缺失**：网关没有配置车辆模块的路由规则
3. **车辆模块未正确注册到服务发现**：导致网关无法找到车辆服务

---

## ✅ 修复方案

### 第一步：修复Nacos配置地址

**已修复**：`ruoyi-modules/ruoyi-vehicle/src/main/resources/bootstrap.yml`
```yaml
# 修复前
server-addr: 127.0.0.1:8848

# 修复后
server-addr: **************:8848
```

### 第二步：更新Nacos配置初始化脚本

**已修复**：`sql/ry_config_20250224.sql`

在现有的Nacos配置初始化脚本中添加了：

#### 1. 网关路由配置更新
在 `ruoyi-gateway-dev.yml` 配置中添加了车辆管理模块路由：
```yaml
# 车辆管理模块
- id: ruoyi-vehicle
  uri: lb://ruoyi-vehicle
  predicates:
    - Path=/vehicle/**
  filters:
    - StripPrefix=1
```

#### 2. 车辆模块配置
新增了 `ruoyi-vehicle-dev.yml` 配置，包含：
- 数据库连接配置
- Redis连接配置
- MyBatis配置
- SpringDoc API文档配置

#### 3. Sentinel限流配置
在 `sentinel-ruoyi-gateway` 配置中添加了车辆模块限流规则：
```json
{
  "resource": "ruoyi-vehicle",
  "count": 500,
  "grade": 1,
  "limitApp": "default",
  "strategy": 0,
  "controlBehavior": 0
}
```

---

## 🚀 操作步骤

### 步骤1：重新初始化Nacos配置

**重要**：由于车辆管理模块的配置已经集成到初始化脚本中，您需要重新执行Nacos配置初始化：

1. **备份现有配置**（如果有重要的自定义配置）：
   - 访问 `http://**************:8848/nacos`
   - 登录（用户名/密码：nacos/nacos）
   - 导出现有配置

2. **重新初始化配置数据库**：
   ```sql
   -- 在MySQL中执行
   source sql/ry_config_20250224.sql;
   ```

3. **验证配置加载**：
   在Nacos控制台的 `配置管理` -> `配置列表` 中，应该能看到：
   - `ruoyi-gateway-dev.yml` （已包含车辆模块路由）
   - `ruoyi-vehicle-dev.yml` （新增的车辆模块配置）
   - `sentinel-ruoyi-gateway` （已包含车辆模块限流规则）

### 步骤2：重启服务

**重启顺序**：
1. 重启车辆模块服务 (`ruoyi-vehicle`)
2. 重启网关服务 (`ruoyi-gateway`)

### 步骤3：验证服务注册

1. **检查服务注册**：
   在Nacos控制台的 `服务管理` -> `服务列表` 中，应该能看到：
   - `ruoyi-vehicle` 服务
   - `ruoyi-gateway` 服务

2. **检查服务健康状态**：
   确保所有服务的健康状态都是 `UP`

### 步骤4：测试API接口

重启完成后，测试以下接口：
```bash
# 车辆信息列表
curl http://localhost/dev-api/vehicle/info/list?pageNum=1&pageSize=10

# 用车申请列表  
curl http://localhost/dev-api/vehicle/application/list?pageNum=1&pageSize=10
```

---

## 🔍 故障排除

### 问题1：服务未注册到Nacos
**检查方法**：
- 查看Nacos控制台服务列表
- 检查服务启动日志

**解决方案**：
- 确认Nacos地址配置正确
- 检查网络连接
- 重启服务

### 问题2：网关路由不生效
**检查方法**：
- 查看网关启动日志
- 检查Nacos配置是否正确加载

**解决方案**：
- 确认配置文件格式正确
- 重启网关服务
- 检查配置文件的Group和DataID

### 问题3：数据库连接问题
**检查方法**：
- 查看车辆模块启动日志
- 检查数据库连接配置

**解决方案**：
- 确认数据库地址、用户名、密码正确
- 检查数据库服务是否正常运行
- 确认数据库表结构已创建

---

## 📊 验证清单

修复完成后，请逐项检查：

- [ ] Nacos配置中心包含所有必要的配置文件
- [ ] 车辆模块服务在Nacos中正常注册
- [ ] 网关服务在Nacos中正常注册
- [ ] 所有服务健康状态为UP
- [ ] API接口返回正常数据（不是404错误）
- [ ] 前端页面能正常加载数据

---

## 🎯 预期结果

修复完成后：

1. **API接口正常**：
   ```json
   {
     "code": 200,
     "msg": "查询成功",
     "rows": [...],
     "total": 0
   }
   ```

2. **前端页面正常**：
   - 车辆信息页面能正常显示
   - 用车申请页面能正常显示
   - 所有CRUD操作正常

3. **服务架构完整**：
   ```
   前端(Vue) -> 网关(Gateway) -> 车辆模块(Vehicle) -> 数据库(MySQL)
   ```

---

## 📞 技术支持

如果按照以上步骤操作后仍有问题，请检查：

1. **日志文件**：查看各服务的启动日志和错误日志
2. **网络连接**：确认各服务之间网络连通性
3. **端口占用**：确认各服务端口未被占用
4. **配置文件**：仔细检查配置文件的语法和内容

修复完成后，您的车辆管理系统API接口就能正常工作了！🎉
