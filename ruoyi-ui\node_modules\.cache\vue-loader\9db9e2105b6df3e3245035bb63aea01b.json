{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\apply.vue", "mtime": 1754142812526}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHN1Ym1pdEFwcGxpY2F0aW9uLCBhZGRBcHBsaWNhdGlvbiB9IGZyb20gIkAvYXBpL3ZlaGljbGUvYXBwbGljYXRpb24iOwppbXBvcnQgeyBnZXRUZWFtT3B0aW9ucyB9IGZyb20gIkAvYXBpL3ZlaGljbGUvdGVhbSI7CmltcG9ydCB7IGdldEF2YWlsYWJsZVZlaGljbGVzIH0gZnJvbSAiQC9hcGkvdmVoaWNsZS9hcHBsaWNhdGlvbiI7CmltcG9ydCB7IGdldERpY3RzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkFwcGxpY2F0aW9uQXBwbHkiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDooajljZXmlbDmja4KICAgICAgZm9ybTogewogICAgICAgIGFwcGxpY2F0aW9uVGl0bGU6ICcnLAogICAgICAgIHZlaGljbGVUeXBlOiAnJywKICAgICAgICB2ZWhpY2xlTW9kZWw6ICcnLAogICAgICAgIHVzYWdlTG9jYXRpb246ICcnLAogICAgICAgIHdvcmtEZXNjcmlwdGlvbjogJycsCiAgICAgICAgc3RhcnRUaW1lOiAnJywKICAgICAgICBlbmRUaW1lOiAnJywKICAgICAgICB0ZWFtSWQ6IG51bGwsCiAgICAgICAgYXBwbGljYW50OiAnJywKICAgICAgICBhcHBsaWNhbnRQaG9uZTogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICAvLyDmj5DkuqTnirbmgIEKICAgICAgc3VibWl0TG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOmAiemhueaVsOaNrgogICAgICB2ZWhpY2xlVHlwZU9wdGlvbnM6IFtdLAogICAgICB2ZWhpY2xlTW9kZWxPcHRpb25zOiBbXSwKICAgICAgdGVhbU9wdGlvbnM6IFtdLAogICAgICAvLyDovabovobnirbmgIHlr7nor53moYYKICAgICAgc3RhdHVzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHN0YXR1c0xvYWRpbmc6IGZhbHNlLAogICAgICB2ZWhpY2xlU3RhdHVzTGlzdDogW10sCiAgICAgIC8vIOihqOWNlemqjOivgeinhOWImQogICAgICBydWxlczogewogICAgICAgIGFwcGxpY2F0aW9uVGl0bGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlLPor7fmoIfpopjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDEwMCwgbWVzc2FnZTogIumVv+W6puWcqCAyIOWIsCAxMDAg5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHZlaGljbGVUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6L2m6L6G57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdmVoaWNsZU1vZGVsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6L2m6L6G5Z6L5Y+3IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdXNhZ2VMb2NhdGlvbjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUqOi9puWcsOeCueS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB3b3JrRGVzY3JpcHRpb246IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlr3lt6XkvZzkuJror7TmmI7kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAxMCwgbWF4OiA1MDAsIG1lc3NhZ2U6ICLplb/luqblnKggMTAg5YiwIDUwMCDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgc3RhcnRUaW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup55So6L2m5byA5aeL5pe26Ze0IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgZW5kVGltZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeeUqOi9pue7k+adn+aXtumXtCIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHRlYW1JZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqemYn+S8jSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGFwcGxpY2FudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUs+ivt+S6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBhcHBsaWNhbnRQaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxvYWRPcHRpb25zKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L296YCJ6aG55pWw5o2uICovCiAgICBhc3luYyBsb2FkT3B0aW9ucygpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDliqDovb3ovabovobnsbvlnovlrZflhbgKICAgICAgICBjb25zdCB2ZWhpY2xlVHlwZVJlcyA9IGF3YWl0IGdldERpY3RzKCJ2ZWhpY2xlX3R5cGUiKTsKICAgICAgICB0aGlzLnZlaGljbGVUeXBlT3B0aW9ucyA9IHZlaGljbGVUeXBlUmVzLmRhdGE7CiAgICAgICAgCiAgICAgICAgLy8g5Yqg6L296Zif5LyN6YCJ6aG5CiAgICAgICAgY29uc3QgdGVhbVJlcyA9IGF3YWl0IGdldFRlYW1PcHRpb25zKCk7CiAgICAgICAgdGhpcy50ZWFtT3B0aW9ucyA9IHRlYW1SZXMuZGF0YTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5Yqg6L296YCJ6aG55pWw5o2u5aSx6LSlIik7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8qKiDovabovobnsbvlnovlj5jljJblpITnkIYgKi8KICAgIGFzeW5jIGhhbmRsZVZlaGljbGVUeXBlQ2hhbmdlKHZhbHVlKSB7CiAgICAgIHRoaXMuZm9ybS52ZWhpY2xlTW9kZWwgPSAnJzsKICAgICAgdGhpcy52ZWhpY2xlTW9kZWxPcHRpb25zID0gW107CiAgICAgIAogICAgICBpZiAodmFsdWUpIHsKICAgICAgICB0cnkgewogICAgICAgICAgLy8g5qC55o2u6L2m6L6G57G75Z6L5Yqg6L295a+55bqU55qE5Z6L5Y+3CiAgICAgICAgICBjb25zdCBtb2RlbFJlcyA9IGF3YWl0IGdldERpY3RzKCJ2ZWhpY2xlX21vZGVsXyIgKyB2YWx1ZSk7CiAgICAgICAgICB0aGlzLnZlaGljbGVNb2RlbE9wdGlvbnMgPSBtb2RlbFJlcy5kYXRhOwogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oIuWKoOi9vei9pui+huWei+WPt+Wksei0pToiLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgCiAgICAvKiog6Zif5LyN5Y+Y5YyW5aSE55CGICovCiAgICBoYW5kbGVUZWFtQ2hhbmdlKHRlYW1JZCkgewogICAgICBjb25zdCBzZWxlY3RlZFRlYW0gPSB0aGlzLnRlYW1PcHRpb25zLmZpbmQodGVhbSA9PiB0ZWFtLnRlYW1JZCA9PT0gdGVhbUlkKTsKICAgICAgaWYgKHNlbGVjdGVkVGVhbSkgewogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhbnQgPSBzZWxlY3RlZFRlYW0udGVhbUxlYWRlcjsKICAgICAgICB0aGlzLmZvcm0uYXBwbGljYW50UGhvbmUgPSBzZWxlY3RlZFRlYW0ubGVhZGVyUGhvbmU7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8qKiDmmL7npLrovabovobnirbmgIEgKi8KICAgIGFzeW5jIHNob3dWZWhpY2xlU3RhdHVzKCkgewogICAgICB0aGlzLnN0YXR1c0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLnN0YXR1c0xvYWRpbmcgPSB0cnVlOwogICAgICAKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEF2YWlsYWJsZVZlaGljbGVzKHsKICAgICAgICAgIHZlaGljbGVUeXBlOiB0aGlzLmZvcm0udmVoaWNsZVR5cGUsCiAgICAgICAgICBzdGFydFRpbWU6IHRoaXMuZm9ybS5zdGFydFRpbWUsCiAgICAgICAgICBlbmRUaW1lOiB0aGlzLmZvcm0uZW5kVGltZQogICAgICAgIH0pOwogICAgICAgIHRoaXMudmVoaWNsZVN0YXR1c0xpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bovabovobnirbmgIHlpLHotKUiKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLnN0YXR1c0xvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIAogICAgLyoqIOaPkOS6pOihqOWNlSAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIOmqjOivgeaXtumXtOiMg+WbtAogICAgICAgICAgaWYgKG5ldyBEYXRlKHRoaXMuZm9ybS5lbmRUaW1lKSA8PSBuZXcgRGF0ZSh0aGlzLmZvcm0uc3RhcnRUaW1lKSkgewogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi55So6L2m57uT5p2f5pe26Ze05b+F6aG75aSn5LqO5byA5aeL5pe26Ze0Iik7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgdGhpcy5zdWJtaXRMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIHN1Ym1pdEFwcGxpY2F0aW9uKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIueUqOi9pueUs+ivt+aPkOS6pOaIkOWKnyIpOwogICAgICAgICAgICB0aGlzLmdvQmFjaygpOwogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLnN1Ym1pdExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgCiAgICAvKiog5L+d5a2Y6I2J56i/ICovCiAgICBzYXZlRHJhZnQoKSB7CiAgICAgIGNvbnN0IGRyYWZ0RGF0YSA9IHsgLi4udGhpcy5mb3JtLCBhcHByb3ZhbFN0YXR1czogJ2RyYWZ0JyB9OwogICAgICBhZGRBcHBsaWNhdGlvbihkcmFmdERhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuiNieeov+S/neWtmOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDph43nva7ooajljZUgKi8KICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnJlc2V0RmllbGRzKCk7CiAgICAgIHRoaXMudmVoaWNsZU1vZGVsT3B0aW9ucyA9IFtdOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlui9pui+hueKtuaAgeexu+WeiyAqLwogICAgZ2V0VmVoaWNsZVN0YXR1c1R5cGUoc3RhdHVzKSB7CiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsKICAgICAgICAnYXZhaWxhYmxlJzogJ3N1Y2Nlc3MnLAogICAgICAgICdidXN5JzogJ3dhcm5pbmcnLAogICAgICAgICdtYWludGVuYW5jZSc6ICdpbmZvJywKICAgICAgICAnZmF1bHQnOiAnZGFuZ2VyJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlui9pui+hueKtuaAgeaWh+acrCAqLwogICAgZ2V0VmVoaWNsZVN0YXR1c1RleHQoc3RhdHVzKSB7CiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsKICAgICAgICAnYXZhaWxhYmxlJzogJ+WPr+eUqCcsCiAgICAgICAgJ2J1c3knOiAn5L2/55So5LitJywKICAgICAgICAnbWFpbnRlbmFuY2UnOiAn57u05oqk5LitJywKICAgICAgICAnZmF1bHQnOiAn5pWF6ZqcJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpSc7CiAgICB9LAogICAgCiAgICAvKiog6L+U5Zue5YiX6KGoICovCiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvdmVoaWNsZS9hcHBsaWNhdGlvbicpOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/vehicle/application", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">机械用车申请</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\" size=\"medium\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请标题\" prop=\"applicationTitle\">\n              <el-input v-model=\"form.applicationTitle\" placeholder=\"请输入申请标题\" maxlength=\"100\" show-word-limit />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请人\" prop=\"applicant\">\n              <el-input v-model=\"form.applicant\" placeholder=\"请输入申请人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\" @change=\"handleVehicleTypeChange\">\n                <el-option\n                  v-for=\"type in vehicleTypeOptions\"\n                  :key=\"type.dictValue\"\n                  :label=\"type.dictLabel\"\n                  :value=\"type.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-select v-model=\"form.vehicleModel\" placeholder=\"请选择车辆型号\" :disabled=\"!form.vehicleType\">\n                <el-option\n                  v-for=\"model in vehicleModelOptions\"\n                  :key=\"model.dictValue\"\n                  :label=\"model.dictLabel\"\n                  :value=\"model.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"队伍信息\" prop=\"teamId\">\n              <el-select v-model=\"form.teamId\" placeholder=\"请选择队伍\" @change=\"handleTeamChange\">\n                <el-option\n                  v-for=\"team in teamOptions\"\n                  :key=\"team.teamId\"\n                  :label=\"team.teamName\"\n                  :value=\"team.teamId\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"applicantPhone\">\n              <el-input v-model=\"form.applicantPhone\" placeholder=\"请输入联系电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"用车地点\" prop=\"usageLocation\">\n              <el-input v-model=\"form.usageLocation\" placeholder=\"请输入详细的用车地点\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"用车开始时间\" prop=\"startTime\">\n              <el-date-picker\n                v-model=\"form.startTime\"\n                type=\"datetime\"\n                placeholder=\"请选择用车开始时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"用车结束时间\" prop=\"endTime\">\n              <el-date-picker\n                v-model=\"form.endTime\"\n                type=\"datetime\"\n                placeholder=\"请选择用车结束时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"施工作业说明\" prop=\"workDescription\">\n          <el-input\n            v-model=\"form.workDescription\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请详细说明施工作业内容\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息\"\n            maxlength=\"200\"\n            show-word-limit />\n        </el-form-item>\n\n        <!-- 车辆忙闲状态展示 -->\n        <el-form-item label=\"车辆状态\">\n          <el-button type=\"text\" @click=\"showVehicleStatus\" icon=\"el-icon-view\">\n            查看当前车辆忙闲状态\n          </el-button>\n        </el-form-item>\n\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">\n            <i class=\"el-icon-check\"></i> 提交申请\n          </el-button>\n          <el-button @click=\"resetForm\">\n            <i class=\"el-icon-refresh-left\"></i> 重置\n          </el-button>\n          <el-button @click=\"saveDraft\">\n            <i class=\"el-icon-document\"></i> 保存草稿\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 车辆状态对话框 -->\n    <el-dialog title=\"车辆忙闲状态\" :visible.sync=\"statusDialogVisible\" width=\"800px\">\n      <el-table :data=\"vehicleStatusList\" v-loading=\"statusLoading\">\n        <el-table-column label=\"车辆类型\" prop=\"vehicleType\" />\n        <el-table-column label=\"车辆型号\" prop=\"vehicleModel\" />\n        <el-table-column label=\"车牌号\" prop=\"licensePlate\" />\n        <el-table-column label=\"司机\" prop=\"driverName\" />\n        <el-table-column label=\"状态\" prop=\"vehicleStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getVehicleStatusType(scope.row.vehicleStatus)\">\n              {{ getVehicleStatusText(scope.row.vehicleStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"当前位置\" prop=\"currentLocation\" />\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { submitApplication, addApplication } from \"@/api/vehicle/application\";\nimport { getTeamOptions } from \"@/api/vehicle/team\";\nimport { getAvailableVehicles } from \"@/api/vehicle/application\";\nimport { getDicts } from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"ApplicationApply\",\n  data() {\n    return {\n      // 表单数据\n      form: {\n        applicationTitle: '',\n        vehicleType: '',\n        vehicleModel: '',\n        usageLocation: '',\n        workDescription: '',\n        startTime: '',\n        endTime: '',\n        teamId: null,\n        applicant: '',\n        applicantPhone: '',\n        remark: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 选项数据\n      vehicleTypeOptions: [],\n      vehicleModelOptions: [],\n      teamOptions: [],\n      // 车辆状态对话框\n      statusDialogVisible: false,\n      statusLoading: false,\n      vehicleStatusList: [],\n      // 表单验证规则\n      rules: {\n        applicationTitle: [\n          { required: true, message: \"申请标题不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: \"长度在 2 到 100 个字符\", trigger: \"blur\" }\n        ],\n        vehicleType: [\n          { required: true, message: \"请选择车辆类型\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"请选择车辆型号\", trigger: \"change\" }\n        ],\n        usageLocation: [\n          { required: true, message: \"用车地点不能为空\", trigger: \"blur\" }\n        ],\n        workDescription: [\n          { required: true, message: \"施工作业说明不能为空\", trigger: \"blur\" },\n          { min: 10, max: 500, message: \"长度在 10 到 500 个字符\", trigger: \"blur\" }\n        ],\n        startTime: [\n          { required: true, message: \"请选择用车开始时间\", trigger: \"change\" }\n        ],\n        endTime: [\n          { required: true, message: \"请选择用车结束时间\", trigger: \"change\" }\n        ],\n        teamId: [\n          { required: true, message: \"请选择队伍\", trigger: \"change\" }\n        ],\n        applicant: [\n          { required: true, message: \"申请人不能为空\", trigger: \"blur\" }\n        ],\n        applicantPhone: [\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadOptions();\n  },\n  methods: {\n    /** 加载选项数据 */\n    async loadOptions() {\n      try {\n        // 加载车辆类型字典\n        const vehicleTypeRes = await getDicts(\"vehicle_type\");\n        this.vehicleTypeOptions = vehicleTypeRes.data;\n        \n        // 加载队伍选项\n        const teamRes = await getTeamOptions();\n        this.teamOptions = teamRes.data;\n      } catch (error) {\n        this.$modal.msgError(\"加载选项数据失败\");\n      }\n    },\n    \n    /** 车辆类型变化处理 */\n    async handleVehicleTypeChange(value) {\n      this.form.vehicleModel = '';\n      this.vehicleModelOptions = [];\n      \n      if (value) {\n        try {\n          // 根据车辆类型加载对应的型号\n          const modelRes = await getDicts(\"vehicle_model_\" + value);\n          this.vehicleModelOptions = modelRes.data;\n        } catch (error) {\n          console.warn(\"加载车辆型号失败:\", error);\n        }\n      }\n    },\n    \n    /** 队伍变化处理 */\n    handleTeamChange(teamId) {\n      const selectedTeam = this.teamOptions.find(team => team.teamId === teamId);\n      if (selectedTeam) {\n        this.form.applicant = selectedTeam.teamLeader;\n        this.form.applicantPhone = selectedTeam.leaderPhone;\n      }\n    },\n    \n    /** 显示车辆状态 */\n    async showVehicleStatus() {\n      this.statusDialogVisible = true;\n      this.statusLoading = true;\n      \n      try {\n        const response = await getAvailableVehicles({\n          vehicleType: this.form.vehicleType,\n          startTime: this.form.startTime,\n          endTime: this.form.endTime\n        });\n        this.vehicleStatusList = response.data;\n      } catch (error) {\n        this.$modal.msgError(\"获取车辆状态失败\");\n      } finally {\n        this.statusLoading = false;\n      }\n    },\n    \n    /** 提交表单 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 验证时间范围\n          if (new Date(this.form.endTime) <= new Date(this.form.startTime)) {\n            this.$modal.msgError(\"用车结束时间必须大于开始时间\");\n            return;\n          }\n          \n          this.submitLoading = true;\n          submitApplication(this.form).then(response => {\n            this.$modal.msgSuccess(\"用车申请提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 保存草稿 */\n    saveDraft() {\n      const draftData = { ...this.form, approvalStatus: 'draft' };\n      addApplication(draftData).then(response => {\n        this.$modal.msgSuccess(\"草稿保存成功\");\n      });\n    },\n    \n    /** 重置表单 */\n    resetForm() {\n      this.$refs[\"form\"].resetFields();\n      this.vehicleModelOptions = [];\n    },\n    \n    /** 获取车辆状态类型 */\n    getVehicleStatusType(status) {\n      const statusMap = {\n        'available': 'success',\n        'busy': 'warning',\n        'maintenance': 'info',\n        'fault': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取车辆状态文本 */\n    getVehicleStatusText(status) {\n      const statusMap = {\n        'available': '可用',\n        'busy': '使用中',\n        'maintenance': '维护中',\n        'fault': '故障'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/application');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-form-item {\n  margin-bottom: 22px;\n}\n\n.el-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}