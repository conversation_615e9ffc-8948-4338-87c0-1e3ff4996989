package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleViolation;
import com.ruoyi.vehicle.service.IVehicleViolationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 车辆违章记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/violation")
public class VehicleViolationController extends BaseController
{
    @Autowired
    private IVehicleViolationService vehicleViolationService;

    /**
     * 查询车辆违章记录列表
     */
    @RequiresPermissions("vehicle:violation:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleViolation vehicleViolation)
    {
        startPage();
        List<VehicleViolation> list = vehicleViolationService.selectVehicleViolationList(vehicleViolation);
        return getDataTable(list);
    }

    /**
     * 导出车辆违章记录列表
     */
    @RequiresPermissions("vehicle:violation:export")
    @Log(title = "车辆违章记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleViolation vehicleViolation)
    {
        List<VehicleViolation> list = vehicleViolationService.selectVehicleViolationList(vehicleViolation);
        ExcelUtil<VehicleViolation> util = new ExcelUtil<VehicleViolation>(VehicleViolation.class);
        util.exportExcel(response, list, "车辆违章记录数据");
    }

    /**
     * 获取车辆违章记录详细信息
     */
    @RequiresPermissions("vehicle:violation:query")
    @GetMapping(value = "/{violationId}")
    public AjaxResult getInfo(@PathVariable("violationId") Long violationId)
    {
        return success(vehicleViolationService.selectVehicleViolationByViolationId(violationId));
    }

    /**
     * 新增车辆违章记录
     */
    @RequiresPermissions("vehicle:violation:add")
    @Log(title = "车辆违章记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleViolation vehicleViolation)
    {
        vehicleViolation.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleViolationService.insertVehicleViolation(vehicleViolation));
    }

    /**
     * 修改车辆违章记录
     */
    @RequiresPermissions("vehicle:violation:edit")
    @Log(title = "车辆违章记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleViolation vehicleViolation)
    {
        vehicleViolation.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleViolationService.updateVehicleViolation(vehicleViolation));
    }

    /**
     * 删除车辆违章记录
     */
    @RequiresPermissions("vehicle:violation:remove")
    @Log(title = "车辆违章记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{violationIds}")
    public AjaxResult remove(@PathVariable Long[] violationIds)
    {
        return toAjax(vehicleViolationService.deleteVehicleViolationByViolationIds(violationIds));
    }

    /**
     * 根据车辆ID查询违章记录
     */
    @RequiresPermissions("vehicle:violation:list")
    @GetMapping("/vehicle/{vehicleId}")
    public AjaxResult getByVehicleId(@PathVariable Long vehicleId)
    {
        List<VehicleViolation> list = vehicleViolationService.selectVehicleViolationByVehicleId(vehicleId);
        return success(list);
    }

    /**
     * 根据处理状态查询违章记录
     */
    @RequiresPermissions("vehicle:violation:list")
    @GetMapping("/status/{status}")
    public AjaxResult getByStatus(@PathVariable String status)
    {
        List<VehicleViolation> list = vehicleViolationService.selectVehicleViolationByStatus(status);
        return success(list);
    }

    /**
     * 处理违章记录
     */
    @RequiresPermissions("vehicle:violation:process")
    @Log(title = "处理违章记录", businessType = BusinessType.UPDATE)
    @PutMapping("/process/{violationId}")
    public AjaxResult process(@PathVariable Long violationId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleViolationService.processViolation(violationId, operName));
    }

    /**
     * 批量处理违章记录
     */
    @RequiresPermissions("vehicle:violation:process")
    @Log(title = "批量处理违章记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-process")
    public AjaxResult batchProcess(@RequestBody Long[] violationIds)
    {
        String operName = SecurityUtils.getUsername();
        int result = vehicleViolationService.batchProcessViolation(violationIds, operName);
        return success("批量处理完成，成功处理 " + result + " 条违章记录");
    }
}
