package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleApplication;

/**
 * 机械用车申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleApplicationService 
{
    /**
     * 查询机械用车申请
     * 
     * @param applicationId 机械用车申请主键
     * @return 机械用车申请
     */
    public VehicleApplication selectVehicleApplicationByApplicationId(Long applicationId);

    /**
     * 查询机械用车申请列表
     * 
     * @param vehicleApplication 机械用车申请
     * @return 机械用车申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationList(VehicleApplication vehicleApplication);

    /**
     * 新增机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    public int insertVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 修改机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    public int updateVehicleApplication(VehicleApplication vehicleApplication);

    /**
     * 批量删除机械用车申请
     * 
     * @param applicationIds 需要删除的机械用车申请主键集合
     * @return 结果
     */
    public int deleteVehicleApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除机械用车申请信息
     * 
     * @param applicationId 机械用车申请主键
     * @return 结果
     */
    public int deleteVehicleApplicationByApplicationId(Long applicationId);

    /**
     * 提交用车申请
     * 
     * @param vehicleApplication 用车申请信息
     * @param operName 操作人
     * @return 结果
     */
    public int submitApplication(VehicleApplication vehicleApplication, String operName);

    /**
     * 审批用车申请
     * 
     * @param applicationId 申请ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int approveApplication(Long applicationId, String approvalStatus, String approvalComments, String operName);

    /**
     * 分配车辆和司机
     * 
     * @param applicationId 申请ID
     * @param vehicleId 车辆ID
     * @param driverName 司机姓名
     * @param operName 操作人
     * @return 结果
     */
    public int assignVehicleAndDriver(Long applicationId, Long vehicleId, String driverName, String operName);

    /**
     * 根据审批状态查询申请列表
     * 
     * @param approvalStatus 审批状态
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByStatus(String approvalStatus);

    /**
     * 根据队伍ID查询申请列表
     * 
     * @param teamId 队伍ID
     * @return 申请集合
     */
    public List<VehicleApplication> selectVehicleApplicationByTeamId(Long teamId);

    /**
     * 查询待调度的申请列表
     * 
     * @return 申请集合
     */
    public List<VehicleApplication> selectPendingScheduleApplications();

    /**
     * 批量审批申请
     * 
     * @param applicationIds 申请ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int batchApproveApplications(Long[] applicationIds, String approvalStatus, String approvalComments, String operName);
}
