package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleShiftApproval;

/**
 * 台班审批Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleShiftApprovalService 
{
    /**
     * 查询台班审批
     * 
     * @param approvalId 台班审批主键
     * @return 台班审批
     */
    public VehicleShiftApproval selectVehicleShiftApprovalByApprovalId(Long approvalId);

    /**
     * 查询台班审批列表
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 台班审批集合
     */
    public List<VehicleShiftApproval> selectVehicleShiftApprovalList(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 新增台班审批
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 结果
     */
    public int insertVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 修改台班审批
     * 
     * @param vehicleShiftApproval 台班审批
     * @return 结果
     */
    public int updateVehicleShiftApproval(VehicleShiftApproval vehicleShiftApproval);

    /**
     * 批量删除台班审批
     * 
     * @param approvalIds 需要删除的台班审批主键集合
     * @return 结果
     */
    public int deleteVehicleShiftApprovalByApprovalIds(Long[] approvalIds);

    /**
     * 删除台班审批信息
     * 
     * @param approvalId 台班审批主键
     * @return 结果
     */
    public int deleteVehicleShiftApprovalByApprovalId(Long approvalId);

    /**
     * 创建审批记录
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param approvalLevel 审批级别
     * @param approver 审批人
     * @param operName 操作人
     * @return 结果
     */
    public int createApprovalRecord(String businessType, Long businessId, Integer approvalLevel, String approver, String operName);

    /**
     * 审批处理
     * 
     * @param approvalId 审批ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int processApproval(Long approvalId, String approvalStatus, String approvalComments, String operName);

    /**
     * 批量审批处理
     * 
     * @param approvalIds 审批ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int batchProcessApproval(Long[] approvalIds, String approvalStatus, String approvalComments, String operName);

    /**
     * 根据审批人查询待审批列表
     * 
     * @param approver 审批人
     * @return 审批集合
     */
    public List<VehicleShiftApproval> selectPendingApprovalsByApprover(String approver);

    /**
     * 根据业务ID和类型查询审批记录
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 审批集合
     */
    public List<VehicleShiftApproval> selectApprovalsByBusinessIdAndType(Long businessId, String businessType);

    /**
     * 根据审批级别查询待审批列表
     * 
     * @param approvalLevel 审批级别
     * @return 审批集合
     */
    public List<VehicleShiftApproval> selectPendingApprovalsByLevel(Integer approvalLevel);

    /**
     * 检查是否需要下一级审批
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param currentLevel 当前审批级别
     * @return 是否需要下一级审批
     */
    public boolean needNextLevelApproval(String businessType, Long businessId, Integer currentLevel);

    /**
     * 创建下一级审批记录
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param currentLevel 当前审批级别
     * @param operName 操作人
     * @return 结果
     */
    public int createNextLevelApproval(String businessType, Long businessId, Integer currentLevel, String operName);

    /**
     * 获取审批统计信息
     * 
     * @param approver 审批人
     * @return 统计信息
     */
    public Object getApprovalStatistics(String approver);

    /**
     * 查询超时未审批的记录
     * 
     * @param timeoutHours 超时小时数
     * @return 审批集合
     */
    public List<VehicleShiftApproval> selectTimeoutApprovals(Integer timeoutHours);
}
