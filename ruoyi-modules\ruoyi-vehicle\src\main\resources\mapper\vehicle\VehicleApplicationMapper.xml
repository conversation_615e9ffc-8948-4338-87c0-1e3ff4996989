<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleApplicationMapper">
    
    <resultMap type="VehicleApplication" id="VehicleApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="applicationTitle"    column="application_title"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="vehicleModel"    column="vehicle_model"    />
        <result property="usageLocation"    column="usage_location"    />
        <result property="workDescription"    column="work_description"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="teamId"    column="team_id"    />
        <result property="applicant"    column="applicant"    />
        <result property="applicantPhone"    column="applicant_phone"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="assignedVehicleId"    column="assigned_vehicle_id"    />
        <result property="assignedDriver"    column="assigned_driver"    />
        <result property="dispatchTime"    column="dispatch_time"    />
        <result property="dispatchPerson"    column="dispatch_person"    />
        <result property="dispatchRemark"    column="dispatch_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleApplicationVo">
        select application_id, application_title, vehicle_type, vehicle_model, usage_location, 
               work_description, start_time, end_time, team_id, applicant, applicant_phone, 
               approval_status, assigned_vehicle_id, assigned_driver, dispatch_time, dispatch_person, 
               dispatch_remark, create_by, create_time, update_by, update_time, remark 
        from vehicle_application
    </sql>

    <select id="selectVehicleApplicationList" parameterType="VehicleApplication" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        <where>  
            <if test="applicationTitle != null  and applicationTitle != ''"> and application_title like concat('%', #{applicationTitle}, '%')</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="vehicleModel != null  and vehicleModel != ''"> and vehicle_model = #{vehicleModel}</if>
            <if test="usageLocation != null  and usageLocation != ''"> and usage_location like concat('%', #{usageLocation}, '%')</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="assignedVehicleId != null "> and assigned_vehicle_id = #{assignedVehicleId}</if>
            <if test="assignedDriver != null  and assignedDriver != ''"> and assigned_driver like concat('%', #{assignedDriver}, '%')</if>
            <if test="params.beginStartTime != null and params.beginStartTime != ''"><!-- 开始时间检索 -->
                and date_format(start_time,'%y%m%d') &gt;= date_format(#{params.beginStartTime},'%y%m%d')
            </if>
            <if test="params.endStartTime != null and params.endStartTime != ''"><!-- 结束时间检索 -->
                and date_format(start_time,'%y%m%d') &lt;= date_format(#{params.endStartTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleApplicationByApplicationId" parameterType="Long" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where application_id = #{applicationId}
    </select>

    <select id="selectVehicleApplicationByStatus" parameterType="String" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where approval_status = #{approvalStatus}
        order by create_time desc
    </select>

    <select id="selectVehicleApplicationByTeamId" parameterType="Long" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where team_id = #{teamId}
        order by create_time desc
    </select>

    <select id="selectVehicleApplicationByApplicant" parameterType="String" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where applicant = #{applicant}
        order by create_time desc
    </select>

    <select id="selectPendingApplications" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where approval_status = 'pending'
        order by create_time desc
    </select>

    <select id="selectApprovedApplications" resultMap="VehicleApplicationResult">
        <include refid="selectVehicleApplicationVo"/>
        where approval_status = 'approved' and assigned_vehicle_id is null
        order by create_time desc
    </select>
        
    <insert id="insertVehicleApplication" parameterType="VehicleApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into vehicle_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationTitle != null and applicationTitle != ''">application_title,</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model,</if>
            <if test="usageLocation != null and usageLocation != ''">usage_location,</if>
            <if test="workDescription != null and workDescription != ''">work_description,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="teamId != null">team_id,</if>
            <if test="applicant != null and applicant != ''">applicant,</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="assignedVehicleId != null">assigned_vehicle_id,</if>
            <if test="assignedDriver != null">assigned_driver,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="dispatchPerson != null">dispatch_person,</if>
            <if test="dispatchRemark != null">dispatch_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationTitle != null and applicationTitle != ''">#{applicationTitle},</if>
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">#{vehicleModel},</if>
            <if test="usageLocation != null and usageLocation != ''">#{usageLocation},</if>
            <if test="workDescription != null and workDescription != ''">#{workDescription},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="applicant != null and applicant != ''">#{applicant},</if>
            <if test="applicantPhone != null and applicantPhone != ''">#{applicantPhone},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="assignedVehicleId != null">#{assignedVehicleId},</if>
            <if test="assignedDriver != null">#{assignedDriver},</if>
            <if test="dispatchTime != null">#{dispatchTime},</if>
            <if test="dispatchPerson != null">#{dispatchPerson},</if>
            <if test="dispatchRemark != null">#{dispatchRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleApplication" parameterType="VehicleApplication">
        update vehicle_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationTitle != null and applicationTitle != ''">application_title = #{applicationTitle},</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model = #{vehicleModel},</if>
            <if test="usageLocation != null and usageLocation != ''">usage_location = #{usageLocation},</if>
            <if test="workDescription != null and workDescription != ''">work_description = #{workDescription},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="applicant != null and applicant != ''">applicant = #{applicant},</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone = #{applicantPhone},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="assignedVehicleId != null">assigned_vehicle_id = #{assignedVehicleId},</if>
            <if test="assignedDriver != null">assigned_driver = #{assignedDriver},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchPerson != null">dispatch_person = #{dispatchPerson},</if>
            <if test="dispatchRemark != null">dispatch_remark = #{dispatchRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteVehicleApplicationByApplicationId" parameterType="Long">
        delete from vehicle_application where application_id = #{applicationId}
    </delete>

    <delete id="deleteVehicleApplicationByApplicationIds" parameterType="String">
        delete from vehicle_application where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
</mapper>
