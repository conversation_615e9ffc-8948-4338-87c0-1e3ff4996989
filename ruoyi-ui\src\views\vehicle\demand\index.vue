<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="计划标题" prop="planTitle">
        <el-input
          v-model="queryParams.planTitle"
          placeholder="请输入计划标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆类型" prop="vehicleType">
        <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
          <el-option label="草稿" value="draft"></el-option>
          <el-option label="待项目调度室审批" value="pending_level1"></el-option>
          <el-option label="待机械主管审批" value="pending_level2"></el-option>
          <el-option label="待经营部门审批" value="pending_level3"></el-option>
          <el-option label="已通过" value="approved"></el-option>
          <el-option label="已拒绝" value="rejected"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vehicle:demand:add']"
        >新增申请</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vehicle:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vehicle:demand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:demand:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          @click="handleMyPlans"
          v-hasPermi="['vehicle:demand:list']"
        >我的申请</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="demandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="计划ID" align="center" prop="planId" width="80" />
      <el-table-column label="计划标题" align="center" prop="planTitle" :show-overflow-tooltip="true" />
      <el-table-column label="车辆信息" align="center" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.vehicleType }}</div>
          <div style="color: #909399; font-size: 12px;">{{ scope.row.vehicleModel }}</div>
        </template>
      </el-table-column>
      <el-table-column label="需求单位" align="center" prop="demandUnit" />
      <el-table-column label="需求数量" align="center" prop="demandQuantity" width="80" />
      <el-table-column label="需求时间" align="center" width="180">
        <template slot-scope="scope">
          <div>{{ parseTime(scope.row.demandStartTime, '{m}-{d} {h}:{i}') }}</div>
          <div style="color: #909399; font-size: 12px;">
            至 {{ parseTime(scope.row.demandEndTime, '{m}-{d} {h}:{i}') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="applicant" width="100" />
      <el-table-column label="审批状态" align="center" prop="approvalStatus" width="120">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusTagType(scope.row.approvalStatus)"
            size="mini">
            {{ getStatusText(scope.row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['vehicle:demand:query']"
          >查看</el-button>
          <el-button
            v-if="scope.row.approvalStatus === 'draft'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vehicle:demand:edit']"
          >修改</el-button>
          <el-button
            v-if="canApprove(scope.row.approvalStatus)"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleApprove(scope.row)"
            v-hasPermi="['vehicle:demand:approve']"
          >审批</el-button>
          <el-button
            v-if="scope.row.approvalStatus === 'draft'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vehicle:demand:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog title="需求计划详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="计划标题" :span="2">{{ detailForm.planTitle }}</el-descriptions-item>
        <el-descriptions-item label="车辆类型">{{ detailForm.vehicleType }}</el-descriptions-item>
        <el-descriptions-item label="车辆型号">{{ detailForm.vehicleModel }}</el-descriptions-item>
        <el-descriptions-item label="需求单位">{{ detailForm.demandUnit }}</el-descriptions-item>
        <el-descriptions-item label="需求数量">{{ detailForm.demandQuantity }}</el-descriptions-item>
        <el-descriptions-item label="需求开始时间">{{ parseTime(detailForm.demandStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="需求结束时间">{{ parseTime(detailForm.demandEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ detailForm.applicant }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.applicantPhone }}</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getStatusTagType(detailForm.approvalStatus)">
            {{ getStatusText(detailForm.approvalStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="用途说明" :span="2">{{ detailForm.usagePurpose }}</el-descriptions-item>
        <el-descriptions-item v-if="detailForm.approvalComments" label="审批意见" :span="2">
          {{ detailForm.approvalComments }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailForm.remark" label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listDemand, getDemand, delDemand, getMyDemands } from "@/api/vehicle/demand";

export default {
  name: "Demand",
  dicts: ['vehicle_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求计划表格数据
      demandList: [],
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情表单参数
      detailForm: {},
      // 申请时间时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planTitle: null,
        vehicleType: null,
        approvalStatus: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询需求计划列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.planId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/vehicle/demand/apply');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const planId = row.planId || this.ids[0];
      this.$router.push('/vehicle/demand/edit/' + planId);
    },
    /** 查看按钮操作 */
    handleView(row) {
      getDemand(row.planId).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 审批按钮操作 */
    handleApprove(row) {
      this.$router.push('/vehicle/demand/approve/' + row.planId);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const planIds = row.planId || this.ids;
      this.$modal.confirm('是否确认删除需求计划编号为"' + planIds + '"的数据项？').then(function() {
        return delDemand(planIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 我的申请 */
    handleMyPlans() {
      getMyDemands().then(response => {
        this.demandList = response.data;
        this.total = response.data.length;
        this.$modal.msgSuccess("已切换到我的申请");
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/demand/export', {
        ...this.queryParams
      }, `demand_${new Date().getTime()}.xlsx`)
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'draft': 'info',
        'pending_level1': 'warning',
        'pending_level2': 'warning', 
        'pending_level3': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      };
      return statusMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'draft': '草稿',
        'pending_level1': '待项目调度室审批',
        'pending_level2': '待机械主管审批',
        'pending_level3': '待经营部门审批',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '未知';
    },
    /** 判断是否可以审批 */
    canApprove(status) {
      return ['pending_level1', 'pending_level2', 'pending_level3'].includes(status);
    }
  }
};
</script>
