#!/bin/bash

# =============================================================================
# RuoYi-Cloud 一键部署配置文件
# =============================================================================

# 部署路径配置
export DEPLOY_ROOT="/erdCloud"
export DATA_ROOT="/home"

# Docker镜像源配置 (按优先级排序，支持多镜像源故障转移)
export DOCKER_REGISTRIES=(
    "05f073ad3c0010ea0f4bc00b7105ec20.mirror.swr.myhuaweicloud.com"
    "swr.cn-north-4.myhuaweicloud.com"
    "ccr.ccs.tencentyun.com"
    "mirror.ccs.tencentyun.com"
    "docker.xuanyuan.me"
    "xuanyuan.cloud"
    "docker.mirrors.ustc.edu.cn"
    "registry.docker-cn.com"
    "docker.io"
)

# 镜像配置列表 (可自定义添加)
declare -A IMAGES
IMAGES[mysql]="mysql:5.7"
IMAGES[dameng]="laglangyue/dmdb8:latest"
IMAGES[redis]="redis:7.0-alpine"
IMAGES[nacos]="nacos/nacos-server:v2.2.3"
IMAGES[nginx]="nginx:1.24-alpine"
IMAGES[openjdk]="openjdk:8-jre-alpine"
# IMAGES[portainer]="portainer/portainer-ce:latest"  # 注释掉英文版，使用中文版

# 中文版Portainer镜像 (如果需要中文界面)
IMAGES[portainer_cn]="portainer/portainer-ce:latest"

# 端口配置
declare -A PORTS
PORTS[mysql]=3306
PORTS[dameng]=5236
PORTS[redis]=6379
PORTS[nacos]=8848
PORTS[nacos_grpc1]=9848
PORTS[nacos_grpc2]=9849
PORTS[nginx]=80
PORTS[gateway]=8080
PORTS[auth]=9200
PORTS[system]=9201
PORTS[gen]=9202
PORTS[job]=9203
PORTS[file]=9300
PORTS[monitor]=9100
PORTS[portainer]=9000

# MySQL数据库配置
export MYSQL_ROOT_PASSWORD="RuoYi@2024"
export MYSQL_DATABASE="ry-cloud"
export MYSQL_DATABASE_NACOS="nacos"
export MYSQL_USER="ruoyi"
export MYSQL_PASSWORD="RuoYi@2024"

# 达梦数据库配置
export DAMENG_SYSDBA_PASSWORD="SYSDBA001"
export DAMENG_DATABASE="RUOYICLOUD"
export DAMENG_USER="RUOYI"
export DAMENG_PASSWORD="RuoYi@2024"
export DAMENG_INSTANCE_NAME="dm8_test"
export DAMENG_PORT_NUM="5236"

# Redis配置
export REDIS_PASSWORD="RuoYi@2024"

# Nacos配置
export NACOS_MODE="standalone"

# 网络配置
export DOCKER_NETWORK="ruoyi-network"

# 日志配置
export LOG_LEVEL="INFO"
export LOG_MAX_SIZE="100m"
export LOG_MAX_FILES="10"

# 资源限制配置
declare -A MEMORY_LIMITS
MEMORY_LIMITS[mysql]="512m"
MEMORY_LIMITS[redis]="256m"
MEMORY_LIMITS[nacos]="1g"
MEMORY_LIMITS[nginx]="128m"
MEMORY_LIMITS[gateway]="512m"
MEMORY_LIMITS[auth]="512m"
MEMORY_LIMITS[system]="512m"
MEMORY_LIMITS[gen]="256m"
MEMORY_LIMITS[job]="256m"
MEMORY_LIMITS[file]="256m"
MEMORY_LIMITS[monitor]="256m"
MEMORY_LIMITS[portainer]="128m"

# JVM配置
export JVM_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+PrintGCDetails"

# 健康检查配置
export HEALTH_CHECK_INTERVAL="30s"
export HEALTH_CHECK_TIMEOUT="10s"
export HEALTH_CHECK_RETRIES=3

# 备份配置
export BACKUP_ENABLED=true
export BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份
export BACKUP_RETENTION_DAYS=7

# 监控配置
export MONITORING_ENABLED=true

# 安全配置
export ENABLE_FIREWALL=true
export ENABLE_SSL=false

# 构建配置
export MAVEN_OPTS="-Xmx1024m"
export NODE_OPTIONS="--max-old-space-size=4096"

# 颜色输出配置
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export PURPLE='\033[0;35m'
export CYAN='\033[0;36m'
export WHITE='\033[1;37m'
export NC='\033[0m' # No Color

# 函数：打印彩色日志
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "$LOG_LEVEL" == "DEBUG" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 函数：检查命令是否存在（静默检查）
check_command() {
    command -v $1 &> /dev/null
}

# 函数：检查命令是否存在（带日志输出）
check_command_with_log() {
    if ! command -v $1 &> /dev/null; then
        log_error "命令 $1 未找到，请先安装"
        return 1
    fi
    return 0
}

# 函数：创建目录
create_directory() {
    local dir=$1
    if [[ ! -d "$dir" ]]; then
        log_info "创建目录: $dir"
        mkdir -p "$dir"
        if [[ $? -ne 0 ]]; then
            log_error "创建目录失败: $dir"
            return 1
        fi
    fi
    return 0
}

# 函数：拉取Docker镜像（直接使用配置的镜像源列表）
pull_image() {
    local image_name=$1
    local image_tag=${2:-"latest"}
    local full_image="${image_name}:${image_tag}"

    # 检查镜像是否已经存在
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${full_image}$"; then
        log_info "镜像已存在，跳过下载: $full_image"
        return 0
    fi

    log_info "开始拉取镜像: $full_image"
    log_info "将依次尝试 ${#DOCKER_REGISTRIES[@]} 个镜像源"

    for registry in "${DOCKER_REGISTRIES[@]}"; do
        # 构建镜像源地址
        local mirror_image

        if [[ "$registry" == "docker.io" ]]; then
            # Docker官方源，直接使用原始镜像名
            mirror_image="$full_image"
        elif [[ "$image_name" == *"/"* ]]; then
            # 第三方镜像（如 nacos/nacos-server, portainer/portainer-ce）
            mirror_image="${registry}/${full_image}"
        else
            # 官方镜像（如 mysql, redis, nginx）
            # 根据镜像源类型决定是否添加library前缀
            if [[ "$registry" =~ "ustc.edu.cn" ]] || [[ "$registry" =~ "163.com" ]]; then
                # 中科大、网易镜像源需要library前缀
                mirror_image="${registry}/library/${full_image}"
            else
                # 其他镜像源直接使用
                mirror_image="${registry}/${full_image}"
            fi
        fi

        log_info "尝试从镜像源拉取: $mirror_image"

        if docker pull "$mirror_image" 2>/dev/null; then
            log_info "成功从镜像源拉取: $mirror_image"

            # 如果不是原始镜像名，需要重新标记
            if [[ "$mirror_image" != "$full_image" ]]; then
                docker tag "$mirror_image" "$full_image" 2>/dev/null
                # 删除带镜像源前缀的标签
                docker rmi "$mirror_image" 2>/dev/null || true
                log_info "镜像重新标记为: $full_image"
            fi
            return 0
        else
            log_warn "从镜像源 $registry 拉取失败"
        fi
    done

    log_error "所有镜像源都拉取失败: $full_image"
    log_info "已尝试的镜像源:"
    for registry in "${DOCKER_REGISTRIES[@]}"; do
        log_info "  - $registry"
    done

    log_info "故障排除建议:"
    log_info "  1. 检查网络连接: ping -c 3 ${DOCKER_REGISTRIES[0]}"
    log_info "  2. 检查Docker daemon状态: systemctl status docker"
    log_info "  3. 手动测试第一个镜像源: docker pull ${DOCKER_REGISTRIES[0]}/${full_image}"
    log_info "  4. 重启Docker服务: systemctl restart docker"
    log_info "  5. 检查防火墙设置: firewall-cmd --list-all"
    log_info "  6. 如果网络受限，可以考虑使用离线镜像包"

    return 1
}

# 函数：检查单个端口是否可访问
check_port_accessible() {
    local port=$1
    local timeout_seconds=${2:-3}

    # 使用nc命令检查端口
    if command -v nc >/dev/null 2>&1; then
        if timeout "$timeout_seconds" nc -z localhost "$port" 2>/dev/null; then
            return 0
        fi
    fi

    # 如果nc不可用，使用bash内置的网络检查
    if timeout "$timeout_seconds" bash -c "exec 3<>/dev/tcp/localhost/$port && exec 3<&-" 2>/dev/null; then
        return 0
    fi

    return 1
}

# 函数：等待服务启动
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_wait=${3:-60}
    local check_interval=10
    local max_checks=3
    local check_count=0

    log_info "等待服务启动: $service_name (端口: $port)"

    while [[ $check_count -lt $max_checks ]]; do
        check_count=$((check_count + 1))
        log_info "第 $check_count/$max_checks 次检查服务 $service_name"

        if check_port_accessible "$port" 3; then
            log_info "服务 $service_name 已启动"
            return 0
        fi

        if [[ $check_count -lt $max_checks ]]; then
            log_info "服务 $service_name 未就绪，${check_interval}秒后重试..."
            sleep $check_interval
        fi
    done

    log_warn "服务 $service_name 在 $((max_checks * check_interval)) 秒内未启动，继续执行后续步骤"
    return 1
}

# 函数：检查Docker服务状态
check_docker_status() {
    if ! systemctl is-active --quiet docker; then
        log_error "Docker服务未运行，请先启动Docker服务"
        return 1
    fi
    return 0
}

# 函数：检查磁盘空间
check_disk_space() {
    local required_space_gb=${1:-10}
    local available_space=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    
    if [[ $available_space -lt $required_space_gb ]]; then
        log_error "磁盘空间不足，需要至少 ${required_space_gb}GB，当前可用 ${available_space}GB"
        return 1
    fi
    
    log_info "磁盘空间检查通过，可用空间: ${available_space}GB"
    return 0
}

# 函数：检查内存
check_memory() {
    local required_memory_gb=${1:-4}
    local available_memory=$(free -g | awk 'NR==2{print $2}')
    
    if [[ $available_memory -lt $required_memory_gb ]]; then
        log_warn "内存可能不足，建议至少 ${required_memory_gb}GB，当前 ${available_memory}GB"
    else
        log_info "内存检查通过，总内存: ${available_memory}GB"
    fi
}

# 导出所有函数供其他脚本使用
export -f log_info log_warn log_error log_debug
export -f check_command check_command_with_log create_directory pull_image wait_for_service check_port_accessible
export -f check_docker_status check_disk_space check_memory
