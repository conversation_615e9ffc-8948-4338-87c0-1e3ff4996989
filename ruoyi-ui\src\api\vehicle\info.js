import request from '@/utils/request'

// 查询机械车辆信息列表
export function listVehicleInfo(query) {
  return request({
    url: '/vehicle/info/list',
    method: 'get',
    params: query
  })
}

// 查询车辆信息列表（别名方法）
export function listInfo(query) {
  return request({
    url: '/vehicle/info/list',
    method: 'get',
    params: query
  })
}

// 查询机械车辆信息详细
export function getVehicleInfo(vehicleId) {
  return request({
    url: '/vehicle/info/' + vehicleId,
    method: 'get'
  })
}

// 新增机械车辆信息
export function addVehicleInfo(data) {
  return request({
    url: '/vehicle/info',
    method: 'post',
    data: data
  })
}

// 修改机械车辆信息
export function updateVehicleInfo(data) {
  return request({
    url: '/vehicle/info',
    method: 'put',
    data: data
  })
}

// 删除机械车辆信息
export function delVehicleInfo(vehicleId) {
  return request({
    url: '/vehicle/info/' + vehicleId,
    method: 'delete'
  })
}

// 导出机械车辆信息
export function exportVehicleInfo(query) {
  return request({
    url: '/vehicle/info/export',
    method: 'post',
    data: query
  })
}

// 获取可用车辆列表
export function getAvailableVehicles() {
  return request({
    url: '/vehicle/info/available',
    method: 'get'
  })
}

// 根据车辆类型获取车辆列表
export function getVehiclesByType(vehicleType) {
  return request({
    url: '/vehicle/info/type/' + vehicleType,
    method: 'get'
  })
}

// 更新车辆状态
export function updateVehicleStatus(vehicleId, status) {
  return request({
    url: '/vehicle/info/status/' + vehicleId + '/' + status,
    method: 'put'
  })
}

// 导入车辆信息数据
export function importVehicleData(data) {
  return request({
    url: '/vehicle/info/importData',
    method: 'post',
    data: data
  })
}

// 下载车辆信息导入模板
export function importTemplate() {
  return request({
    url: '/vehicle/info/importTemplate',
    method: 'post'
  })
}
