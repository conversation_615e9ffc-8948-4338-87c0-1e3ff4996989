{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\maintenance\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\maintenance\\index.vue", "mtime": 1754141729388}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RNYWludGVuYW5jZSwgZ2V0TWFpbnRlbmFuY2UsIGRlbE1haW50ZW5hbmNlLCBhZGRNYWludGVuYW5jZSwgdXBkYXRlTWFpbnRlbmFuY2UgfSBmcm9tICJAL2FwaS92ZWhpY2xlL21haW50ZW5hbmNlIjsKaW1wb3J0IHsgbGlzdEluZm8gfSBmcm9tICJAL2FwaS92ZWhpY2xlL2luZm8iOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJNYWludGVuYW5jZSIsCiAgZGljdHM6IFsnbWFpbnRlbmFuY2Vfc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnu7Tkv67orrDlvZXooajmoLzmlbDmja4KICAgICAgbWFpbnRlbmFuY2VMaXN0OiBbXSwKICAgICAgLy8g6L2m6L6G6YCJ6aG5CiAgICAgIHZlaGljbGVPcHRpb25zOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLror6bmg4XlvLnlh7rlsYIKICAgICAgZGV0YWlsT3BlbjogZmFsc2UsCiAgICAgIC8vIOe7tOS/ruaXpeacn+aXtumXtOiMg+WbtAogICAgICBkYXRlcmFuZ2VNYWludGVuYW5jZURhdGU6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB2ZWhpY2xlSWQ6IG51bGwsCiAgICAgICAgbWFpbnRlbmFuY2VUeXBlOiBudWxsLAogICAgICAgIG1haW50ZW5hbmNlRGF0ZTogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDor6bmg4XooajljZXlj4LmlbAKICAgICAgZGV0YWlsRm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHZlaGljbGVJZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui9pui+huS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIG1haW50ZW5hbmNlVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue7tOS/ruexu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIG1haW50ZW5hbmNlRGF0ZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue7tOS/ruaXpeacn+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBmYXVsdERlc2NyaXB0aW9uOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pWF6Zqc5o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldFZlaGljbGVPcHRpb25zKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i57u05L+u6K6w5b2V5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcyA9IHt9OwogICAgICBpZiAobnVsbCAhPSB0aGlzLmRhdGVyYW5nZU1haW50ZW5hbmNlRGF0ZSAmJiAnJyAhPSB0aGlzLmRhdGVyYW5nZU1haW50ZW5hbmNlRGF0ZSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJiZWdpbk1haW50ZW5hbmNlRGF0ZSJdID0gdGhpcy5kYXRlcmFuZ2VNYWludGVuYW5jZURhdGVbMF07CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXNbImVuZE1haW50ZW5hbmNlRGF0ZSJdID0gdGhpcy5kYXRlcmFuZ2VNYWludGVuYW5jZURhdGVbMV07CiAgICAgIH0KICAgICAgbGlzdE1haW50ZW5hbmNlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubWFpbnRlbmFuY2VMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5bovabovobpgInpobkgKi8KICAgIGdldFZlaGljbGVPcHRpb25zKCkgewogICAgICBsaXN0SW5mbygpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMudmVoaWNsZU9wdGlvbnMgPSByZXNwb25zZS5yb3dzOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgbWFpbnRlbmFuY2VJZDogbnVsbCwKICAgICAgICB2ZWhpY2xlSWQ6IG51bGwsCiAgICAgICAgbWFpbnRlbmFuY2VUeXBlOiBudWxsLAogICAgICAgIG1haW50ZW5hbmNlRGF0ZTogbnVsbCwKICAgICAgICBmYXVsdERlc2NyaXB0aW9uOiBudWxsLAogICAgICAgIG1haW50ZW5hbmNlQ29udGVudDogbnVsbCwKICAgICAgICBtYWludGVuYW5jZUNvc3Q6IG51bGwsCiAgICAgICAgbWFpbnRlbmFuY2VQZXJzb246IG51bGwsCiAgICAgICAgc3RhdHVzOiAicGVuZGluZyIsCiAgICAgICAgcmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZXJhbmdlTWFpbnRlbmFuY2VEYXRlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm1haW50ZW5hbmNlSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg57u05L+u6K6w5b2VIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgbWFpbnRlbmFuY2VJZCA9IHJvdy5tYWludGVuYW5jZUlkIHx8IHRoaXMuaWRzCiAgICAgIGdldE1haW50ZW5hbmNlKG1haW50ZW5hbmNlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuee7tOS/ruiusOW9lSI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIGdldE1haW50ZW5hbmNlKHJvdy5tYWludGVuYW5jZUlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRldGFpbEZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLm1haW50ZW5hbmNlSWQgIT0gbnVsbCkgewogICAgICAgICAgICB1cGRhdGVNYWludGVuYW5jZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZE1haW50ZW5hbmNlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBtYWludGVuYW5jZUlkcyA9IHJvdy5tYWludGVuYW5jZUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTnu7Tkv67orrDlvZXnvJblj7fkuLoiJyArIG1haW50ZW5hbmNlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxNYWludGVuYW5jZShtYWludGVuYW5jZUlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOi0ueeUqOe7n+iuoSAqLwogICAgaGFuZGxlU3RhdGlzdGljcygpIHsKICAgICAgdGhpcy4kbW9kYWwubXNnSW5mbygi6LS555So57uf6K6h5Yqf6IO95byA5Y+R5LitLi4uIik7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCd2ZWhpY2xlL21haW50ZW5hbmNlL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGBtYWludGVuYW5jZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgQA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/maintenance", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"车辆ID\" prop=\"vehicleId\">\n        <el-select v-model=\"queryParams.vehicleId\" placeholder=\"请选择车辆\" clearable>\n          <el-option\n            v-for=\"vehicle in vehicleOptions\"\n            :key=\"vehicle.vehicleId\"\n            :label=\"vehicle.vehicleModel\"\n            :value=\"vehicle.vehicleId\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"维修类型\" prop=\"maintenanceType\">\n        <el-select v-model=\"queryParams.maintenanceType\" placeholder=\"请选择维修类型\" clearable>\n          <el-option label=\"定期保养\" value=\"定期保养\"></el-option>\n          <el-option label=\"故障维修\" value=\"故障维修\"></el-option>\n          <el-option label=\"大修\" value=\"大修\"></el-option>\n          <el-option label=\"其他\" value=\"其他\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"维修状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择维修状态\" clearable>\n          <el-option label=\"待维修\" value=\"pending\"></el-option>\n          <el-option label=\"维修中\" value=\"repairing\"></el-option>\n          <el-option label=\"已完成\" value=\"completed\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"维修日期\">\n        <el-date-picker\n          v-model=\"daterangeMaintenanceDate\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:maintenance:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:maintenance:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:maintenance:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:maintenance:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-s-data\"\n          size=\"mini\"\n          @click=\"handleStatistics\"\n          v-hasPermi=\"['vehicle:maintenance:list']\"\n        >费用统计</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"maintenanceList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"维修ID\" align=\"center\" prop=\"maintenanceId\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" prop=\"vehicleInfo\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"维修类型\" align=\"center\" prop=\"maintenanceType\" />\n      <el-table-column label=\"维修日期\" align=\"center\" prop=\"maintenanceDate\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.maintenanceDate, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"故障描述\" align=\"center\" prop=\"faultDescription\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"维修费用\" align=\"center\" prop=\"maintenanceCost\">\n        <template slot-scope=\"scope\">\n          <span style=\"color: #E6A23C;\">¥{{ scope.row.maintenanceCost }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"维修状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.maintenance_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['vehicle:maintenance:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:maintenance:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:maintenance:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改维修记录对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆\" prop=\"vehicleId\">\n              <el-select v-model=\"form.vehicleId\" placeholder=\"请选择车辆\">\n                <el-option\n                  v-for=\"vehicle in vehicleOptions\"\n                  :key=\"vehicle.vehicleId\"\n                  :label=\"vehicle.vehicleModel\"\n                  :value=\"vehicle.vehicleId\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"维修类型\" prop=\"maintenanceType\">\n              <el-select v-model=\"form.maintenanceType\" placeholder=\"请选择维修类型\">\n                <el-option label=\"定期保养\" value=\"定期保养\"></el-option>\n                <el-option label=\"故障维修\" value=\"故障维修\"></el-option>\n                <el-option label=\"大修\" value=\"大修\"></el-option>\n                <el-option label=\"其他\" value=\"其他\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"维修日期\" prop=\"maintenanceDate\">\n              <el-date-picker clearable\n                v-model=\"form.maintenanceDate\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择维修日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"维修费用\" prop=\"maintenanceCost\">\n              <el-input v-model=\"form.maintenanceCost\" placeholder=\"请输入维修费用\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"故障描述\" prop=\"faultDescription\">\n          <el-input v-model=\"form.faultDescription\" type=\"textarea\" placeholder=\"请输入故障描述\" />\n        </el-form-item>\n        <el-form-item label=\"维修内容\" prop=\"maintenanceContent\">\n          <el-input v-model=\"form.maintenanceContent\" type=\"textarea\" placeholder=\"请输入维修内容\" />\n        </el-form-item>\n        <el-form-item label=\"维修人员\" prop=\"maintenancePerson\">\n          <el-input v-model=\"form.maintenancePerson\" placeholder=\"请输入维修人员\" />\n        </el-form-item>\n        <el-form-item label=\"维修状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"pending\">待维修</el-radio>\n            <el-radio label=\"repairing\">维修中</el-radio>\n            <el-radio label=\"completed\">已完成</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 维修记录详情对话框 -->\n    <el-dialog title=\"维修记录详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailForm.vehicleInfo ? detailForm.vehicleInfo.vehicleModel : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"维修类型\">{{ detailForm.maintenanceType }}</el-descriptions-item>\n        <el-descriptions-item label=\"维修日期\">{{ detailForm.maintenanceDate }}</el-descriptions-item>\n        <el-descriptions-item label=\"维修费用\">¥{{ detailForm.maintenanceCost }}</el-descriptions-item>\n        <el-descriptions-item label=\"维修人员\">{{ detailForm.maintenancePerson }}</el-descriptions-item>\n        <el-descriptions-item label=\"维修状态\">\n          <dict-tag :options=\"dict.type.maintenance_status\" :value=\"detailForm.status\"/>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"故障描述\" :span=\"2\">{{ detailForm.faultDescription }}</el-descriptions-item>\n        <el-descriptions-item label=\"维修内容\" :span=\"2\">{{ detailForm.maintenanceContent }}</el-descriptions-item>\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ detailForm.remark }}</el-descriptions-item>\n      </el-descriptions>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listMaintenance, getMaintenance, delMaintenance, addMaintenance, updateMaintenance } from \"@/api/vehicle/maintenance\";\nimport { listInfo } from \"@/api/vehicle/info\";\n\nexport default {\n  name: \"Maintenance\",\n  dicts: ['maintenance_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 维修记录表格数据\n      maintenanceList: [],\n      // 车辆选项\n      vehicleOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详情弹出层\n      detailOpen: false,\n      // 维修日期时间范围\n      daterangeMaintenanceDate: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleId: null,\n        maintenanceType: null,\n        maintenanceDate: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 详情表单参数\n      detailForm: {},\n      // 表单校验\n      rules: {\n        vehicleId: [\n          { required: true, message: \"车辆不能为空\", trigger: \"change\" }\n        ],\n        maintenanceType: [\n          { required: true, message: \"维修类型不能为空\", trigger: \"change\" }\n        ],\n        maintenanceDate: [\n          { required: true, message: \"维修日期不能为空\", trigger: \"blur\" }\n        ],\n        faultDescription: [\n          { required: true, message: \"故障描述不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getVehicleOptions();\n  },\n  methods: {\n    /** 查询维修记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.daterangeMaintenanceDate && '' != this.daterangeMaintenanceDate) {\n        this.queryParams.params[\"beginMaintenanceDate\"] = this.daterangeMaintenanceDate[0];\n        this.queryParams.params[\"endMaintenanceDate\"] = this.daterangeMaintenanceDate[1];\n      }\n      listMaintenance(this.queryParams).then(response => {\n        this.maintenanceList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 获取车辆选项 */\n    getVehicleOptions() {\n      listInfo().then(response => {\n        this.vehicleOptions = response.rows;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        maintenanceId: null,\n        vehicleId: null,\n        maintenanceType: null,\n        maintenanceDate: null,\n        faultDescription: null,\n        maintenanceContent: null,\n        maintenanceCost: null,\n        maintenancePerson: null,\n        status: \"pending\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.daterangeMaintenanceDate = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.maintenanceId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加维修记录\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const maintenanceId = row.maintenanceId || this.ids\n      getMaintenance(maintenanceId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改维修记录\";\n      });\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      getMaintenance(row.maintenanceId).then(response => {\n        this.detailForm = response.data;\n        this.detailOpen = true;\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.maintenanceId != null) {\n            updateMaintenance(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addMaintenance(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const maintenanceIds = row.maintenanceId || this.ids;\n      this.$modal.confirm('是否确认删除维修记录编号为\"' + maintenanceIds + '\"的数据项？').then(function() {\n        return delMaintenance(maintenanceIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 费用统计 */\n    handleStatistics() {\n      this.$modal.msgInfo(\"费用统计功能开发中...\");\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/maintenance/export', {\n        ...this.queryParams\n      }, `maintenance_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"]}]}