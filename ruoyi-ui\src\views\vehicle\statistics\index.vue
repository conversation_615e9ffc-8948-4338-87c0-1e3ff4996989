<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="选择开始日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="选择结束日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="车辆类型" prop="vehicleType">
        <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #409EFF;">
              <i class="el-icon-truck" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overallStats.totalVehicles || 0 }}</div>
              <div class="stat-label">车辆总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #67C23A;">
              <i class="el-icon-s-order" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overallStats.totalOrders || 0 }}</div>
              <div class="stat-label">订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #E6A23C;">
              <i class="el-icon-time" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overallStats.totalWorkHours || 0 }}</div>
              <div class="stat-label">总工作时长</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="stat-card">
            <div class="stat-icon" style="background: #F56C6C;">
              <i class="el-icon-warning" style="color: white; font-size: 24px;"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overallStats.utilizationRate || 0 }}%</div>
              <div class="stat-label">车辆利用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>车辆类型统计</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshVehicleTypeChart">刷新</el-button>
          </div>
          <div id="vehicleTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>队伍用车统计</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTeamChart">刷新</el-button>
          </div>
          <div id="teamChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>时间趋势统计</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeTimeRange('day')" :type="timeRange === 'day' ? 'primary' : ''">按天</el-button>
              <el-button size="mini" @click="changeTimeRange('month')" :type="timeRange === 'month' ? 'primary' : ''">按月</el-button>
            </el-button-group>
          </div>
          <div id="trendChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>车辆使用排行榜</span>
          </div>
          <el-table :data="vehicleRanking" style="width: 100%" size="small">
            <el-table-column prop="rank" label="排名" width="60" align="center" />
            <el-table-column prop="vehicleModel" label="车辆型号" />
            <el-table-column prop="usageCount" label="使用次数" align="center" />
            <el-table-column prop="workHours" label="工作时长" align="center" />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>队伍用车排行榜</span>
          </div>
          <el-table :data="teamRanking" style="width: 100%" size="small">
            <el-table-column prop="rank" label="排名" width="60" align="center" />
            <el-table-column prop="teamName" label="队伍名称" />
            <el-table-column prop="orderCount" label="订单数量" align="center" />
            <el-table-column prop="workHours" label="工作时长" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 导出按钮 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24" style="text-align: center;">
        <el-button type="primary" icon="el-icon-download" @click="handleExport">导出统计报表</el-button>
        <el-button type="success" icon="el-icon-refresh" @click="handleRefreshAll">刷新所有数据</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "VehicleStatistics",
  dicts: ['vehicle_type'],
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        startDate: null,
        endDate: null,
        vehicleType: null
      },
      // 总体统计数据
      overallStats: {},
      // 时间范围
      timeRange: 'day',
      // 车辆排行榜
      vehicleRanking: [],
      // 队伍排行榜
      teamRanking: [],
      // 图表实例
      charts: {
        vehicleType: null,
        team: null,
        trend: null
      }
    };
  },
  created() {
    this.initDefaultDates();
    this.getOverallStatistics();
  },
  mounted() {
    this.initCharts();
    this.loadAllData();
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  methods: {
    /** 初始化默认日期 */
    initDefaultDates() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前
      
      this.queryParams.startDate = this.formatDate(start);
      this.queryParams.endDate = this.formatDate(end);
    },
    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    /** 初始化图表 */
    initCharts() {
      this.charts.vehicleType = echarts.init(document.getElementById('vehicleTypeChart'));
      this.charts.team = echarts.init(document.getElementById('teamChart'));
      this.charts.trend = echarts.init(document.getElementById('trendChart'));
    },
    /** 获取总体统计 */
    getOverallStatistics() {
      // TODO: 调用API获取总体统计数据
      // 模拟数据
      this.overallStats = {
        totalVehicles: 156,
        totalOrders: 1248,
        totalWorkHours: 3456,
        utilizationRate: 78.5
      };
    },
    /** 加载所有数据 */
    loadAllData() {
      this.loadVehicleTypeData();
      this.loadTeamData();
      this.loadTrendData();
      this.loadRankingData();
    },
    /** 加载车辆类型数据 */
    loadVehicleTypeData() {
      // TODO: 调用API获取车辆类型统计数据
      // 模拟数据
      const data = [
        { name: '挖掘机', value: 45 },
        { name: '推土机', value: 32 },
        { name: '装载机', value: 28 },
        { name: '起重机', value: 25 },
        { name: '运输车', value: 26 }
      ];
      
      const option = {
        title: {
          text: '车辆类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '车辆数量',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      
      this.charts.vehicleType.setOption(option);
    },
    /** 加载队伍数据 */
    loadTeamData() {
      // TODO: 调用API获取队伍统计数据
      // 模拟数据
      const option = {
        title: {
          text: '队伍用车统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: ['第一施工队', '第二施工队', '第三施工队', '机械维修队', '安全监督队']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '用车次数',
            type: 'bar',
            data: [120, 98, 87, 45, 23],
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      };
      
      this.charts.team.setOption(option);
    },
    /** 加载趋势数据 */
    loadTrendData() {
      // TODO: 调用API获取趋势统计数据
      // 模拟数据
      const option = {
        title: {
          text: '用车趋势统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['订单数量', '工作时长'],
          top: 30
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月']
        },
        yAxis: [
          {
            type: 'value',
            name: '订单数量'
          },
          {
            type: 'value',
            name: '工作时长'
          }
        ],
        series: [
          {
            name: '订单数量',
            type: 'line',
            data: [120, 132, 101, 134, 90, 230, 210, 156]
          },
          {
            name: '工作时长',
            type: 'bar',
            yAxisIndex: 1,
            data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2]
          }
        ]
      };
      
      this.charts.trend.setOption(option);
    },
    /** 加载排行榜数据 */
    loadRankingData() {
      // TODO: 调用API获取排行榜数据
      // 模拟数据
      this.vehicleRanking = [
        { rank: 1, vehicleModel: '卡特320D', usageCount: 45, workHours: 360 },
        { rank: 2, vehicleModel: '小松PC200', usageCount: 38, workHours: 304 },
        { rank: 3, vehicleModel: '三一SY215', usageCount: 32, workHours: 256 },
        { rank: 4, vehicleModel: '徐工XE215', usageCount: 28, workHours: 224 },
        { rank: 5, vehicleModel: '柳工CLG922', usageCount: 25, workHours: 200 }
      ];
      
      this.teamRanking = [
        { rank: 1, teamName: '第一施工队', orderCount: 120, workHours: 960 },
        { rank: 2, teamName: '第二施工队', orderCount: 98, workHours: 784 },
        { rank: 3, teamName: '第三施工队', orderCount: 87, workHours: 696 },
        { rank: 4, teamName: '机械维修队', orderCount: 45, workHours: 360 },
        { rank: 5, teamName: '安全监督队', orderCount: 23, workHours: 184 }
      ];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.loadAllData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initDefaultDates();
      this.handleQuery();
    },
    /** 刷新车辆类型图表 */
    refreshVehicleTypeChart() {
      this.loadVehicleTypeData();
    },
    /** 刷新队伍图表 */
    refreshTeamChart() {
      this.loadTeamData();
    },
    /** 改变时间范围 */
    changeTimeRange(range) {
      this.timeRange = range;
      this.loadTrendData();
    },
    /** 导出统计报表 */
    handleExport() {
      // TODO: 实现导出功能
      this.$modal.msgSuccess("导出功能开发中...");
    },
    /** 刷新所有数据 */
    handleRefreshAll() {
      this.getOverallStatistics();
      this.loadAllData();
      this.$modal.msgSuccess("数据刷新成功");
    }
  }
};
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
