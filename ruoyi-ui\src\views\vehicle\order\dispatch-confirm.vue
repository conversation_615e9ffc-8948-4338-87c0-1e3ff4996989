<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">项目调度室 - 台班确认</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待确认订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.confirmedCount }}</div>
              <div class="stat-label">已确认订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.over50TonCount }}</div>
              <div class="stat-label">超50吨待审</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.todayCount }}</div>
              <div class="stat-label">今日处理</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择状态" clearable @change="getList">
            <el-option label="队伍已确认" value="team_confirmed"></el-option>
            <el-option label="调度已确认" value="dispatch_confirmed"></el-option>
            <el-option label="待主管审批" value="pending_manager"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆重量" prop="vehicleWeight">
          <el-select v-model="queryParams.vehicleWeight" placeholder="请选择重量范围" clearable @change="getList">
            <el-option label="50吨以下" value="under_50"></el-option>
            <el-option label="50吨及以上" value="over_50"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用承担" prop="costBearer">
          <el-select v-model="queryParams.costBearer" placeholder="请选择费用承担方" clearable @change="getList">
            <el-option label="项目承担" value="project"></el-option>
            <el-option label="队伍承担" value="team"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="完成日期">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getList">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchConfirm"
            v-hasPermi="['vehicle:order:dispatch-confirm']"
          >批量确认</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-money"
            size="mini"
            @click="showCostCalculation"
          >费用计算</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['vehicle:order:export']"
          >导出数据</el-button>
        </el-col>
      </el-row>

      <!-- 订单列表 -->
      <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单ID" align="center" prop="orderId" width="80" />
        <el-table-column label="车辆信息" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">
              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
            </div>
            <div style="color: #E6A23C; font-size: 12px;" v-if="scope.row.vehicleWeight >= 50">
              重量：{{ scope.row.vehicleWeight }}吨
            </div>
          </template>
        </el-table-column>
        <el-table-column label="队伍信息" align="center" width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.driverName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
        <el-table-column label="实际用车时间" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>
            <div style="color: #909399; font-size: 12px;">
              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用车时长" align="center" width="100">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold;">
              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="费用信息" align="center" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.totalCost">
              <div style="color: #67C23A; font-weight: bold;">￥{{ scope.row.totalCost }}</div>
              <div style="color: #909399; font-size: 12px;">{{ getCostBearerText(scope.row.costBearer) }}</div>
            </div>
            <div v-else style="color: #E6A23C;">待计算</div>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="mini">
              {{ getStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看详情</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'team_confirmed'"
              size="mini"
              type="success"
              @click="handleConfirm(scope.row)"
              v-hasPermi="['vehicle:order:dispatch-confirm']"
            >确认</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'team_confirmed'"
              size="mini"
              type="primary"
              @click="handleCostCalculate(scope.row)"
              v-hasPermi="['vehicle:order:cost-calculate']"
            >计算费用</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailDialogVisible" width="900px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ detailOrder.orderId }}</el-descriptions-item>
        <el-descriptions-item label="车辆信息">
          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}
          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})
        </el-descriptions-item>
        <el-descriptions-item label="车辆重量">{{ detailOrder.vehicleWeight }}吨</el-descriptions-item>
        <el-descriptions-item label="司机">{{ detailOrder.driverName }}</el-descriptions-item>
        <el-descriptions-item label="队伍">
          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="用车地点" :span="2">{{ detailOrder.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="实际开始时间">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际结束时间">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="用车时长">
          <span style="color: #409EFF; font-weight: bold;">
            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="费用信息">
          <div v-if="detailOrder.totalCost">
            <div>总费用：<span style="color: #67C23A; font-weight: bold;">￥{{ detailOrder.totalCost }}</span></div>
            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>
            <div>计量单位：{{ detailOrder.costUnit }}</div>
            <div>单价：￥{{ detailOrder.unitPrice }}</div>
          </div>
          <div v-else style="color: #E6A23C;">费用待计算</div>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusTagType(detailOrder.orderStatus)">
            {{ getStatusText(detailOrder.orderStatus) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 作业照片 -->
      <div v-if="detailOrder.startPhotoUrl || detailOrder.endPhotoUrl" style="margin-top: 20px;">
        <h4>作业照片</h4>
        <el-row :gutter="20">
          <el-col :span="12" v-if="detailOrder.startPhotoUrl">
            <div class="photo-section">
              <h5>开始照片</h5>
              <el-image
                :src="detailOrder.startPhotoUrl"
                :preview-src-list="[detailOrder.startPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="12" v-if="detailOrder.endPhotoUrl">
            <div class="photo-section">
              <h5>结束照片</h5>
              <el-image
                :src="detailOrder.endPhotoUrl"
                :preview-src-list="[detailOrder.endPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 确认操作 -->
      <div v-if="detailOrder.orderStatus === 'team_confirmed'" style="margin-top: 20px;">
        <el-divider content-position="left">调度室确认</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button type="primary" size="medium" @click="handleCostCalculate(detailOrder)" style="width: 100%;">
              <i class="el-icon-money"></i> 计算费用
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="success" size="medium" @click="handleConfirm(detailOrder)" style="width: 100%;">
              <i class="el-icon-check"></i> 确认订单
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 费用计算对话框 -->
    <el-dialog title="费用计算" :visible.sync="costDialogVisible" width="600px" append-to-body>
      <el-form ref="costForm" :model="costForm" :rules="costRules" label-width="120px">
        <el-form-item label="车辆信息">
          <div class="cost-info">
            <div>车辆型号：{{ currentOrder.vehicleInfo ? currentOrder.vehicleInfo.vehicleModel : '未知' }}</div>
            <div>车辆重量：{{ currentOrder.vehicleWeight }}吨</div>
            <div>用车时长：{{ calculateDuration(currentOrder.actualStartTime, currentOrder.actualEndTime) }}</div>
          </div>
        </el-form-item>
        
        <el-form-item label="计量单位" prop="costUnit">
          <el-radio-group v-model="costForm.costUnit">
            <el-radio label="hour">小时</el-radio>
            <el-radio label="day">天</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number
            v-model="costForm.unitPrice"
            :precision="2"
            :min="0"
            :max="99999"
            placeholder="请输入单价"
            style="width: 200px;">
          </el-input-number>
          <span style="margin-left: 10px;">元/{{ costForm.costUnit === 'hour' ? '小时' : '天' }}</span>
        </el-form-item>
        
        <el-form-item label="费用承担方" prop="costBearer">
          <el-radio-group v-model="costForm.costBearer">
            <el-radio label="project">项目承担</el-radio>
            <el-radio label="team">队伍承担</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="预计总费用">
          <div class="cost-preview">
            <span style="font-size: 18px; color: #67C23A; font-weight: bold;">
              ￥{{ calculateTotalCost() }}
            </span>
          </div>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="costDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCostCalculation" :loading="costLoading">确认计算</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPendingConfirmOrders, getOrder, dispatchConfirmOrder, calculateOrderCost } from "@/api/vehicle/order";

export default {
  name: "DispatchConfirm",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 订单列表
      orderList: [],
      // 统计信息
      statistics: {
        pendingCount: 0,
        confirmedCount: 0,
        over50TonCount: 0,
        todayCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderStatus: 'team_confirmed'
      },
      // 日期范围
      dateRange: [],
      // 详情对话框
      detailDialogVisible: false,
      detailOrder: {},
      // 费用计算对话框
      costDialogVisible: false,
      costLoading: false,
      currentOrder: {},
      costForm: {
        costUnit: 'hour',
        unitPrice: 0,
        costBearer: 'project'
      },
      // 费用计算表单验证规则
      costRules: {
        unitPrice: [
          { required: true, message: "请输入单价", trigger: "blur" },
          { type: 'number', min: 0.01, message: "单价必须大于0", trigger: "blur" }
        ],
        costBearer: [
          { required: true, message: "请选择费用承担方", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.params["beginActualEndTime"] = this.dateRange[0];
        this.queryParams.params["endActualEndTime"] = this.dateRange[1];
      }
      
      getPendingConfirmOrders('dispatch').then(response => {
        this.orderList = response.data;
        this.total = response.data.length;
        this.loading = false;
      });
    },
    
    /** 加载统计信息 */
    loadStatistics() {
      // TODO: 调用统计接口
      this.statistics = {
        pendingCount: 8,
        confirmedCount: 15,
        over50TonCount: 3,
        todayCount: 5
      };
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新列表 */
    refreshList() {
      this.getList();
      this.loadStatistics();
      this.$modal.msgSuccess("刷新成功");
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.multiple = !selection.length
    },
    
    /** 查看详情 */
    handleView(row) {
      getOrder(row.orderId).then(response => {
        this.detailOrder = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 确认订单 */
    handleConfirm(row) {
      this.$modal.confirm('确认该订单的费用和用车情况？').then(() => {
        return dispatchConfirmOrder(row.orderId);
      }).then(() => {
        this.$modal.msgSuccess("确认成功");
        this.detailDialogVisible = false;
        this.getList();
      }).catch(() => {});
    },
    
    /** 费用计算 */
    handleCostCalculate(row) {
      this.currentOrder = row;
      this.costDialogVisible = true;
      this.costForm = {
        costUnit: 'hour',
        unitPrice: 0,
        costBearer: row.vehicleWeight >= 50 ? 'project' : 'team'
      };
    },
    
    /** 提交费用计算 */
    submitCostCalculation() {
      this.$refs["costForm"].validate(valid => {
        if (valid) {
          this.costLoading = true;
          const data = {
            orderId: this.currentOrder.orderId,
            costUnit: this.costForm.costUnit,
            unitPrice: this.costForm.unitPrice,
            costBearer: this.costForm.costBearer,
            totalCost: this.calculateTotalCost()
          };
          
          calculateOrderCost(data).then(response => {
            this.$modal.msgSuccess("费用计算成功");
            this.costDialogVisible = false;
            this.detailDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.costLoading = false;
          });
        }
      });
    },
    
    /** 计算总费用 */
    calculateTotalCost() {
      if (!this.currentOrder.actualStartTime || !this.currentOrder.actualEndTime || !this.costForm.unitPrice) {
        return 0;
      }
      
      const diff = new Date(this.currentOrder.actualEndTime).getTime() - new Date(this.currentOrder.actualStartTime).getTime();
      let duration = 0;
      
      if (this.costForm.costUnit === 'hour') {
        duration = Math.ceil(diff / (1000 * 60 * 60)); // 向上取整小时
      } else {
        duration = Math.ceil(diff / (1000 * 60 * 60 * 24)); // 向上取整天
      }
      
      return (duration * this.costForm.unitPrice).toFixed(2);
    },
    
    /** 批量确认 */
    handleBatchConfirm() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要确认的订单");
        return;
      }
      
      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {
        // TODO: 调用批量确认接口
        this.$modal.msgSuccess("批量确认成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 显示费用计算 */
    showCostCalculation() {
      this.$modal.msgInfo("费用计算功能开发中...");
    },
    
    /** 导出数据 */
    handleExport() {
      this.$modal.msgInfo("导出功能开发中...");
    },
    
    /** 计算用车时长 */
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '0小时';
      
      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      return `${hours}小时${minutes}分钟`;
    },
    
    /** 获取费用承担方文本 */
    getCostBearerText(costBearer) {
      const bearerMap = {
        'project': '项目承担',
        'team': '队伍承担'
      };
      return bearerMap[costBearer] || '未知';
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'team_confirmed': 'warning',
        'dispatch_confirmed': 'success',
        'pending_manager': 'primary'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'team_confirmed': '队伍已确认',
        'dispatch_confirmed': '调度已确认',
        'pending_manager': '待主管审批'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.mb20 {
  margin-bottom: 20px;
}

.photo-section {
  text-align: center;
}

.photo-section h5 {
  margin-bottom: 10px;
  color: #606266;
}

.cost-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.8;
}

.cost-preview {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
  text-align: center;
  border: 1px solid #b3d8ff;
}
</style>
