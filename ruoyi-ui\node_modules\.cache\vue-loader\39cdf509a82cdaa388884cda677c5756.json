{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\team-confirm.vue?vue&type=style&index=0&id=c5ccc864&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\team-confirm.vue", "mtime": 1754143099443}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZCB7CiAgbWFyZ2luOiAyMHB4Owp9Cgouc3RhdC1jYXJkIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzOwp9Cgouc3RhdC1jYXJkOmhvdmVyIHsKICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjEyKTsKfQoKLnN0YXQtaXRlbSB7CiAgcGFkZGluZzogMjBweDsKfQoKLnN0YXQtdmFsdWUgewogIGZvbnQtc2l6ZTogMjhweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzQwOUVGRjsKICBtYXJnaW4tYm90dG9tOiA4cHg7Cn0KCi5zdGF0LWxhYmVsIHsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2MDYyNjY7Cn0KCi5tYjIwIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9CgoucGhvdG8tc2VjdGlvbiB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9CgoucGhvdG8tc2VjdGlvbiBoNSB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBjb2xvcjogIzYwNjI2NjsKfQo="}, {"version": 3, "sources": ["team-confirm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2gBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "team-confirm.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">队伍确认 - 待确认订单</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.confirmedCount }}</div>\n              <div class=\"stat-label\">已确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.rejectedCount }}</div>\n              <div class=\"stat-label\">异议订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日处理</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"司机已结束\" value=\"driver_finished\"></el-option>\n            <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n            <el-option label=\"异议退回\" value=\"rejected\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n          <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable @change=\"getList\">\n            <el-option\n              v-for=\"dict in dict.type.vehicle_type\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchConfirm\"\n            v-hasPermi=\"['vehicle:order:team-confirm']\"\n          >批量确认</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-close\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchReject\"\n            v-hasPermi=\"['vehicle:order:reject']\"\n          >批量异议</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"司机\" align=\"center\" prop=\"driverName\" width=\"100\" />\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'driver_finished'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleConfirm(scope.row)\"\n              v-hasPermi=\"['vehicle:order:team-confirm']\"\n            >确认</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'driver_finished'\"\n              size=\"mini\"\n              type=\"warning\"\n              @click=\"handleReject(scope.row)\"\n              v-hasPermi=\"['vehicle:order:reject']\"\n            >异议</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划开始时间\">{{ parseTime(detailOrder.plannedStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划结束时间\">{{ parseTime(detailOrder.plannedEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 确认操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'driver_finished'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">确认操作</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleConfirm(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 确认订单\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"warning\" size=\"medium\" @click=\"handleReject(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-close\"></i> 提出异议\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 异议对话框 -->\n    <el-dialog title=\"提出异议\" :visible.sync=\"rejectDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\n        <el-form-item label=\"异议原因\" prop=\"rejectReason\">\n          <el-select v-model=\"rejectForm.rejectReason\" placeholder=\"请选择异议原因\">\n            <el-option label=\"用车时间不符\" value=\"time_mismatch\"></el-option>\n            <el-option label=\"用车地点不符\" value=\"location_mismatch\"></el-option>\n            <el-option label=\"作业内容不符\" value=\"work_mismatch\"></el-option>\n            <el-option label=\"照片不清晰\" value=\"photo_unclear\"></el-option>\n            <el-option label=\"其他原因\" value=\"other\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"详细说明\" prop=\"rejectDescription\">\n          <el-input\n            v-model=\"rejectForm.rejectDescription\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请详细说明异议原因\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectDialogVisible = false\">取 消</el-button>\n        <el-button type=\"warning\" @click=\"submitReject\" :loading=\"rejectLoading\">提交异议</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, teamConfirmOrder, rejectOrder } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"TeamConfirm\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        confirmedCount: 0,\n        rejectedCount: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'driver_finished',\n        teamId: this.$store.state.user.teamId // 当前用户所属队伍\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 异议对话框\n      rejectDialogVisible: false,\n      rejectLoading: false,\n      currentOrder: {},\n      rejectForm: {\n        rejectReason: '',\n        rejectDescription: ''\n      },\n      // 异议表单验证规则\n      rejectRules: {\n        rejectReason: [\n          { required: true, message: \"请选择异议原因\", trigger: \"change\" }\n        ],\n        rejectDescription: [\n          { required: true, message: \"请详细说明异议原因\", trigger: \"blur\" },\n          { min: 10, max: 500, message: \"长度在 10 到 500 个字符\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('team').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 5,\n        confirmedCount: 12,\n        rejectedCount: 2,\n        todayCount: 3\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 确认订单 */\n    handleConfirm(row) {\n      this.$modal.confirm('确认该订单的用车情况属实？').then(() => {\n        return teamConfirmOrder(row.orderId);\n      }).then(() => {\n        this.$modal.msgSuccess(\"确认成功\");\n        this.detailDialogVisible = false;\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 提出异议 */\n    handleReject(row) {\n      this.currentOrder = row;\n      this.rejectDialogVisible = true;\n      this.rejectForm = {\n        rejectReason: '',\n        rejectDescription: ''\n      };\n    },\n    \n    /** 提交异议 */\n    submitReject() {\n      this.$refs[\"rejectForm\"].validate(valid => {\n        if (valid) {\n          this.rejectLoading = true;\n          const data = {\n            rejectReason: `${this.getRejectReasonText(this.rejectForm.rejectReason)}: ${this.rejectForm.rejectDescription}`\n          };\n          \n          rejectOrder(this.currentOrder.orderId, data).then(response => {\n            this.$modal.msgSuccess(\"异议提交成功\");\n            this.rejectDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.rejectLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 批量确认 */\n    handleBatchConfirm() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要确认的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量确认接口\n        this.$modal.msgSuccess(\"批量确认成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 批量异议 */\n    handleBatchReject() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要提出异议的订单\");\n        return;\n      }\n      this.$modal.msgInfo(\"批量异议功能开发中...\");\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取异议原因文本 */\n    getRejectReasonText(reason) {\n      const reasonMap = {\n        'time_mismatch': '用车时间不符',\n        'location_mismatch': '用车地点不符',\n        'work_mismatch': '作业内容不符',\n        'photo_unclear': '照片不清晰',\n        'other': '其他原因'\n      };\n      return reasonMap[reason] || '未知原因';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'driver_finished': 'warning',\n        'team_confirmed': 'success',\n        'rejected': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'driver_finished': '司机已结束',\n        'team_confirmed': '队伍已确认',\n        'rejected': '异议退回'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n</style>\n"]}]}