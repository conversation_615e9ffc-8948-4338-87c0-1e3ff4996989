package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.vehicle.mapper.VehicleDemandPlanMapper;
import com.ruoyi.vehicle.domain.VehicleDemandPlan;
import com.ruoyi.vehicle.service.IVehicleDemandPlanService;
import com.ruoyi.vehicle.service.IVehicleNotificationService;
import com.ruoyi.vehicle.service.IVehicleShiftApprovalService;

/**
 * 车辆需求计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleDemandPlanServiceImpl implements IVehicleDemandPlanService 
{
    @Autowired
    private VehicleDemandPlanMapper vehicleDemandPlanMapper;

    @Autowired
    private IVehicleNotificationService notificationService;

    @Autowired
    private IVehicleShiftApprovalService approvalService;

    /**
     * 查询车辆需求计划
     * 
     * @param planId 车辆需求计划主键
     * @return 车辆需求计划
     */
    @Override
    public VehicleDemandPlan selectVehicleDemandPlanByPlanId(Long planId)
    {
        return vehicleDemandPlanMapper.selectVehicleDemandPlanByPlanId(planId);
    }

    /**
     * 查询车辆需求计划列表
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 车辆需求计划
     */
    @Override
    public List<VehicleDemandPlan> selectVehicleDemandPlanList(VehicleDemandPlan vehicleDemandPlan)
    {
        return vehicleDemandPlanMapper.selectVehicleDemandPlanList(vehicleDemandPlan);
    }

    /**
     * 新增车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    @Override
    public int insertVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan)
    {
        vehicleDemandPlan.setCreateTime(DateUtils.getNowDate());
        vehicleDemandPlan.setApprovalStatus("draft"); // 默认草稿状态
        return vehicleDemandPlanMapper.insertVehicleDemandPlan(vehicleDemandPlan);
    }

    /**
     * 修改车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    @Override
    public int updateVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan)
    {
        vehicleDemandPlan.setUpdateTime(DateUtils.getNowDate());
        return vehicleDemandPlanMapper.updateVehicleDemandPlan(vehicleDemandPlan);
    }

    /**
     * 批量删除车辆需求计划
     * 
     * @param planIds 需要删除的车辆需求计划主键
     * @return 结果
     */
    @Override
    public int deleteVehicleDemandPlanByPlanIds(Long[] planIds)
    {
        return vehicleDemandPlanMapper.deleteVehicleDemandPlanByPlanIds(planIds);
    }

    /**
     * 删除车辆需求计划信息
     * 
     * @param planId 车辆需求计划主键
     * @return 结果
     */
    @Override
    public int deleteVehicleDemandPlanByPlanId(Long planId)
    {
        return vehicleDemandPlanMapper.deleteVehicleDemandPlanByPlanId(planId);
    }

    /**
     * 提交需求计划
     * 
     * @param vehicleDemandPlan 需求计划信息
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int submitDemandPlan(VehicleDemandPlan vehicleDemandPlan, String operName)
    {
        vehicleDemandPlan.setCreateBy(operName);
        vehicleDemandPlan.setApprovalStatus("pending_level1"); // 待项目调度室审批
        
        int result = insertVehicleDemandPlan(vehicleDemandPlan);
        
        if (result > 0) {
            // 创建第一级审批记录
            approvalService.createApprovalRecord("demand_plan", vehicleDemandPlan.getPlanId(), 1, "项目调度室", operName);
            
            // 发送提交通知
            notificationService.sendDemandPlanNotification(vehicleDemandPlan.getPlanId(), "submit", operName);
        }
        
        return result;
    }

    /**
     * 审批需求计划
     * 
     * @param planId 计划ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int approveDemandPlan(Long planId, String approvalStatus, String approvalComments, String operName)
    {
        VehicleDemandPlan plan = selectVehicleDemandPlanByPlanId(planId);
        if (plan == null) {
            throw new ServiceException("需求计划不存在");
        }
        
        // 根据当前状态确定下一步流程
        String nextStatus = getNextApprovalStatus(plan.getApprovalStatus(), approvalStatus);
        
        plan.setApprovalStatus(nextStatus);
        plan.setApprovalComments(approvalComments);
        plan.setUpdateBy(operName);
        plan.setUpdateTime(DateUtils.getNowDate());
        
        int result = updateVehicleDemandPlan(plan);
        
        if (result > 0) {
            // 处理审批流程
            handleApprovalFlow(planId, plan.getApprovalStatus(), approvalStatus, operName);
            
            // 发送审批通知
            String notifyType = "approved".equals(approvalStatus) ? "approve" : "reject";
            notificationService.sendDemandPlanNotification(planId, notifyType, operName);
        }
        
        return result;
    }

    /**
     * 根据审批状态查询计划列表
     * 
     * @param approvalStatus 审批状态
     * @return 计划集合
     */
    @Override
    public List<VehicleDemandPlan> selectVehicleDemandPlanByStatus(String approvalStatus)
    {
        return vehicleDemandPlanMapper.selectVehicleDemandPlanByStatus(approvalStatus);
    }

    /**
     * 根据队伍ID查询计划列表
     * 
     * @param teamId 队伍ID
     * @return 计划集合
     */
    @Override
    public List<VehicleDemandPlan> selectVehicleDemandPlanByTeamId(Long teamId)
    {
        return vehicleDemandPlanMapper.selectVehicleDemandPlanByTeamId(teamId);
    }

    /**
     * 根据申请人查询计划列表
     * 
     * @param applicant 申请人
     * @return 计划集合
     */
    @Override
    public List<VehicleDemandPlan> selectVehicleDemandPlanByApplicant(String applicant)
    {
        VehicleDemandPlan queryPlan = new VehicleDemandPlan();
        queryPlan.setApplicant(applicant);
        return selectVehicleDemandPlanList(queryPlan);
    }

    /**
     * 查询待审批的计划列表
     * 
     * @return 计划集合
     */
    @Override
    public List<VehicleDemandPlan> selectPendingApprovalPlans()
    {
        VehicleDemandPlan queryPlan = new VehicleDemandPlan();
        // 查询所有待审批状态的计划
        List<VehicleDemandPlan> allPlans = selectVehicleDemandPlanList(queryPlan);
        return allPlans.stream()
            .filter(plan -> plan.getApprovalStatus() != null && 
                           plan.getApprovalStatus().startsWith("pending_"))
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 批量审批需求计划
     * 
     * @param planIds 计划ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int batchApproveDemandPlans(Long[] planIds, String approvalStatus, String approvalComments, String operName)
    {
        int successCount = 0;
        for (Long planId : planIds) {
            try {
                int result = approveDemandPlan(planId, approvalStatus, approvalComments, operName);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他计划
                continue;
            }
        }
        return successCount;
    }

    /**
     * 获取下一个审批状态
     */
    private String getNextApprovalStatus(String currentStatus, String approvalResult) {
        if ("rejected".equals(approvalResult)) {
            return "rejected";
        }
        
        switch (currentStatus) {
            case "pending_level1": // 项目调度室审批
                return "pending_level2"; // 机械主管审批
            case "pending_level2": // 机械主管审批
                return "pending_level3"; // 经营部门审批
            case "pending_level3": // 经营部门审批
                return "approved"; // 审批完成
            default:
                return currentStatus;
        }
    }

    /**
     * 处理审批流程
     */
    private void handleApprovalFlow(Long planId, String currentStatus, String approvalResult, String operName) {
        if ("approved".equals(approvalResult) && currentStatus.startsWith("pending_")) {
            // 创建下一级审批记录
            int currentLevel = getCurrentApprovalLevel(currentStatus);
            if (currentLevel < 3) {
                String nextApprover = getNextApprover(currentLevel + 1);
                approvalService.createApprovalRecord("demand_plan", planId, currentLevel + 1, nextApprover, operName);
            }
        }
    }

    /**
     * 获取当前审批级别
     */
    private int getCurrentApprovalLevel(String status) {
        switch (status) {
            case "pending_level1":
                return 1;
            case "pending_level2":
                return 2;
            case "pending_level3":
                return 3;
            default:
                return 0;
        }
    }

    /**
     * 获取下一级审批人
     */
    private String getNextApprover(int level) {
        switch (level) {
            case 1:
                return "项目调度室";
            case 2:
                return "机械主管";
            case 3:
                return "经营部门";
            default:
                return "未知";
        }
    }
}
