{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\dispatch.vue?vue&type=style&index=0&id=dcf82714&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\dispatch.vue", "mtime": 1754142892050}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbjogMjBweDsKfQoKLnN0YXQtY2FyZCB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKfQoKLnN0YXQtY2FyZDpob3ZlciB7CiAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwwLDAsMC4xMik7Cn0KCi5zdGF0LWl0ZW0gewogIHBhZGRpbmc6IDIwcHg7Cn0KCi5zdGF0LXZhbHVlIHsKICBmb250LXNpemU6IDI4cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICM0MDlFRkY7CiAgbWFyZ2luLWJvdHRvbTogOHB4Owp9Cgouc3RhdC1sYWJlbCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjNjA2MjY2Owp9CgoubWIyMCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQo="}, {"version": 3, "sources": ["dispatch.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAggBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "dispatch.vue", "sourceRoot": "src/views/vehicle/application", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">车辆调度安排</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待调度申请</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.availableCount }}</div>\n              <div class=\"stat-label\">可用车辆</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.busyCount }}</div>\n              <div class=\"stat-label\">使用中车辆</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayDispatchCount }}</div>\n              <div class=\"stat-label\">今日调度</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n          <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable @change=\"getList\">\n            <el-option\n              v-for=\"dict in dict.type.vehicle_type\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"申请状态\" prop=\"approvalStatus\">\n          <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"待审批\" value=\"pending\"></el-option>\n            <el-option label=\"已审批\" value=\"approved\"></el-option>\n            <el-option label=\"待调度\" value=\"approved\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"紧急程度\" prop=\"urgency\">\n          <el-select v-model=\"queryParams.urgency\" placeholder=\"请选择紧急程度\" clearable @change=\"getList\">\n            <el-option label=\"紧急\" value=\"urgent\"></el-option>\n            <el-option label=\"普通\" value=\"normal\"></el-option>\n            <el-option label=\"不急\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"primary\"\n            plain\n            icon=\"el-icon-s-operation\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchDispatch\"\n            v-hasPermi=\"['vehicle:application:dispatch']\"\n          >批量调度</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchApprove\"\n            v-hasPermi=\"['vehicle:application:approve']\"\n          >批量审批</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-view\"\n            size=\"mini\"\n            @click=\"showVehicleStatus\"\n          >车辆状态</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 申请列表 -->\n      <el-table v-loading=\"loading\" :data=\"applicationList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"申请ID\" align=\"center\" prop=\"applicationId\" width=\"80\" />\n        <el-table-column label=\"申请标题\" align=\"center\" prop=\"applicationTitle\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"车辆需求\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleType }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.vehicleModel }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.startTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.endTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"申请人\" align=\"center\" prop=\"applicant\" width=\"100\" />\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.approvalStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"分配情况\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.assignedVehicleId\">\n              <div style=\"color: #67C23A;\">已分配车辆</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.assignedDriver }}</div>\n            </div>\n            <div v-else style=\"color: #E6A23C;\">待分配</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看</el-button>\n            <el-button\n              v-if=\"scope.row.approvalStatus === 'approved' && !scope.row.assignedVehicleId\"\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-s-operation\"\n              @click=\"handleDispatch(scope.row)\"\n              v-hasPermi=\"['vehicle:application:dispatch']\"\n            >调度</el-button>\n            <el-button\n              v-if=\"scope.row.approvalStatus === 'pending'\"\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-check\"\n              @click=\"handleApprove(scope.row)\"\n              v-hasPermi=\"['vehicle:application:approve']\"\n            >审批</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 调度对话框 -->\n    <el-dialog title=\"车辆调度\" :visible.sync=\"dispatchDialogVisible\" width=\"800px\" append-to-body>\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\">\n        <el-form-item label=\"申请信息\">\n          <el-descriptions :column=\"2\" size=\"small\">\n            <el-descriptions-item label=\"申请标题\">{{ currentApplication.applicationTitle }}</el-descriptions-item>\n            <el-descriptions-item label=\"车辆需求\">{{ currentApplication.vehicleType }} - {{ currentApplication.vehicleModel }}</el-descriptions-item>\n            <el-descriptions-item label=\"用车时间\">{{ parseTime(currentApplication.startTime) }} 至 {{ parseTime(currentApplication.endTime) }}</el-descriptions-item>\n            <el-descriptions-item label=\"用车地点\">{{ currentApplication.usageLocation }}</el-descriptions-item>\n          </el-descriptions>\n        </el-form-item>\n        \n        <el-form-item label=\"分配车辆\" prop=\"assignedVehicleId\">\n          <el-select v-model=\"dispatchForm.assignedVehicleId\" placeholder=\"请选择车辆\" @change=\"handleVehicleChange\">\n            <el-option\n              v-for=\"vehicle in availableVehicles\"\n              :key=\"vehicle.vehicleId\"\n              :label=\"`${vehicle.vehicleModel} (${vehicle.licensePlate})`\"\n              :value=\"vehicle.vehicleId\">\n              <span style=\"float: left\">{{ vehicle.vehicleModel }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ vehicle.licensePlate }}</span>\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"分配司机\" prop=\"assignedDriver\">\n          <el-select v-model=\"dispatchForm.assignedDriver\" placeholder=\"请选择司机\">\n            <el-option\n              v-for=\"driver in availableDrivers\"\n              :key=\"driver.driverName\"\n              :label=\"`${driver.driverName} (${driver.driverPhone})`\"\n              :value=\"driver.driverName\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"调度备注\" prop=\"dispatchRemark\">\n          <el-input\n            v-model=\"dispatchForm.dispatchRemark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入调度备注\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitDispatch\" :loading=\"dispatchLoading\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 车辆状态对话框 -->\n    <el-dialog title=\"车辆状态总览\" :visible.sync=\"statusDialogVisible\" width=\"1000px\">\n      <el-table :data=\"vehicleStatusList\" v-loading=\"statusLoading\">\n        <el-table-column label=\"车辆类型\" prop=\"vehicleType\" />\n        <el-table-column label=\"车辆型号\" prop=\"vehicleModel\" />\n        <el-table-column label=\"车牌号\" prop=\"licensePlate\" />\n        <el-table-column label=\"司机\" prop=\"driverName\" />\n        <el-table-column label=\"状态\" prop=\"vehicleStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getVehicleStatusType(scope.row.vehicleStatus)\">\n              {{ getVehicleStatusText(scope.row.vehicleStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"当前位置\" prop=\"currentLocation\" />\n        <el-table-column label=\"下次可用时间\" prop=\"nextAvailableTime\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.nextAvailableTime\">{{ parseTime(scope.row.nextAvailableTime) }}</span>\n            <span v-else style=\"color: #67C23A;\">立即可用</span>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listApplication, assignVehicle, batchAssignVehicle, getAvailableVehicles } from \"@/api/vehicle/application\";\nimport { listInfo } from \"@/api/vehicle/info\";\n\nexport default {\n  name: \"ApplicationDispatch\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 申请列表\n      applicationList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        availableCount: 0,\n        busyCount: 0,\n        todayDispatchCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleType: null,\n        approvalStatus: 'approved'\n      },\n      // 调度对话框\n      dispatchDialogVisible: false,\n      dispatchLoading: false,\n      currentApplication: {},\n      dispatchForm: {\n        assignedVehicleId: null,\n        assignedDriver: '',\n        dispatchRemark: ''\n      },\n      availableVehicles: [],\n      availableDrivers: [],\n      // 车辆状态对话框\n      statusDialogVisible: false,\n      statusLoading: false,\n      vehicleStatusList: [],\n      // 调度表单验证规则\n      dispatchRules: {\n        assignedVehicleId: [\n          { required: true, message: \"请选择分配车辆\", trigger: \"change\" }\n        ],\n        assignedDriver: [\n          { required: true, message: \"请选择分配司机\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询申请列表 */\n    getList() {\n      this.loading = true;\n      listApplication(this.queryParams).then(response => {\n        this.applicationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 12,\n        availableCount: 8,\n        busyCount: 15,\n        todayDispatchCount: 6\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.applicationId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看申请详情 */\n    handleView(row) {\n      this.$router.push('/vehicle/application/detail/' + row.applicationId);\n    },\n    \n    /** 调度按钮操作 */\n    async handleDispatch(row) {\n      this.currentApplication = row;\n      this.dispatchDialogVisible = true;\n      \n      // 加载可用车辆和司机\n      try {\n        const vehicleRes = await getAvailableVehicles({\n          vehicleType: row.vehicleType,\n          vehicleModel: row.vehicleModel,\n          startTime: row.startTime,\n          endTime: row.endTime\n        });\n        this.availableVehicles = vehicleRes.data;\n        \n        // 加载司机列表\n        const driverRes = await listInfo({ vehicleType: row.vehicleType });\n        this.availableDrivers = driverRes.rows.filter(v => v.driverName).map(v => ({\n          driverName: v.driverName,\n          driverPhone: v.driverPhone\n        }));\n      } catch (error) {\n        this.$modal.msgError(\"加载可用资源失败\");\n      }\n    },\n    \n    /** 车辆变化处理 */\n    handleVehicleChange(vehicleId) {\n      const selectedVehicle = this.availableVehicles.find(v => v.vehicleId === vehicleId);\n      if (selectedVehicle && selectedVehicle.driverName) {\n        this.dispatchForm.assignedDriver = selectedVehicle.driverName;\n      }\n    },\n    \n    /** 提交调度 */\n    submitDispatch() {\n      this.$refs[\"dispatchForm\"].validate(valid => {\n        if (valid) {\n          this.dispatchLoading = true;\n          const data = {\n            ...this.dispatchForm,\n            applicationId: this.currentApplication.applicationId\n          };\n          \n          assignVehicle(data).then(response => {\n            this.$modal.msgSuccess(\"调度成功\");\n            this.dispatchDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.dispatchLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 批量调度 */\n    handleBatchDispatch() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要调度的申请\");\n        return;\n      }\n      this.$modal.msgInfo(\"批量调度功能开发中...\");\n    },\n    \n    /** 批量审批 */\n    handleBatchApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审批的申请\");\n        return;\n      }\n      this.$modal.msgInfo(\"批量审批功能开发中...\");\n    },\n    \n    /** 显示车辆状态 */\n    async showVehicleStatus() {\n      this.statusDialogVisible = true;\n      this.statusLoading = true;\n      \n      try {\n        const response = await listInfo();\n        this.vehicleStatusList = response.rows;\n      } catch (error) {\n        this.$modal.msgError(\"获取车辆状态失败\");\n      } finally {\n        this.statusLoading = false;\n      }\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending': 'warning',\n        'approved': 'success',\n        'rejected': 'danger',\n        'dispatched': 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待审批',\n        'approved': '已审批',\n        'rejected': '已拒绝',\n        'dispatched': '已调度'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 获取车辆状态类型 */\n    getVehicleStatusType(status) {\n      const statusMap = {\n        'available': 'success',\n        'busy': 'warning',\n        'maintenance': 'info',\n        'fault': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取车辆状态文本 */\n    getVehicleStatusText(status) {\n      const statusMap = {\n        'available': '可用',\n        'busy': '使用中',\n        'maintenance': '维护中',\n        'fault': '故障'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/application');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}