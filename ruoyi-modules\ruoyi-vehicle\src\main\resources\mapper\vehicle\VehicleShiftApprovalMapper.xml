<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleShiftApprovalMapper">
    
    <resultMap type="VehicleShiftApproval" id="VehicleShiftApprovalResult">
        <result property="approvalId"    column="approval_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="approvalType"    column="approval_type"    />
        <result property="approvalLevel"    column="approval_level"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approvalPerson"    column="approval_person"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="approvalComments"    column="approval_comments"    />
        <result property="nextApprovalLevel"    column="next_approval_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleShiftApprovalVo">
        select approval_id, order_id, approval_type, approval_level, approval_status, 
               approval_person, approval_time, approval_comments, next_approval_level, 
               create_by, create_time, update_by, update_time, remark 
        from vehicle_shift_approval
    </sql>

    <select id="selectVehicleShiftApprovalList" parameterType="VehicleShiftApproval" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="approvalType != null  and approvalType != ''"> and approval_type = #{approvalType}</if>
            <if test="approvalLevel != null "> and approval_level = #{approvalLevel}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="approvalPerson != null  and approvalPerson != ''"> and approval_person like concat('%', #{approvalPerson}, '%')</if>
            <if test="params.beginApprovalTime != null and params.beginApprovalTime != ''"><!-- 开始时间检索 -->
                and date_format(approval_time,'%y%m%d') &gt;= date_format(#{params.beginApprovalTime},'%y%m%d')
            </if>
            <if test="params.endApprovalTime != null and params.endApprovalTime != ''"><!-- 结束时间检索 -->
                and date_format(approval_time,'%y%m%d') &lt;= date_format(#{params.endApprovalTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleShiftApprovalByApprovalId" parameterType="Long" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        where approval_id = #{approvalId}
    </select>

    <select id="selectVehicleShiftApprovalByOrderId" parameterType="Long" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        where order_id = #{orderId}
        order by approval_level asc
    </select>

    <select id="selectPendingApprovals" parameterType="String" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        where approval_status = 'pending'
        <if test="approvalPerson != null and approvalPerson != ''">
            and approval_person = #{approvalPerson}
        </if>
        order by create_time desc
    </select>

    <select id="selectApprovalsByLevel" parameterType="Integer" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        where approval_level = #{approvalLevel}
        and approval_status = 'pending'
        order by create_time desc
    </select>

    <select id="selectApprovalHistory" parameterType="Long" resultMap="VehicleShiftApprovalResult">
        <include refid="selectVehicleShiftApprovalVo"/>
        where order_id = #{orderId}
        and approval_status in ('approved', 'rejected')
        order by approval_level asc, approval_time asc
    </select>
        
    <insert id="insertVehicleShiftApproval" parameterType="VehicleShiftApproval" useGeneratedKeys="true" keyProperty="approvalId">
        insert into vehicle_shift_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="approvalType != null and approvalType != ''">approval_type,</if>
            <if test="approvalLevel != null">approval_level,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="approvalPerson != null and approvalPerson != ''">approval_person,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="approvalComments != null">approval_comments,</if>
            <if test="nextApprovalLevel != null">next_approval_level,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="approvalType != null and approvalType != ''">#{approvalType},</if>
            <if test="approvalLevel != null">#{approvalLevel},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="approvalPerson != null and approvalPerson != ''">#{approvalPerson},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="approvalComments != null">#{approvalComments},</if>
            <if test="nextApprovalLevel != null">#{nextApprovalLevel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleShiftApproval" parameterType="VehicleShiftApproval">
        update vehicle_shift_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="approvalType != null and approvalType != ''">approval_type = #{approvalType},</if>
            <if test="approvalLevel != null">approval_level = #{approvalLevel},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="approvalPerson != null and approvalPerson != ''">approval_person = #{approvalPerson},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalComments != null">approval_comments = #{approvalComments},</if>
            <if test="nextApprovalLevel != null">next_approval_level = #{nextApprovalLevel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where approval_id = #{approvalId}
    </update>

    <update id="batchUpdateApprovalStatus">
        update vehicle_shift_approval
        set approval_status = #{approvalStatus},
            approval_person = #{approvalPerson},
            approval_time = #{approvalTime},
            approval_comments = #{approvalComments},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where approval_id in
        <foreach item="approvalId" collection="approvalIds" open="(" separator="," close=")">
            #{approvalId}
        </foreach>
    </update>

    <delete id="deleteVehicleShiftApprovalByApprovalId" parameterType="Long">
        delete from vehicle_shift_approval where approval_id = #{approvalId}
    </delete>

    <delete id="deleteVehicleShiftApprovalByApprovalIds" parameterType="String">
        delete from vehicle_shift_approval where approval_id in 
        <foreach item="approvalId" collection="array" open="(" separator="," close=")">
            #{approvalId}
        </foreach>
    </delete>
</mapper>
