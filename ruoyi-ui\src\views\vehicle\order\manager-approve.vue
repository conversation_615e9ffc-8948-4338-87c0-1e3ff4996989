<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">机械主管 - 最终审批</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待审批订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.approvedCount }}</div>
              <div class="stat-label">已审批订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCost }}</div>
              <div class="stat-label">总费用(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.todayCount }}</div>
              <div class="stat-label">今日审批</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="queryParams.orderStatus" placeholder="请选择状态" clearable @change="getList">
            <el-option label="待主管审批" value="pending_manager"></el-option>
            <el-option label="已完成" value="completed"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用范围" prop="costRange">
          <el-select v-model="queryParams.costRange" placeholder="请选择费用范围" clearable @change="getList">
            <el-option label="1000元以下" value="under_1000"></el-option>
            <el-option label="1000-5000元" value="1000_5000"></el-option>
            <el-option label="5000-10000元" value="5000_10000"></el-option>
            <el-option label="10000元以上" value="over_10000"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆重量" prop="vehicleWeight">
          <el-select v-model="queryParams.vehicleWeight" placeholder="请选择重量范围" clearable @change="getList">
            <el-option label="50吨及以上" value="over_50"></el-option>
            <el-option label="50吨以下" value="under_50"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="完成日期">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getList">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchApprove"
            v-hasPermi="['vehicle:order:manager-approve']"
          >批量审批</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-pie-chart"
            size="mini"
            @click="showCostAnalysis"
          >费用分析</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['vehicle:order:export']"
          >导出报表</el-button>
        </el-col>
      </el-row>

      <!-- 订单列表 -->
      <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单ID" align="center" prop="orderId" width="80" />
        <el-table-column label="车辆信息" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">
              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}
            </div>
            <div style="color: #E6A23C; font-size: 12px; font-weight: bold;">
              重量：{{ scope.row.vehicleWeight }}吨
            </div>
          </template>
        </el-table-column>
        <el-table-column label="队伍信息" align="center" width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.driverName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
        <el-table-column label="实际用车时间" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>
            <div style="color: #909399; font-size: 12px;">
              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用车时长" align="center" width="100">
          <template slot-scope="scope">
            <span style="color: #409EFF; font-weight: bold;">
              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="费用信息" align="center" width="150">
          <template slot-scope="scope">
            <div>
              <div style="color: #67C23A; font-weight: bold; font-size: 16px;">￥{{ scope.row.totalCost }}</div>
              <div style="color: #909399; font-size: 12px;">{{ getCostBearerText(scope.row.costBearer) }}</div>
              <div style="color: #909399; font-size: 12px;">{{ scope.row.costUnit === 'hour' ? '按小时' : '按天' }}计费</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="orderStatus" width="120">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="mini">
              {{ getStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看详情</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'pending_manager'"
              size="mini"
              type="success"
              @click="handleApprove(scope.row)"
              v-hasPermi="['vehicle:order:manager-approve']"
            >审批通过</el-button>
            <el-button
              v-if="scope.row.orderStatus === 'pending_manager'"
              size="mini"
              type="warning"
              @click="handleReject(scope.row)"
              v-hasPermi="['vehicle:order:manager-reject']"
            >退回</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailDialogVisible" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ detailOrder.orderId }}</el-descriptions-item>
        <el-descriptions-item label="车辆信息">
          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}
          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})
        </el-descriptions-item>
        <el-descriptions-item label="车辆重量">
          <span style="color: #E6A23C; font-weight: bold;">{{ detailOrder.vehicleWeight }}吨</span>
        </el-descriptions-item>
        <el-descriptions-item label="司机">{{ detailOrder.driverName }}</el-descriptions-item>
        <el-descriptions-item label="队伍">
          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="用车地点" :span="2">{{ detailOrder.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="实际开始时间">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际结束时间">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="用车时长">
          <span style="color: #409EFF; font-weight: bold;">
            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="费用详情">
          <div>
            <div>总费用：<span style="color: #67C23A; font-weight: bold; font-size: 18px;">￥{{ detailOrder.totalCost }}</span></div>
            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>
            <div>计量单位：{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>
            <div>单价：￥{{ detailOrder.unitPrice }}/{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>
            <div>计算时间：{{ parseTime(detailOrder.costCalculateTime) }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusTagType(detailOrder.orderStatus)">
            {{ getStatusText(detailOrder.orderStatus) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 审批流程 -->
      <div style="margin-top: 20px;">
        <h4>审批流程</h4>
        <el-steps :active="getApprovalStep(detailOrder.orderStatus)" finish-status="success">
          <el-step title="司机完成" :description="parseTime(detailOrder.actualEndTime)"></el-step>
          <el-step title="队伍确认" :description="parseTime(detailOrder.teamConfirmTime)"></el-step>
          <el-step title="调度确认" :description="parseTime(detailOrder.dispatchConfirmTime)"></el-step>
          <el-step title="主管审批" :description="parseTime(detailOrder.managerConfirmTime)"></el-step>
        </el-steps>
      </div>
      
      <!-- 作业照片 -->
      <div v-if="detailOrder.startPhotoUrl || detailOrder.endPhotoUrl" style="margin-top: 20px;">
        <h4>作业照片</h4>
        <el-row :gutter="20">
          <el-col :span="12" v-if="detailOrder.startPhotoUrl">
            <div class="photo-section">
              <h5>开始照片</h5>
              <el-image
                :src="detailOrder.startPhotoUrl"
                :preview-src-list="[detailOrder.startPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="12" v-if="detailOrder.endPhotoUrl">
            <div class="photo-section">
              <h5>结束照片</h5>
              <el-image
                :src="detailOrder.endPhotoUrl"
                :preview-src-list="[detailOrder.endPhotoUrl]"
                style="width: 100%; height: 200px;"
                fit="cover">
              </el-image>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 审批操作 -->
      <div v-if="detailOrder.orderStatus === 'pending_manager'" style="margin-top: 20px;">
        <el-divider content-position="left">主管审批</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-button type="success" size="medium" @click="handleApprove(detailOrder)" style="width: 100%;">
              <i class="el-icon-check"></i> 审批通过
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="warning" size="medium" @click="handleReject(detailOrder)" style="width: 100%;">
              <i class="el-icon-close"></i> 退回修改
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog title="审批确认" :visible.sync="approveDialogVisible" width="500px" append-to-body>
      <el-form ref="approveForm" :model="approveForm" :rules="approveRules" label-width="100px">
        <el-form-item label="审批结果" prop="approveResult">
          <el-radio-group v-model="approveForm.approveResult">
            <el-radio label="approve">审批通过</el-radio>
            <el-radio label="reject">退回修改</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="审批意见" prop="approveComment">
          <el-input
            v-model="approveForm.approveComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
            maxlength="500"
            show-word-limit />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="approveDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApproval" :loading="approveLoading">确认审批</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPendingConfirmOrders, getOrder, managerApproveOrder } from "@/api/vehicle/order";

export default {
  name: "ManagerApprove",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 订单列表
      orderList: [],
      // 统计信息
      statistics: {
        pendingCount: 0,
        approvedCount: 0,
        totalCost: 0,
        todayCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderStatus: 'pending_manager'
      },
      // 日期范围
      dateRange: [],
      // 详情对话框
      detailDialogVisible: false,
      detailOrder: {},
      // 审批对话框
      approveDialogVisible: false,
      approveLoading: false,
      currentOrder: {},
      approveForm: {
        approveResult: 'approve',
        approveComment: ''
      },
      // 审批表单验证规则
      approveRules: {
        approveComment: [
          { required: true, message: "请输入审批意见", trigger: "blur" },
          { min: 5, max: 500, message: "长度在 5 到 500 个字符", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.params["beginActualEndTime"] = this.dateRange[0];
        this.queryParams.params["endActualEndTime"] = this.dateRange[1];
      }
      
      getPendingConfirmOrders('manager').then(response => {
        this.orderList = response.data;
        this.total = response.data.length;
        this.loading = false;
      });
    },
    
    /** 加载统计信息 */
    loadStatistics() {
      // TODO: 调用统计接口
      this.statistics = {
        pendingCount: 3,
        approvedCount: 25,
        totalCost: 15.6,
        todayCount: 2
      };
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新列表 */
    refreshList() {
      this.getList();
      this.loadStatistics();
      this.$modal.msgSuccess("刷新成功");
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.multiple = !selection.length
    },
    
    /** 查看详情 */
    handleView(row) {
      getOrder(row.orderId).then(response => {
        this.detailOrder = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 审批通过 */
    handleApprove(row) {
      this.currentOrder = row;
      this.approveDialogVisible = true;
      this.approveForm = {
        approveResult: 'approve',
        approveComment: ''
      };
    },
    
    /** 退回修改 */
    handleReject(row) {
      this.currentOrder = row;
      this.approveDialogVisible = true;
      this.approveForm = {
        approveResult: 'reject',
        approveComment: ''
      };
    },
    
    /** 提交审批 */
    submitApproval() {
      this.$refs["approveForm"].validate(valid => {
        if (valid) {
          this.approveLoading = true;
          const data = {
            orderId: this.currentOrder.orderId,
            approveResult: this.approveForm.approveResult,
            approveComment: this.approveForm.approveComment
          };
          
          managerApproveOrder(data).then(response => {
            this.$modal.msgSuccess("审批成功");
            this.approveDialogVisible = false;
            this.detailDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.approveLoading = false;
          });
        }
      });
    },
    
    /** 批量审批 */
    handleBatchApprove() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审批的订单");
        return;
      }
      
      this.$modal.confirm(`确认批量审批选中的 ${this.ids.length} 个订单？`).then(() => {
        // TODO: 调用批量审批接口
        this.$modal.msgSuccess("批量审批成功");
        this.getList();
      }).catch(() => {});
    },
    
    /** 显示费用分析 */
    showCostAnalysis() {
      this.$modal.msgInfo("费用分析功能开发中...");
    },
    
    /** 导出报表 */
    handleExport() {
      this.$modal.msgInfo("导出功能开发中...");
    },
    
    /** 获取审批步骤 */
    getApprovalStep(status) {
      const stepMap = {
        'driver_finished': 1,
        'team_confirmed': 2,
        'dispatch_confirmed': 3,
        'pending_manager': 3,
        'completed': 4
      };
      return stepMap[status] || 0;
    },
    
    /** 计算用车时长 */
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '0小时';
      
      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      return `${hours}小时${minutes}分钟`;
    },
    
    /** 获取费用承担方文本 */
    getCostBearerText(costBearer) {
      const bearerMap = {
        'project': '项目承担',
        'team': '队伍承担'
      };
      return bearerMap[costBearer] || '未知';
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'pending_manager': 'warning',
        'completed': 'success'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'pending_manager': '待主管审批',
        'completed': '已完成'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.mb20 {
  margin-bottom: 20px;
}

.photo-section {
  text-align: center;
}

.photo-section h5 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
