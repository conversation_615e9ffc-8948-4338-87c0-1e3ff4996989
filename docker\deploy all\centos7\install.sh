#!/bin/bash

# =============================================================================
# RuoYi-Cloud 一键部署主脚本
# 统一管理中间件和应用的部署流程
# =============================================================================

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载配置文件
source "${SCRIPT_DIR}/config.sh"

# 函数：显示帮助信息
show_help() {
    echo "RuoYi-Cloud 一键部署主脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -c, --check             仅检查系统环境"
    echo "  -m, --middleware-only   仅部署中间件"
    echo "  -a, --application-only  仅部署应用"
    echo "  -f, --full              完整部署（中间件+应用）"
    echo "  --force                 强制重新部署"
    echo "  --skip-portainer        跳过Portainer安装"
    echo "  --skip-firewall         跳过防火墙配置"
    echo ""
    echo "示例:"
    echo "  $0 -f                   完整部署"
    echo "  $0 -m                   仅部署中间件"
    echo "  $0 -a                   仅部署应用"
    echo "  $0 -c                   检查系统环境"
    echo ""
    echo "分步部署建议:"
    echo "  1. $0 -c                检查环境"
    echo "  2. $0 -m                部署中间件（MySQL+达梦+Redis+Nacos）"
    echo "  3. $0 -a                部署应用（可选择数据库类型）"
    echo ""
}

# 函数：显示系统信息
show_system_info() {
    echo "========================================"
    echo "           系统信息"
    echo "========================================"
    echo "操作系统: $(cat /etc/centos-release 2>/dev/null || echo 'Unknown')"
    echo "内核版本: $(uname -r)"
    echo "主机名: $(hostname)"
    echo "IP地址: $(hostname -I | awk '{print $1}')"
    echo "CPU核心: $(nproc)"
    echo "内存大小: $(free -h | awk 'NR==2{print $2}')"
    echo "磁盘空间: $(df -h / | awk 'NR==2{print $4}') 可用"
    echo "当前用户: $(whoami)"
    echo "当前时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "========================================"
    echo ""
}

# 函数：检查脚本权限
check_script_permissions() {
    log_info "检查脚本权限..."
    
    local scripts=(
        "config.sh"
        "install-middleware.sh"
        "install-application.sh"
    )
    
    for script in "${scripts[@]}"; do
        local script_path="${SCRIPT_DIR}/${script}"
        if [[ -f "$script_path" ]]; then
            chmod +x "$script_path"
            log_info "设置执行权限: $script"
        else
            log_error "脚本文件不存在: $script"
            exit 1
        fi
    done
}

# 函数：检查Docker Compose文件
check_compose_files() {
    log_info "检查Docker Compose文件..."
    
    local compose_files=(
        "docker-compose-middleware.yml"
        "docker-compose-application.yml"
    )
    
    for compose_file in "${compose_files[@]}"; do
        local file_path="${SCRIPT_DIR}/${compose_file}"
        if [[ ! -f "$file_path" ]]; then
            log_error "Docker Compose文件不存在: $compose_file"
            exit 1
        else
            log_info "Docker Compose文件检查通过: $compose_file"
        fi
    done
}

# 函数：显示部署进度
show_progress() {
    local step=$1
    local total=$2
    local description=$3
    
    echo ""
    echo "========================================"
    echo "  步骤 $step/$total: $description"
    echo "========================================"
    echo ""
}

# 函数：部署中间件
deploy_middleware() {
    show_progress 1 2 "部署中间件服务"
    
    log_info "开始部署中间件..."
    
    # 执行中间件部署脚本
    "${SCRIPT_DIR}/install-middleware.sh" "$@"
    
    if [[ $? -eq 0 ]]; then
        log_info "中间件部署成功"
        
        # 等待中间件服务完全启动
        log_info "等待中间件服务启动..."
        sleep 60
        
        # 检查中间件服务状态
        log_info "检查中间件服务状态..."
        cd "${SCRIPT_DIR}"
        docker-compose -f docker-compose-middleware.yml ps
        
        # 验证关键服务
        local services=("ruoyi-redis:6379" "ruoyi-nacos:8848")
        for service_info in "${services[@]}"; do
            local service_name=$(echo "$service_info" | cut -d':' -f1)
            local service_port=$(echo "$service_info" | cut -d':' -f2)

            if wait_for_service "$service_name" "$service_port" 120; then
                log_info "服务 $service_name 启动成功"
            else
                log_error "服务 $service_name 启动失败"
                return 1
            fi
        done

        # 检查数据库服务（至少一个运行）
        local mysql_running=$(docker ps | grep -c "ruoyi-mysql" || echo "0")
        local dameng_running=$(docker ps | grep -c "ruoyi-dameng" || echo "0")

        if [[ "$mysql_running" == "0" && "$dameng_running" == "0" ]]; then
            log_error "没有数据库服务运行"
            return 1
        else
            log_info "数据库服务检查通过"
            if [[ "$mysql_running" != "0" ]]; then
                log_info "  - MySQL 数据库运行中"
            fi
            if [[ "$dameng_running" != "0" ]]; then
                log_info "  - 达梦数据库运行中"
            fi
        fi
        
        log_info "所有中间件服务启动成功"
    else
        log_error "中间件部署失败"
        return 1
    fi
}

# 函数：部署应用
deploy_application() {
    show_progress 2 2 "部署应用服务"
    
    log_info "开始部署应用..."
    
    # 执行应用部署脚本
    "${SCRIPT_DIR}/install-application.sh" "$@"
    
    if [[ $? -eq 0 ]]; then
        log_info "应用部署成功"
        
        # 等待应用服务启动
        log_info "等待应用服务启动..."
        sleep 120
        
        # 检查应用服务状态
        log_info "检查应用服务状态..."
        cd "${SCRIPT_DIR}"
        docker-compose -f docker-compose-application.yml ps
        
        log_info "应用部署完成"
    else
        log_error "应用部署失败"
        return 1
    fi
}

# 函数：显示部署结果
show_deployment_result() {
    echo ""
    echo "========================================"
    echo "           部署完成"
    echo "========================================"
    echo ""
    
    local server_ip=$(hostname -I | awk '{print $1}')
    
    echo "🎉 RuoYi-Cloud 部署成功！"
    echo ""
    echo "📋 服务访问地址:"
    echo "  前端系统: http://${server_ip}:80"
    echo "  API网关: http://${server_ip}:8080"
    echo "  Nacos控制台: http://${server_ip}:8848/nacos"
    echo "  Portainer: http://${server_ip}:9000"
    echo ""
    echo "🔑 默认账号信息:"
    echo "  系统管理员: admin / admin123"
    echo "  Nacos: nacos / nacos"
    echo ""
    echo "📁 重要目录:"
    echo "  部署目录: ${DEPLOY_ROOT}"
    echo "  数据目录: ${DATA_ROOT}"
    echo "  日志目录: ${DEPLOY_ROOT}/logs"
    echo ""
    echo "🛠️ 管理命令:"
    echo "  查看服务状态: docker ps"
    echo "  查看日志: docker logs <容器名>"
    echo "  重启服务: docker-compose restart <服务名>"
    echo "  停止所有服务: docker-compose down"
    echo ""
    echo "📖 更多信息请查看项目文档"
    echo "========================================"
}

# 主函数
main() {
    local CHECK_ONLY=false
    local MIDDLEWARE_ONLY=false
    local APPLICATION_ONLY=false
    local FULL_DEPLOY=false
    local FORCE_INSTALL=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -m|--middleware-only)
                MIDDLEWARE_ONLY=true
                shift
                ;;
            -a|--application-only)
                APPLICATION_ONLY=true
                shift
                ;;
            -f|--full)
                FULL_DEPLOY=true
                shift
                ;;
            --force)
                FORCE_INSTALL=true
                shift
                ;;
            --skip-portainer|--skip-firewall)
                # 传递给子脚本
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示欢迎信息
    echo "========================================"
    echo "    RuoYi-Cloud 一键部署脚本 v1.0"
    echo "========================================"
    echo ""
    
    # 显示系统信息
    show_system_info
    
    # 检查脚本权限
    check_script_permissions
    
    # 检查Docker Compose文件
    check_compose_files
    
    # 仅检查环境
    if [[ "$CHECK_ONLY" == "true" ]]; then
        log_info "执行环境检查..."
        "${SCRIPT_DIR}/install-middleware.sh" -c
        "${SCRIPT_DIR}/install-application.sh" -c
        log_info "环境检查完成，系统满足部署要求"
        exit 0
    fi
    
    # 开始部署
    local start_time=$(date +%s)
    
    if [[ "$FULL_DEPLOY" == "true" ]]; then
        # 完整部署
        deploy_middleware "$@"
        deploy_application "$@"
        show_deployment_result
    elif [[ "$MIDDLEWARE_ONLY" == "true" ]]; then
        # 仅部署中间件
        deploy_middleware "$@"
        echo ""
        echo "✅ 中间件部署完成！"
        echo "💡 已安装: MySQL + 达梦数据库 + Redis + Nacos + Nginx + Portainer"
        echo "💡 下一步请运行: $0 -a （部署时可选择使用哪个数据库）"
    elif [[ "$APPLICATION_ONLY" == "true" ]]; then
        # 仅部署应用
        deploy_application "$@"
        show_deployment_result
    else
        # 默认显示帮助
        log_warn "请指定部署选项"
        show_help
        exit 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    echo "⏱️ 总耗时: ${duration} 秒"
    echo ""
}

# 执行主函数
main "$@"
