{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue", "mtime": 1754144052418}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_order", "require", "name", "data", "loading", "ids", "multiple", "total", "orderList", "statistics", "pendingCount", "confirmedCount", "over50TonCount", "todayCount", "queryParams", "pageNum", "pageSize", "orderStatus", "date<PERSON><PERSON><PERSON>", "detailDialogVisible", "detailOrder", "costDialogVisible", "costLoading", "currentOrder", "costForm", "costUnit", "unitPrice", "costBearer", "costRules", "required", "message", "trigger", "type", "min", "created", "getList", "loadStatistics", "methods", "_this", "params", "length", "getPendingConfirmOrders", "then", "response", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "refreshList", "$modal", "msgSuccess", "handleSelectionChange", "selection", "map", "item", "orderId", "handleView", "row", "_this2", "getOrder", "handleConfirm", "_this3", "confirm", "dispatchConfirmOrder", "catch", "handleCostCalculate", "vehicleWeight", "submitCostCalculation", "_this4", "$refs", "validate", "valid", "totalCost", "calculateTotalCost", "calculateOrderCost", "actualStartTime", "actualEndTime", "diff", "Date", "getTime", "duration", "Math", "ceil", "toFixed", "handleBatchConfirm", "_this5", "msgError", "concat", "showCostCalculation", "msgInfo", "handleExport", "calculateDuration", "startTime", "endTime", "hours", "floor", "minutes", "getCostBearerText", "bearerMap", "getStatusTagType", "status", "statusMap", "getStatusText"], "sources": ["src/views/vehicle/order/dispatch-confirm.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">项目调度室 - 台班确认</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.confirmedCount }}</div>\n              <div class=\"stat-label\">已确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.over50TonCount }}</div>\n              <div class=\"stat-label\">超50吨待审</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日处理</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n            <el-option label=\"调度已确认\" value=\"dispatch_confirmed\"></el-option>\n            <el-option label=\"待主管审批\" value=\"pending_manager\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆重量\" prop=\"vehicleWeight\">\n          <el-select v-model=\"queryParams.vehicleWeight\" placeholder=\"请选择重量范围\" clearable @change=\"getList\">\n            <el-option label=\"50吨以下\" value=\"under_50\"></el-option>\n            <el-option label=\"50吨及以上\" value=\"over_50\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"费用承担\" prop=\"costBearer\">\n          <el-select v-model=\"queryParams.costBearer\" placeholder=\"请选择费用承担方\" clearable @change=\"getList\">\n            <el-option label=\"项目承担\" value=\"project\"></el-option>\n            <el-option label=\"队伍承担\" value=\"team\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchConfirm\"\n            v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n          >批量确认</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-money\"\n            size=\"mini\"\n            @click=\"showCostCalculation\"\n          >费用计算</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-download\"\n            size=\"mini\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['vehicle:order:export']\"\n          >导出数据</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n            <div style=\"color: #E6A23C; font-size: 12px;\" v-if=\"scope.row.vehicleWeight >= 50\">\n              重量：{{ scope.row.vehicleWeight }}吨\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"队伍信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.driverName }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"费用信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.totalCost\">\n              <div style=\"color: #67C23A; font-weight: bold;\">￥{{ scope.row.totalCost }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ getCostBearerText(scope.row.costBearer) }}</div>\n            </div>\n            <div v-else style=\"color: #E6A23C;\">待计算</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleConfirm(scope.row)\"\n              v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n            >确认</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleCostCalculate(scope.row)\"\n              v-hasPermi=\"['vehicle:order:cost-calculate']\"\n            >计算费用</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"车辆重量\">{{ detailOrder.vehicleWeight }}吨</el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用信息\">\n          <div v-if=\"detailOrder.totalCost\">\n            <div>总费用：<span style=\"color: #67C23A; font-weight: bold;\">￥{{ detailOrder.totalCost }}</span></div>\n            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>\n            <div>计量单位：{{ detailOrder.costUnit }}</div>\n            <div>单价：￥{{ detailOrder.unitPrice }}</div>\n          </div>\n          <div v-else style=\"color: #E6A23C;\">费用待计算</div>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 确认操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'team_confirmed'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">调度室确认</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleCostCalculate(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-money\"></i> 计算费用\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleConfirm(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 确认订单\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 费用计算对话框 -->\n    <el-dialog title=\"费用计算\" :visible.sync=\"costDialogVisible\" width=\"600px\" append-to-body>\n      <el-form ref=\"costForm\" :model=\"costForm\" :rules=\"costRules\" label-width=\"120px\">\n        <el-form-item label=\"车辆信息\">\n          <div class=\"cost-info\">\n            <div>车辆型号：{{ currentOrder.vehicleInfo ? currentOrder.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div>车辆重量：{{ currentOrder.vehicleWeight }}吨</div>\n            <div>用车时长：{{ calculateDuration(currentOrder.actualStartTime, currentOrder.actualEndTime) }}</div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"计量单位\" prop=\"costUnit\">\n          <el-radio-group v-model=\"costForm.costUnit\">\n            <el-radio label=\"hour\">小时</el-radio>\n            <el-radio label=\"day\">天</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"单价\" prop=\"unitPrice\">\n          <el-input-number\n            v-model=\"costForm.unitPrice\"\n            :precision=\"2\"\n            :min=\"0\"\n            :max=\"99999\"\n            placeholder=\"请输入单价\"\n            style=\"width: 200px;\">\n          </el-input-number>\n          <span style=\"margin-left: 10px;\">元/{{ costForm.costUnit === 'hour' ? '小时' : '天' }}</span>\n        </el-form-item>\n        \n        <el-form-item label=\"费用承担方\" prop=\"costBearer\">\n          <el-radio-group v-model=\"costForm.costBearer\">\n            <el-radio label=\"project\">项目承担</el-radio>\n            <el-radio label=\"team\">队伍承担</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"预计总费用\">\n          <div class=\"cost-preview\">\n            <span style=\"font-size: 18px; color: #67C23A; font-weight: bold;\">\n              ￥{{ calculateTotalCost() }}\n            </span>\n          </div>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"costDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitCostCalculation\" :loading=\"costLoading\">确认计算</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, dispatchConfirmOrder, calculateOrderCost } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"DispatchConfirm\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        confirmedCount: 0,\n        over50TonCount: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'team_confirmed'\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 费用计算对话框\n      costDialogVisible: false,\n      costLoading: false,\n      currentOrder: {},\n      costForm: {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: 'project'\n      },\n      // 费用计算表单验证规则\n      costRules: {\n        unitPrice: [\n          { required: true, message: \"请输入单价\", trigger: \"blur\" },\n          { type: 'number', min: 0.01, message: \"单价必须大于0\", trigger: \"blur\" }\n        ],\n        costBearer: [\n          { required: true, message: \"请选择费用承担方\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('dispatch').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 8,\n        confirmedCount: 15,\n        over50TonCount: 3,\n        todayCount: 5\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 确认订单 */\n    handleConfirm(row) {\n      this.$modal.confirm('确认该订单的费用和用车情况？').then(() => {\n        return dispatchConfirmOrder(row.orderId);\n      }).then(() => {\n        this.$modal.msgSuccess(\"确认成功\");\n        this.detailDialogVisible = false;\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 费用计算 */\n    handleCostCalculate(row) {\n      this.currentOrder = row;\n      this.costDialogVisible = true;\n      this.costForm = {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: row.vehicleWeight >= 50 ? 'project' : 'team'\n      };\n    },\n    \n    /** 提交费用计算 */\n    submitCostCalculation() {\n      this.$refs[\"costForm\"].validate(valid => {\n        if (valid) {\n          this.costLoading = true;\n          const data = {\n            orderId: this.currentOrder.orderId,\n            costUnit: this.costForm.costUnit,\n            unitPrice: this.costForm.unitPrice,\n            costBearer: this.costForm.costBearer,\n            totalCost: this.calculateTotalCost()\n          };\n          \n          calculateOrderCost(data).then(response => {\n            this.$modal.msgSuccess(\"费用计算成功\");\n            this.costDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.costLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 计算总费用 */\n    calculateTotalCost() {\n      if (!this.currentOrder.actualStartTime || !this.currentOrder.actualEndTime || !this.costForm.unitPrice) {\n        return 0;\n      }\n      \n      const diff = new Date(this.currentOrder.actualEndTime).getTime() - new Date(this.currentOrder.actualStartTime).getTime();\n      let duration = 0;\n      \n      if (this.costForm.costUnit === 'hour') {\n        duration = Math.ceil(diff / (1000 * 60 * 60)); // 向上取整小时\n      } else {\n        duration = Math.ceil(diff / (1000 * 60 * 60 * 24)); // 向上取整天\n      }\n      \n      return (duration * this.costForm.unitPrice).toFixed(2);\n    },\n    \n    /** 批量确认 */\n    handleBatchConfirm() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要确认的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量确认接口\n        this.$modal.msgSuccess(\"批量确认成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 显示费用计算 */\n    showCostCalculation() {\n      this.$modal.msgInfo(\"费用计算功能开发中...\");\n    },\n    \n    /** 导出数据 */\n    handleExport() {\n      this.$modal.msgInfo(\"导出功能开发中...\");\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取费用承担方文本 */\n    getCostBearerText(costBearer) {\n      const bearerMap = {\n        'project': '项目承担',\n        'team': '队伍承担'\n      };\n      return bearerMap[costBearer] || '未知';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'team_confirmed': 'warning',\n        'dispatch_confirmed': 'success',\n        'pending_manager': 'primary'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'team_confirmed': '队伍已确认',\n        'dispatch_confirmed': '调度已确认',\n        'pending_manager': '待主管审批'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.cost-info {\n  background: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  line-height: 1.8;\n}\n\n.cost-preview {\n  background: #f0f9ff;\n  padding: 15px;\n  border-radius: 4px;\n  text-align: center;\n  border: 1px solid #b3d8ff;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AA0VA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;QACAC,YAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,mBAAA;MACAC,WAAA;MACA;MACAC,iBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACA;MACAC,SAAA;QACAF,SAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,UAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAlC,OAAA;MACA,KAAAU,WAAA,CAAAyB,MAAA;MACA,SAAArB,SAAA,SAAAA,SAAA,CAAAsB,MAAA;QACA,KAAA1B,WAAA,CAAAyB,MAAA,8BAAArB,SAAA;QACA,KAAAJ,WAAA,CAAAyB,MAAA,4BAAArB,SAAA;MACA;MAEA,IAAAuB,8BAAA,cAAAC,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAA9B,SAAA,GAAAmC,QAAA,CAAAxC,IAAA;QACAmC,KAAA,CAAA/B,KAAA,GAAAoC,QAAA,CAAAxC,IAAA,CAAAqC,MAAA;QACAF,KAAA,CAAAlC,OAAA;MACA;IACA;IAEA,aACAgC,cAAA,WAAAA,eAAA;MACA;MACA,KAAA3B,UAAA;QACAC,YAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;MACA;IACA;IAEA,aACA+B,WAAA,WAAAA,YAAA;MACA,KAAA9B,WAAA,CAAAC,OAAA;MACA,KAAAoB,OAAA;IACA;IAEA,aACAU,UAAA,WAAAA,WAAA;MACA,KAAA3B,SAAA;MACA,KAAA4B,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,WACAG,WAAA,WAAAA,YAAA;MACA,KAAAZ,OAAA;MACA,KAAAC,cAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9C,GAAA,GAAA8C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA;MAAA;MACA,KAAAhD,QAAA,IAAA6C,SAAA,CAAAX,MAAA;IACA;IAEA,WACAe,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,eAAA,EAAAF,GAAA,CAAAF,OAAA,EAAAZ,IAAA,WAAAC,QAAA;QACAc,MAAA,CAAArC,WAAA,GAAAuB,QAAA,CAAAxC,IAAA;QACAsD,MAAA,CAAAtC,mBAAA;MACA;IACA;IAEA,WACAwC,aAAA,WAAAA,cAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAZ,MAAA,CAAAa,OAAA,mBAAAnB,IAAA;QACA,WAAAoB,2BAAA,EAAAN,GAAA,CAAAF,OAAA;MACA,GAAAZ,IAAA;QACAkB,MAAA,CAAAZ,MAAA,CAAAC,UAAA;QACAW,MAAA,CAAAzC,mBAAA;QACAyC,MAAA,CAAAzB,OAAA;MACA,GAAA4B,KAAA;IACA;IAEA,WACAC,mBAAA,WAAAA,oBAAAR,GAAA;MACA,KAAAjC,YAAA,GAAAiC,GAAA;MACA,KAAAnC,iBAAA;MACA,KAAAG,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA,EAAA6B,GAAA,CAAAS,aAAA;MACA;IACA;IAEA,aACAC,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA7C,WAAA;UACA,IAAAnB,IAAA;YACAmD,OAAA,EAAAa,MAAA,CAAA5C,YAAA,CAAA+B,OAAA;YACA7B,QAAA,EAAA0C,MAAA,CAAA3C,QAAA,CAAAC,QAAA;YACAC,SAAA,EAAAyC,MAAA,CAAA3C,QAAA,CAAAE,SAAA;YACAC,UAAA,EAAAwC,MAAA,CAAA3C,QAAA,CAAAG,UAAA;YACA4C,SAAA,EAAAJ,MAAA,CAAAK,kBAAA;UACA;UAEA,IAAAC,yBAAA,EAAAtE,IAAA,EAAAuC,IAAA,WAAAC,QAAA;YACAwB,MAAA,CAAAnB,MAAA,CAAAC,UAAA;YACAkB,MAAA,CAAA9C,iBAAA;YACA8C,MAAA,CAAAhD,mBAAA;YACAgD,MAAA,CAAAhC,OAAA;UACA,GAAA4B,KAAA;YACAI,MAAA,CAAA7C,WAAA;UACA;QACA;MACA;IACA;IAEA,YACAkD,kBAAA,WAAAA,mBAAA;MACA,UAAAjD,YAAA,CAAAmD,eAAA,UAAAnD,YAAA,CAAAoD,aAAA,UAAAnD,QAAA,CAAAE,SAAA;QACA;MACA;MAEA,IAAAkD,IAAA,OAAAC,IAAA,MAAAtD,YAAA,CAAAoD,aAAA,EAAAG,OAAA,SAAAD,IAAA,MAAAtD,YAAA,CAAAmD,eAAA,EAAAI,OAAA;MACA,IAAAC,QAAA;MAEA,SAAAvD,QAAA,CAAAC,QAAA;QACAsD,QAAA,GAAAC,IAAA,CAAAC,IAAA,CAAAL,IAAA;MACA;QACAG,QAAA,GAAAC,IAAA,CAAAC,IAAA,CAAAL,IAAA;MACA;MAEA,QAAAG,QAAA,QAAAvD,QAAA,CAAAE,SAAA,EAAAwD,OAAA;IACA;IAEA,WACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/E,GAAA,CAAAmC,MAAA;QACA,KAAAQ,MAAA,CAAAqC,QAAA;QACA;MACA;MAEA,KAAArC,MAAA,CAAAa,OAAA,mCAAAyB,MAAA,MAAAjF,GAAA,CAAAmC,MAAA,gCAAAE,IAAA;QACA;QACA0C,MAAA,CAAApC,MAAA,CAAAC,UAAA;QACAmC,MAAA,CAAAjD,OAAA;MACA,GAAA4B,KAAA;IACA;IAEA,aACAwB,mBAAA,WAAAA,oBAAA;MACA,KAAAvC,MAAA,CAAAwC,OAAA;IACA;IAEA,WACAC,YAAA,WAAAA,aAAA;MACA,KAAAzC,MAAA,CAAAwC,OAAA;IACA;IAEA,aACAE,iBAAA,WAAAA,kBAAAC,SAAA,EAAAC,OAAA;MACA,KAAAD,SAAA,KAAAC,OAAA;MAEA,IAAAhB,IAAA,OAAAC,IAAA,CAAAe,OAAA,EAAAd,OAAA,SAAAD,IAAA,CAAAc,SAAA,EAAAb,OAAA;MACA,IAAAe,KAAA,GAAAb,IAAA,CAAAc,KAAA,CAAAlB,IAAA;MACA,IAAAmB,OAAA,GAAAf,IAAA,CAAAc,KAAA,CAAAlB,IAAA;MAEA,UAAAU,MAAA,CAAAO,KAAA,kBAAAP,MAAA,CAAAS,OAAA;IACA;IAEA,gBACAC,iBAAA,WAAAA,kBAAArE,UAAA;MACA,IAAAsE,SAAA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAtE,UAAA;IACA;IAEA,eACAuE,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,aACAE,aAAA,WAAAA,cAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}