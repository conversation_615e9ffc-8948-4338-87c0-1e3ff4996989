package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleNotification;
import org.apache.ibatis.annotations.Param;

/**
 * 消息通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleNotificationMapper 
{
    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    public VehicleNotification selectVehicleNotificationByNotificationId(Long notificationId);

    /**
     * 查询消息通知列表
     * 
     * @param vehicleNotification 消息通知
     * @return 消息通知集合
     */
    public List<VehicleNotification> selectVehicleNotificationList(VehicleNotification vehicleNotification);

    /**
     * 新增消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    public int insertVehicleNotification(VehicleNotification vehicleNotification);

    /**
     * 修改消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    public int updateVehicleNotification(VehicleNotification vehicleNotification);

    /**
     * 删除消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int deleteVehicleNotificationByNotificationId(Long notificationId);

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleNotificationByNotificationIds(Long[] notificationIds);

    /**
     * 根据接收人查询通知列表
     * 
     * @param recipient 接收人
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationByRecipient(String recipient);

    /**
     * 根据业务ID和类型查询通知列表
     * 
     * @param businessId 业务ID
     * @param notificationType 通知类型
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationByBusinessIdAndType(@Param("businessId") Long businessId, @Param("notificationType") String notificationType);

    /**
     * 查询未读通知数量
     * 
     * @param recipient 接收人
     * @return 未读数量
     */
    public int countUnreadNotifications(String recipient);

    /**
     * 根据发送状态查询通知列表
     * 
     * @param sendStatus 发送状态
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationBySendStatus(String sendStatus);

    /**
     * 根据阅读状态查询通知列表
     * 
     * @param readStatus 阅读状态
     * @param recipient 接收人
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationByReadStatus(@Param("readStatus") String readStatus, @Param("recipient") String recipient);
}
