{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\info.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\info.js", "mtime": 1754146681214}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVehicleInfo", "query", "request", "url", "method", "params", "listInfo", "getVehicleInfo", "vehicleId", "addVehicleInfo", "data", "updateVehicleInfo", "delVehicleInfo", "exportVehicleInfo", "getAvailableVehicles", "getVehiclesByType", "vehicleType", "updateVehicleStatus", "status", "importVehicleData", "importTemplate"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/info.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询机械车辆信息列表\nexport function listVehicleInfo(query) {\n  return request({\n    url: '/vehicle/info/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询车辆信息列表（别名方法）\nexport function listInfo(query) {\n  return request({\n    url: '/vehicle/info/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询机械车辆信息详细\nexport function getVehicleInfo(vehicleId) {\n  return request({\n    url: '/vehicle/info/' + vehicleId,\n    method: 'get'\n  })\n}\n\n// 新增机械车辆信息\nexport function addVehicleInfo(data) {\n  return request({\n    url: '/vehicle/info',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改机械车辆信息\nexport function updateVehicleInfo(data) {\n  return request({\n    url: '/vehicle/info',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除机械车辆信息\nexport function delVehicleInfo(vehicleId) {\n  return request({\n    url: '/vehicle/info/' + vehicleId,\n    method: 'delete'\n  })\n}\n\n// 导出机械车辆信息\nexport function exportVehicleInfo(query) {\n  return request({\n    url: '/vehicle/info/export',\n    method: 'post',\n    data: query\n  })\n}\n\n// 获取可用车辆列表\nexport function getAvailableVehicles() {\n  return request({\n    url: '/vehicle/info/available',\n    method: 'get'\n  })\n}\n\n// 根据车辆类型获取车辆列表\nexport function getVehiclesByType(vehicleType) {\n  return request({\n    url: '/vehicle/info/type/' + vehicleType,\n    method: 'get'\n  })\n}\n\n// 更新车辆状态\nexport function updateVehicleStatus(vehicleId, status) {\n  return request({\n    url: '/vehicle/info/status/' + vehicleId + '/' + status,\n    method: 'put'\n  })\n}\n\n// 导入车辆信息数据\nexport function importVehicleData(data) {\n  return request({\n    url: '/vehicle/info/importData',\n    method: 'post',\n    data: data\n  })\n}\n\n// 下载车辆信息导入模板\nexport function importTemplate() {\n  return request({\n    url: '/vehicle/info/importTemplate',\n    method: 'post'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACL,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGK,SAAS;IACjCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,SAAS,EAAE;EACxC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGK,SAAS;IACjCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,iBAAiBA,CAACZ,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,oBAAoBA,CAAA,EAAG;EACrC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,iBAAiBA,CAACC,WAAW,EAAE;EAC7C,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGa,WAAW;IACxCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,mBAAmBA,CAACT,SAAS,EAAEU,MAAM,EAAE;EACrD,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGK,SAAS,GAAG,GAAG,GAAGU,MAAM;IACvDd,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,iBAAiBA,CAACT,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}