<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单编号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆类型" prop="vehicleType">
        <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
          <el-option label="待开始" value="pending"></el-option>
          <el-option label="进行中" value="running"></el-option>
          <el-option label="司机已结束" value="driver_finished"></el-option>
          <el-option label="队伍已确认" value="team_confirmed"></el-option>
          <el-option label="调度已确认" value="dispatch_confirmed"></el-option>
          <el-option label="已完成" value="completed"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="队伍名称" prop="teamName">
        <el-input
          v-model="queryParams.teamName"
          placeholder="请输入队伍名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleBatchConfirm"
          v-hasPermi="['vehicle:order:confirm']"
        >批量确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vehicle:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单编号" align="center" prop="orderNumber" />
      <el-table-column label="申请标题" align="center" prop="applicationTitle" />
      <el-table-column label="车辆信息" align="center" prop="vehicleInfo" />
      <el-table-column label="队伍名称" align="center" prop="teamName" />
      <el-table-column label="司机姓名" align="center" prop="driverName" />
      <el-table-column label="使用地点" align="center" prop="usageLocation" />
      <el-table-column label="计划时间" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.plannedStartTime, '{y}-{m}-{d} {h}:{i}') }}</span><br/>
          <span>{{ parseTime(scope.row.plannedEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.order_status" :value="scope.row.orderStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="费用状态" align="center" prop="costStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.costStatus === 'calculated'" type="warning">已计算</el-tag>
          <el-tag v-else-if="scope.row.costStatus === 'confirmed'" type="success">已确认</el-tag>
          <el-tag v-else type="info">未计算</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总费用" align="center" prop="totalCost" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['vehicle:order:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.orderStatus === 'driver_finished'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleTeamConfirm(scope.row)"
            v-hasPermi="['vehicle:order:confirm']"
          >队伍确认</el-button>
          <el-button
            v-if="scope.row.orderStatus === 'team_confirmed'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleDispatchConfirm(scope.row)"
            v-hasPermi="['vehicle:order:confirm']"
          >调度确认</el-button>
          <el-button
            v-if="scope.row.orderStatus === 'dispatch_confirmed'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleManagerConfirm(scope.row)"
            v-hasPermi="['vehicle:order:confirm']"
          >主管确认</el-button>
          <el-button
            v-if="scope.row.costStatus !== 'confirmed' && ['driver_finished', 'completed'].includes(scope.row.orderStatus)"
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleCalculateCost(scope.row)"
            v-hasPermi="['vehicle:order:calculate']"
          >计算费用</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleReject(scope.row)"
            v-hasPermi="['vehicle:order:reject']"
          >退回</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单编号">{{ orderDetail.orderNumber }}</el-descriptions-item>
        <el-descriptions-item label="申请标题">{{ orderDetail.applicationTitle }}</el-descriptions-item>
        <el-descriptions-item label="车辆信息">{{ orderDetail.vehicleInfo }}</el-descriptions-item>
        <el-descriptions-item label="队伍名称">{{ orderDetail.teamName }}</el-descriptions-item>
        <el-descriptions-item label="司机姓名">{{ orderDetail.driverName }}</el-descriptions-item>
        <el-descriptions-item label="使用地点">{{ orderDetail.usageLocation }}</el-descriptions-item>
        <el-descriptions-item label="计划开始时间">{{ parseTime(orderDetail.plannedStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="计划结束时间">{{ parseTime(orderDetail.plannedEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际开始时间">{{ parseTime(orderDetail.actualStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际结束时间">{{ parseTime(orderDetail.actualEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <dict-tag :options="dict.type.order_status" :value="orderDetail.orderStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="费用状态">
          <el-tag v-if="orderDetail.costStatus === 'calculated'" type="warning">已计算</el-tag>
          <el-tag v-else-if="orderDetail.costStatus === 'confirmed'" type="success">已确认</el-tag>
          <el-tag v-else type="info">未计算</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="总费用">{{ orderDetail.totalCost }}</el-descriptions-item>
        <el-descriptions-item label="费用承担方">{{ orderDetail.costBearer }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ orderDetail.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 退回订单对话框 -->
    <el-dialog title="退回订单" :visible.sync="rejectOpen" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" label-width="80px">
        <el-form-item label="退回原因" prop="rejectReason">
          <el-input v-model="rejectForm.rejectReason" type="textarea" placeholder="请输入退回原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitReject">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, teamConfirmOrder, dispatchConfirmOrder, managerConfirmOrder, rejectOrder, calculateOrderCost, batchConfirmOrders } from "@/api/vehicle/order";

export default {
  name: "VehicleOrder",
  dicts: ['vehicle_type', 'order_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 退回弹出层
      rejectOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNumber: null,
        vehicleType: null,
        orderStatus: null,
        teamName: null
      },
      // 订单详情
      orderDetail: {},
      // 退回表单
      rejectForm: {
        orderId: null,
        rejectReason: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.multiple = !selection.length
    },
    /** 查看详情 */
    handleView(row) {
      this.orderDetail = row;
      this.detailOpen = true;
    },
    /** 队伍确认 */
    handleTeamConfirm(row) {
      this.$modal.confirm('是否确认该订单？').then(function() {
        return teamConfirmOrder(row.orderId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 调度确认 */
    handleDispatchConfirm(row) {
      this.$modal.confirm('是否确认该订单？').then(function() {
        return dispatchConfirmOrder(row.orderId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 主管确认 */
    handleManagerConfirm(row) {
      this.$modal.confirm('是否确认该订单？').then(function() {
        return managerConfirmOrder(row.orderId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 计算费用 */
    handleCalculateCost(row) {
      this.$modal.confirm('是否计算该订单费用？').then(function() {
        return calculateOrderCost(row.orderId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("费用计算成功");
      }).catch(() => {});
    },
    /** 退回订单 */
    handleReject(row) {
      this.rejectForm.orderId = row.orderId;
      this.rejectForm.rejectReason = null;
      this.rejectOpen = true;
    },
    /** 提交退回 */
    submitReject() {
      rejectOrder(this.rejectForm.orderId, this.rejectForm.rejectReason).then(() => {
        this.getList();
        this.rejectOpen = false;
        this.$modal.msgSuccess("退回成功");
      });
    },
    /** 批量确认 */
    handleBatchConfirm() {
      const orderIds = this.ids;
      this.$modal.confirm('是否确认选中的订单？').then(function() {
        return batchConfirmOrders(orderIds, "team");
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("批量确认成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('vehicle/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
