package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleOrder;
import com.ruoyi.vehicle.service.IVehicleOrderService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 用车订单Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/order")
public class VehicleOrderController extends BaseController
{
    @Autowired
    private IVehicleOrderService vehicleOrderService;

    /**
     * 查询用车订单列表
     */
    @RequiresPermissions("vehicle:order:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleOrder vehicleOrder)
    {
        startPage();
        List<VehicleOrder> list = vehicleOrderService.selectVehicleOrderList(vehicleOrder);
        return getDataTable(list);
    }

    /**
     * 导出用车订单列表
     */
    @RequiresPermissions("vehicle:order:export")
    @Log(title = "用车订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleOrder vehicleOrder)
    {
        List<VehicleOrder> list = vehicleOrderService.selectVehicleOrderList(vehicleOrder);
        ExcelUtil<VehicleOrder> util = new ExcelUtil<VehicleOrder>(VehicleOrder.class);
        util.exportExcel(response, list, "用车订单数据");
    }

    /**
     * 获取用车订单详细信息
     */
    @RequiresPermissions("vehicle:order:query")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(vehicleOrderService.selectVehicleOrderByOrderId(orderId));
    }

    /**
     * 新增用车订单
     */
    @RequiresPermissions("vehicle:order:add")
    @Log(title = "用车订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleOrder vehicleOrder)
    {
        vehicleOrder.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleOrderService.insertVehicleOrder(vehicleOrder));
    }

    /**
     * 修改用车订单
     */
    @RequiresPermissions("vehicle:order:edit")
    @Log(title = "用车订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleOrder vehicleOrder)
    {
        vehicleOrder.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleOrderService.updateVehicleOrder(vehicleOrder));
    }

    /**
     * 删除用车订单
     */
    @RequiresPermissions("vehicle:order:remove")
    @Log(title = "用车订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(vehicleOrderService.deleteVehicleOrderByOrderIds(orderIds));
    }

    /**
     * 根据申请ID创建订单
     */
    @RequiresPermissions("vehicle:order:add")
    @Log(title = "创建用车订单", businessType = BusinessType.INSERT)
    @PostMapping("/create-from-application/{applicationId}")
    public AjaxResult createFromApplication(@PathVariable Long applicationId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.createOrderFromApplication(applicationId, operName));
    }

    /**
     * 司机开始用车
     */
    @RequiresPermissions("vehicle:order:start")
    @Log(title = "司机开始用车", businessType = BusinessType.UPDATE)
    @PutMapping("/start/{orderId}")
    public AjaxResult startOrder(@PathVariable Long orderId, @RequestParam(required = false) String startPhotoUrl)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.startOrder(orderId, startPhotoUrl, operName));
    }

    /**
     * 司机结束用车
     */
    @RequiresPermissions("vehicle:order:finish")
    @Log(title = "司机结束用车", businessType = BusinessType.UPDATE)
    @PutMapping("/finish/{orderId}")
    public AjaxResult finishOrder(@PathVariable Long orderId, @RequestParam(required = false) String endPhotoUrl)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.finishOrder(orderId, endPhotoUrl, operName));
    }

    /**
     * 队伍确认订单
     */
    @RequiresPermissions("vehicle:order:team-confirm")
    @Log(title = "队伍确认订单", businessType = BusinessType.UPDATE)
    @PutMapping("/team-confirm/{orderId}")
    public AjaxResult teamConfirm(@PathVariable Long orderId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.teamConfirmOrder(orderId, operName));
    }

    /**
     * 调度室确认订单
     */
    @RequiresPermissions("vehicle:order:dispatch-confirm")
    @Log(title = "调度室确认订单", businessType = BusinessType.UPDATE)
    @PutMapping("/dispatch-confirm/{orderId}")
    public AjaxResult dispatchConfirm(@PathVariable Long orderId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.dispatchConfirmOrder(orderId, operName));
    }

    /**
     * 主管确认订单
     */
    @RequiresPermissions("vehicle:order:manager-confirm")
    @Log(title = "主管确认订单", businessType = BusinessType.UPDATE)
    @PutMapping("/manager-confirm/{orderId}")
    public AjaxResult managerConfirm(@PathVariable Long orderId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.managerConfirmOrder(orderId, operName));
    }

    /**
     * 退回订单
     */
    @RequiresPermissions("vehicle:order:reject")
    @Log(title = "退回订单", businessType = BusinessType.UPDATE)
    @PutMapping("/reject/{orderId}")
    public AjaxResult rejectOrder(@PathVariable Long orderId, @RequestBody VehicleOrder vehicleOrder)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.rejectOrder(orderId, vehicleOrder.getRejectReason(), operName));
    }

    /**
     * 根据订单状态查询订单列表
     */
    @RequiresPermissions("vehicle:order:list")
    @GetMapping("/status/{orderStatus}")
    public AjaxResult getByStatus(@PathVariable String orderStatus)
    {
        List<VehicleOrder> list = vehicleOrderService.selectVehicleOrderByStatus(orderStatus);
        return success(list);
    }

    /**
     * 根据车辆ID查询订单列表
     */
    @RequiresPermissions("vehicle:order:list")
    @GetMapping("/vehicle/{vehicleId}")
    public AjaxResult getByVehicleId(@PathVariable Long vehicleId)
    {
        List<VehicleOrder> list = vehicleOrderService.selectVehicleOrderByVehicleId(vehicleId);
        return success(list);
    }

    /**
     * 根据队伍ID查询订单列表
     */
    @RequiresPermissions("vehicle:order:list")
    @GetMapping("/team/{teamId}")
    public AjaxResult getByTeamId(@PathVariable Long teamId)
    {
        List<VehicleOrder> list = vehicleOrderService.selectVehicleOrderByTeamId(teamId);
        return success(list);
    }

    /**
     * 查询待确认的订单列表
     */
    @RequiresPermissions("vehicle:order:list")
    @GetMapping("/pending-confirm/{confirmType}")
    public AjaxResult getPendingConfirm(@PathVariable String confirmType)
    {
        List<VehicleOrder> list = vehicleOrderService.selectPendingConfirmOrders(confirmType);
        return success(list);
    }

    /**
     * 批量确认订单
     */
    @RequiresPermissions("vehicle:order:confirm")
    @Log(title = "批量确认订单", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-confirm")
    public AjaxResult batchConfirm(@RequestBody VehicleOrder vehicleOrder, @RequestParam String confirmType)
    {
        String operName = SecurityUtils.getUsername();
        Long[] orderIds = vehicleOrder.getOrderId() != null ? 
            new Long[]{vehicleOrder.getOrderId()} : new Long[0];
        
        int result = vehicleOrderService.batchConfirmOrders(orderIds, confirmType, operName);
        return success("批量确认完成，成功处理 " + result + " 条订单");
    }

    /**
     * 上传拍照文件
     */
    @RequiresPermissions("vehicle:order:upload")
    @Log(title = "上传拍照文件", businessType = BusinessType.UPDATE)
    @PostMapping("/upload-photo")
    public AjaxResult uploadPhoto(@RequestParam("file") MultipartFile file)
    {
        // TODO: 实现文件上传逻辑
        return success("文件上传功能开发中...");
    }

    /**
     * 计算订单费用
     */
    @RequiresPermissions("vehicle:order:cost")
    @Log(title = "计算订单费用", businessType = BusinessType.UPDATE)
    @PutMapping("/calculate-cost/{orderId}")
    public AjaxResult calculateCost(@PathVariable Long orderId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.calculateOrderCost(orderId, operName));
    }

    /**
     * 确认订单费用
     */
    @RequiresPermissions("vehicle:order:cost")
    @Log(title = "确认订单费用", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm-cost/{orderId}")
    public AjaxResult confirmCost(@PathVariable Long orderId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleOrderService.confirmOrderCost(orderId, operName));
    }
}
