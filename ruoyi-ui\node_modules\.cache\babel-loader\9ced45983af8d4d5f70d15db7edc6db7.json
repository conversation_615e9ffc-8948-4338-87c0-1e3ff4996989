{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\application.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\application.js", "mtime": 1754146594188}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listApplication", "query", "request", "url", "method", "params", "getApplication", "applicationId", "addApplication", "data", "updateApplication", "delApplication", "exportApplication", "approveApplication", "rejectApplication", "assignVehicle", "dispatchConfirm", "getApplicationsByStatus", "status", "getApplicationsByApplicant", "applicant", "getApplicationsByTeam", "teamId", "getPendingApplications", "getApprovedApplications", "getDispatchedApplications", "batchApprove", "applicationIds", "batchReject", "rejectReason", "getApplicationStats", "getApplicationsByVehicleType", "vehicleType", "getUrgentApplications", "cancelApplication", "reason", "resubmitApplication", "submitApplication", "getAvailableVehicles", "batchAssignVehicle"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/application.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询用车申请列表\nexport function listApplication(query) {\n  return request({\n    url: '/vehicle/application/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询用车申请详细\nexport function getApplication(applicationId) {\n  return request({\n    url: '/vehicle/application/' + applicationId,\n    method: 'get'\n  })\n}\n\n// 新增用车申请\nexport function addApplication(data) {\n  return request({\n    url: '/vehicle/application',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改用车申请\nexport function updateApplication(data) {\n  return request({\n    url: '/vehicle/application',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除用车申请\nexport function delApplication(applicationId) {\n  return request({\n    url: '/vehicle/application/' + applicationId,\n    method: 'delete'\n  })\n}\n\n// 导出用车申请\nexport function exportApplication(query) {\n  return request({\n    url: '/vehicle/application/export',\n    method: 'get',\n    params: query\n  })\n}\n\n// 审批用车申请\nexport function approveApplication(data) {\n  return request({\n    url: '/vehicle/application/approve',\n    method: 'put',\n    data: data\n  })\n}\n\n// 拒绝用车申请\nexport function rejectApplication(data) {\n  return request({\n    url: '/vehicle/application/reject',\n    method: 'put',\n    data: data\n  })\n}\n\n// 分配车辆\nexport function assignVehicle(data) {\n  return request({\n    url: '/vehicle/application/assign',\n    method: 'put',\n    data: data\n  })\n}\n\n// 调度确认\nexport function dispatchConfirm(data) {\n  return request({\n    url: '/vehicle/application/dispatch-confirm',\n    method: 'put',\n    data: data\n  })\n}\n\n// 根据申请状态查询申请列表\nexport function getApplicationsByStatus(status) {\n  return request({\n    url: '/vehicle/application/status/' + status,\n    method: 'get'\n  })\n}\n\n// 根据申请人查询申请列表\nexport function getApplicationsByApplicant(applicant) {\n  return request({\n    url: '/vehicle/application/applicant/' + applicant,\n    method: 'get'\n  })\n}\n\n// 根据队伍ID查询申请列表\nexport function getApplicationsByTeam(teamId) {\n  return request({\n    url: '/vehicle/application/team/' + teamId,\n    method: 'get'\n  })\n}\n\n// 查询待审批的申请列表\nexport function getPendingApplications() {\n  return request({\n    url: '/vehicle/application/pending',\n    method: 'get'\n  })\n}\n\n// 查询已审批的申请列表\nexport function getApprovedApplications() {\n  return request({\n    url: '/vehicle/application/approved',\n    method: 'get'\n  })\n}\n\n// 查询已调度的申请列表\nexport function getDispatchedApplications() {\n  return request({\n    url: '/vehicle/application/dispatched',\n    method: 'get'\n  })\n}\n\n// 批量审批申请\nexport function batchApprove(applicationIds) {\n  return request({\n    url: '/vehicle/application/batch-approve',\n    method: 'put',\n    data: {\n      applicationIds: applicationIds\n    }\n  })\n}\n\n// 批量拒绝申请\nexport function batchReject(applicationIds, rejectReason) {\n  return request({\n    url: '/vehicle/application/batch-reject',\n    method: 'put',\n    data: {\n      applicationIds: applicationIds,\n      rejectReason: rejectReason\n    }\n  })\n}\n\n// 获取申请统计信息\nexport function getApplicationStats() {\n  return request({\n    url: '/vehicle/application/stats',\n    method: 'get'\n  })\n}\n\n// 根据车辆类型查询申请列表\nexport function getApplicationsByVehicleType(vehicleType) {\n  return request({\n    url: '/vehicle/application/vehicle-type/' + vehicleType,\n    method: 'get'\n  })\n}\n\n// 查询紧急申请列表\nexport function getUrgentApplications() {\n  return request({\n    url: '/vehicle/application/urgent',\n    method: 'get'\n  })\n}\n\n// 撤销申请\nexport function cancelApplication(applicationId, reason) {\n  return request({\n    url: '/vehicle/application/cancel/' + applicationId,\n    method: 'put',\n    data: {\n      reason: reason\n    }\n  })\n}\n\n// 重新提交申请\nexport function resubmitApplication(applicationId, data) {\n  return request({\n    url: '/vehicle/application/resubmit/' + applicationId,\n    method: 'put',\n    data: data\n  })\n}\n\n// 提交申请\nexport function submitApplication(data) {\n  return request({\n    url: '/vehicle/application/submit',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取可用车辆列表\nexport function getAvailableVehicles(query) {\n  return request({\n    url: '/vehicle/application/available-vehicles',\n    method: 'get',\n    params: query\n  })\n}\n\n// 批量分配车辆\nexport function batchAssignVehicle(data) {\n  return request({\n    url: '/vehicle/application/batch-assign',\n    method: 'put',\n    data: data\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,aAAa;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,aAAa;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,iBAAiBA,CAACX,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,kBAAkBA,CAACJ,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACL,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,aAAaA,CAACN,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,eAAeA,CAACP,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,uBAAuBA,CAACC,MAAM,EAAE;EAC9C,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGe,MAAM;IAC5Cd,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,0BAA0BA,CAACC,SAAS,EAAE;EACpD,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC,GAAGiB,SAAS;IAClDhB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAApB,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGmB,MAAM;IAC1ClB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASmB,sBAAsBA,CAAA,EAAG;EACvC,OAAO,IAAArB,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAtB,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,IAAAvB,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASsB,YAAYA,CAACC,cAAc,EAAE;EAC3C,OAAO,IAAAzB,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAE;MACJkB,cAAc,EAAEA;IAClB;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,cAAc,EAAEE,YAAY,EAAE;EACxD,OAAO,IAAA3B,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAE;MACJkB,cAAc,EAAEA,cAAc;MAC9BE,YAAY,EAAEA;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,OAAO,IAAA5B,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS2B,4BAA4BA,CAACC,WAAW,EAAE;EACxD,OAAO,IAAA9B,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC,GAAG6B,WAAW;IACvD5B,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS6B,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAA/B,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS8B,iBAAiBA,CAAC3B,aAAa,EAAE4B,MAAM,EAAE;EACvD,OAAO,IAAAjC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGI,aAAa;IACnDH,MAAM,EAAE,KAAK;IACbK,IAAI,EAAE;MACJ0B,MAAM,EAAEA;IACV;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAAC7B,aAAa,EAAEE,IAAI,EAAE;EACvD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGI,aAAa;IACrDH,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS4B,iBAAiBA,CAAC5B,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAAS6B,oBAAoBA,CAACrC,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASsC,kBAAkBA,CAAC9B,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}