package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleOrder;

/**
 * 用车订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleOrderService 
{
    /**
     * 查询用车订单
     * 
     * @param orderId 用车订单主键
     * @return 用车订单
     */
    public VehicleOrder selectVehicleOrderByOrderId(Long orderId);

    /**
     * 查询用车订单列表
     * 
     * @param vehicleOrder 用车订单
     * @return 用车订单集合
     */
    public List<VehicleOrder> selectVehicleOrderList(VehicleOrder vehicleOrder);

    /**
     * 新增用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    public int insertVehicleOrder(VehicleOrder vehicleOrder);

    /**
     * 修改用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    public int updateVehicleOrder(VehicleOrder vehicleOrder);

    /**
     * 批量删除用车订单
     * 
     * @param orderIds 需要删除的用车订单主键集合
     * @return 结果
     */
    public int deleteVehicleOrderByOrderIds(Long[] orderIds);

    /**
     * 删除用车订单信息
     * 
     * @param orderId 用车订单主键
     * @return 结果
     */
    public int deleteVehicleOrderByOrderId(Long orderId);

    /**
     * 根据申请ID创建订单
     * 
     * @param applicationId 申请ID
     * @param operName 操作人
     * @return 结果
     */
    public int createOrderFromApplication(Long applicationId, String operName);

    /**
     * 司机开始用车
     * 
     * @param orderId 订单ID
     * @param startPhotoUrl 开始拍照URL
     * @param operName 操作人
     * @return 结果
     */
    public int startOrder(Long orderId, String startPhotoUrl, String operName);

    /**
     * 司机结束用车
     * 
     * @param orderId 订单ID
     * @param endPhotoUrl 结束拍照URL
     * @param operName 操作人
     * @return 结果
     */
    public int finishOrder(Long orderId, String endPhotoUrl, String operName);

    /**
     * 队伍确认订单
     * 
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    public int teamConfirmOrder(Long orderId, String operName);

    /**
     * 调度室确认订单
     * 
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    public int dispatchConfirmOrder(Long orderId, String operName);

    /**
     * 主管确认订单
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    public int managerConfirmOrder(Long orderId, String operName);

    /**
     * 主管审批订单
     *
     * @param approvalData 审批数据
     * @return 结果
     */
    public int managerApproveOrder(Object approvalData);

    /**
     * 退回订单
     * 
     * @param orderId 订单ID
     * @param rejectReason 退回原因
     * @param operName 操作人
     * @return 结果
     */
    public int rejectOrder(Long orderId, String rejectReason, String operName);

    /**
     * 根据订单状态查询订单列表
     * 
     * @param orderStatus 订单状态
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByStatus(String orderStatus);

    /**
     * 根据车辆ID查询订单列表
     * 
     * @param vehicleId 车辆ID
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByVehicleId(Long vehicleId);

    /**
     * 根据队伍ID查询订单列表
     * 
     * @param teamId 队伍ID
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByTeamId(Long teamId);

    /**
     * 查询待确认的订单列表
     *
     * @param confirmType 确认类型（team、dispatch、manager）
     * @return 订单集合
     */
    public List<VehicleOrder> selectPendingConfirmOrders(String confirmType);

    /**
     * 获取待确认订单列表（别名方法）
     *
     * @param confirmType 确认类型
     * @return 订单列表
     */
    public List<VehicleOrder> getPendingConfirmOrders(String confirmType);

    /**
     * 批量确认订单
     *
     * @param orderIds 订单ID数组
     * @param confirmType 确认类型
     * @param operName 操作人
     * @return 结果
     */
    public int batchConfirmOrders(Long[] orderIds, String confirmType, String operName);

    /**
     * 计算订单费用
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    public int calculateOrderCost(Long orderId, String operName);

    /**
     * 确认订单费用
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    public int confirmOrderCost(Long orderId, String operName);
}
