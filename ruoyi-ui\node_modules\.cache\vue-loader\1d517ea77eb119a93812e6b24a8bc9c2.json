{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\apply.vue", "mtime": 1754142645182}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHN1Ym1pdERlbWFuZCwgYWRkRGVtYW5kIH0gZnJvbSAiQC9hcGkvdmVoaWNsZS9kZW1hbmQiOwppbXBvcnQgeyBnZXRUZWFtT3B0aW9ucyB9IGZyb20gIkAvYXBpL3ZlaGljbGUvdGVhbSI7CmltcG9ydCB7IGdldERpY3RzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRlbWFuZEFwcGx5IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6KGo5Y2V5pWw5o2uCiAgICAgIGZvcm06IHsKICAgICAgICBwbGFuVGl0bGU6ICcnLAogICAgICAgIHZlaGljbGVUeXBlOiAnJywKICAgICAgICB2ZWhpY2xlTW9kZWw6ICcnLAogICAgICAgIGRlbWFuZFVuaXQ6ICcnLAogICAgICAgIGRlbWFuZFF1YW50aXR5OiAxLAogICAgICAgIGRlbWFuZFN0YXJ0VGltZTogJycsCiAgICAgICAgZGVtYW5kRW5kVGltZTogJycsCiAgICAgICAgdXNhZ2VQdXJwb3NlOiAnJywKICAgICAgICB0ZWFtSWQ6IG51bGwsCiAgICAgICAgYXBwbGljYW50OiAnJywKICAgICAgICBhcHBsaWNhbnRQaG9uZTogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICAvLyDmj5DkuqTnirbmgIEKICAgICAgc3VibWl0TG9hZGluZzogZmFsc2UsCiAgICAgIC8vIOmAiemhueaVsOaNrgogICAgICB2ZWhpY2xlVHlwZU9wdGlvbnM6IFtdLAogICAgICB2ZWhpY2xlTW9kZWxPcHRpb25zOiBbXSwKICAgICAgdGVhbU9wdGlvbnM6IFtdLAogICAgICAvLyDooajljZXpqozor4Hop4TliJkKICAgICAgcnVsZXM6IHsKICAgICAgICBwbGFuVGl0bGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorqHliJLmoIfpopjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWluOiAyLCBtYXg6IDEwMCwgbWVzc2FnZTogIumVv+W6puWcqCAyIOWIsCAxMDAg5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHZlaGljbGVUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6L2m6L6G57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdmVoaWNsZU1vZGVsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6L2m6L6G5Z6L5Y+3IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgZGVtYW5kVW5pdDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZW1hbmRRdWFudGl0eTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguaVsOmHj+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAxLCBtZXNzYWdlOiAi6ZyA5rGC5pWw6YeP6Iez5bCR5Li6MSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZW1hbmRTdGFydFRpbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6npnIDmsYLlvIDlp4vml7bpl7QiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBkZW1hbmRFbmRUaW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6ZyA5rGC57uT5p2f5pe26Ze0IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdXNhZ2VQdXJwb3NlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55So6YCU6K+05piO5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1pbjogMTAsIG1heDogNTAwLCBtZXNzYWdlOiAi6ZW/5bqm5ZyoIDEwIOWIsCA1MDAg5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRlYW1JZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqemYn+S8jSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGFwcGxpY2FudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUs+ivt+S6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBhcHBsaWNhbnRQaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxvYWRPcHRpb25zKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L296YCJ6aG55pWw5o2uICovCiAgICBhc3luYyBsb2FkT3B0aW9ucygpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDliqDovb3ovabovobnsbvlnovlrZflhbgKICAgICAgICBjb25zdCB2ZWhpY2xlVHlwZVJlcyA9IGF3YWl0IGdldERpY3RzKCJ2ZWhpY2xlX3R5cGUiKTsKICAgICAgICB0aGlzLnZlaGljbGVUeXBlT3B0aW9ucyA9IHZlaGljbGVUeXBlUmVzLmRhdGE7CiAgICAgICAgCiAgICAgICAgLy8g5Yqg6L296Zif5LyN6YCJ6aG5CiAgICAgICAgY29uc3QgdGVhbVJlcyA9IGF3YWl0IGdldFRlYW1PcHRpb25zKCk7CiAgICAgICAgdGhpcy50ZWFtT3B0aW9ucyA9IHRlYW1SZXMuZGF0YTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5Yqg6L296YCJ6aG55pWw5o2u5aSx6LSlIik7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8qKiDovabovobnsbvlnovlj5jljJblpITnkIYgKi8KICAgIGFzeW5jIGhhbmRsZVZlaGljbGVUeXBlQ2hhbmdlKHZhbHVlKSB7CiAgICAgIHRoaXMuZm9ybS52ZWhpY2xlTW9kZWwgPSAnJzsKICAgICAgdGhpcy52ZWhpY2xlTW9kZWxPcHRpb25zID0gW107CiAgICAgIAogICAgICBpZiAodmFsdWUpIHsKICAgICAgICB0cnkgewogICAgICAgICAgLy8g5qC55o2u6L2m6L6G57G75Z6L5Yqg6L295a+55bqU55qE5Z6L5Y+3CiAgICAgICAgICBjb25zdCBtb2RlbFJlcyA9IGF3YWl0IGdldERpY3RzKCJ2ZWhpY2xlX21vZGVsXyIgKyB2YWx1ZSk7CiAgICAgICAgICB0aGlzLnZlaGljbGVNb2RlbE9wdGlvbnMgPSBtb2RlbFJlcy5kYXRhOwogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oIuWKoOi9vei9pui+huWei+WPt+Wksei0pToiLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgCiAgICAvKiog6Zif5LyN5Y+Y5YyW5aSE55CGICovCiAgICBoYW5kbGVUZWFtQ2hhbmdlKHRlYW1JZCkgewogICAgICBjb25zdCBzZWxlY3RlZFRlYW0gPSB0aGlzLnRlYW1PcHRpb25zLmZpbmQodGVhbSA9PiB0ZWFtLnRlYW1JZCA9PT0gdGVhbUlkKTsKICAgICAgaWYgKHNlbGVjdGVkVGVhbSkgewogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhbnQgPSBzZWxlY3RlZFRlYW0udGVhbUxlYWRlcjsKICAgICAgICB0aGlzLmZvcm0uYXBwbGljYW50UGhvbmUgPSBzZWxlY3RlZFRlYW0ubGVhZGVyUGhvbmU7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8qKiDmj5DkuqTooajljZUgKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDpqozor4Hml7bpl7TojIPlm7QKICAgICAgICAgIGlmIChuZXcgRGF0ZSh0aGlzLmZvcm0uZGVtYW5kRW5kVGltZSkgPD0gbmV3IERhdGUodGhpcy5mb3JtLmRlbWFuZFN0YXJ0VGltZSkpIHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIumcgOaxgue7k+adn+aXtumXtOW/hemhu+Wkp+S6juW8gOWni+aXtumXtCIpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICAKICAgICAgICAgIHRoaXMuc3VibWl0TG9hZGluZyA9IHRydWU7CiAgICAgICAgICBzdWJtaXREZW1hbmQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6ZyA5rGC6K6h5YiS55Sz6K+35o+Q5Lqk5oiQ5YqfIik7CiAgICAgICAgICAgIHRoaXMuZ29CYWNrKCk7CiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc3VibWl0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDkv53lrZjojYnnqL8gKi8KICAgIHNhdmVEcmFmdCgpIHsKICAgICAgY29uc3QgZHJhZnREYXRhID0geyAuLi50aGlzLmZvcm0sIGFwcHJvdmFsU3RhdHVzOiAnZHJhZnQnIH07CiAgICAgIGFkZERlbWFuZChkcmFmdERhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuiNieeov+S/neWtmOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDph43nva7ooajljZUgKi8KICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnJlc2V0RmllbGRzKCk7CiAgICAgIHRoaXMudmVoaWNsZU1vZGVsT3B0aW9ucyA9IFtdOwogICAgfSwKICAgIAogICAgLyoqIOi/lOWbnuWIl+ihqCAqLwogICAgZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3ZlaGljbGUvZGVtYW5kJyk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/vehicle/demand", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">车辆需求计划申请</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\" size=\"medium\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"计划标题\" prop=\"planTitle\">\n              <el-input v-model=\"form.planTitle\" placeholder=\"请输入计划标题\" maxlength=\"100\" show-word-limit />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求单位\" prop=\"demandUnit\">\n              <el-input v-model=\"form.demandUnit\" placeholder=\"请输入需求单位\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\" @change=\"handleVehicleTypeChange\">\n                <el-option\n                  v-for=\"type in vehicleTypeOptions\"\n                  :key=\"type.dictValue\"\n                  :label=\"type.dictLabel\"\n                  :value=\"type.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-select v-model=\"form.vehicleModel\" placeholder=\"请选择车辆型号\" :disabled=\"!form.vehicleType\">\n                <el-option\n                  v-for=\"model in vehicleModelOptions\"\n                  :key=\"model.dictValue\"\n                  :label=\"model.dictLabel\"\n                  :value=\"model.dictValue\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"队伍信息\" prop=\"teamId\">\n              <el-select v-model=\"form.teamId\" placeholder=\"请选择队伍\" @change=\"handleTeamChange\">\n                <el-option\n                  v-for=\"team in teamOptions\"\n                  :key=\"team.teamId\"\n                  :label=\"team.teamName\"\n                  :value=\"team.teamId\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"申请人\" prop=\"applicant\">\n              <el-input v-model=\"form.applicant\" placeholder=\"请输入申请人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"applicantPhone\">\n              <el-input v-model=\"form.applicantPhone\" placeholder=\"请输入联系电话\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求数量\" prop=\"demandQuantity\">\n              <el-input-number v-model=\"form.demandQuantity\" :min=\"1\" :max=\"100\" placeholder=\"请输入需求数量\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求开始时间\" prop=\"demandStartTime\">\n              <el-date-picker\n                v-model=\"form.demandStartTime\"\n                type=\"datetime\"\n                placeholder=\"请选择需求开始时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"需求结束时间\" prop=\"demandEndTime\">\n              <el-date-picker\n                v-model=\"form.demandEndTime\"\n                type=\"datetime\"\n                placeholder=\"请选择需求结束时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-form-item label=\"用途说明\" prop=\"usagePurpose\">\n          <el-input\n            v-model=\"form.usagePurpose\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请详细说明车辆用途\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息\"\n            maxlength=\"200\"\n            show-word-limit />\n        </el-form-item>\n\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">\n            <i class=\"el-icon-check\"></i> 提交申请\n          </el-button>\n          <el-button @click=\"resetForm\">\n            <i class=\"el-icon-refresh-left\"></i> 重置\n          </el-button>\n          <el-button @click=\"saveDraft\">\n            <i class=\"el-icon-document\"></i> 保存草稿\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { submitDemand, addDemand } from \"@/api/vehicle/demand\";\nimport { getTeamOptions } from \"@/api/vehicle/team\";\nimport { getDicts } from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"DemandApply\",\n  data() {\n    return {\n      // 表单数据\n      form: {\n        planTitle: '',\n        vehicleType: '',\n        vehicleModel: '',\n        demandUnit: '',\n        demandQuantity: 1,\n        demandStartTime: '',\n        demandEndTime: '',\n        usagePurpose: '',\n        teamId: null,\n        applicant: '',\n        applicantPhone: '',\n        remark: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 选项数据\n      vehicleTypeOptions: [],\n      vehicleModelOptions: [],\n      teamOptions: [],\n      // 表单验证规则\n      rules: {\n        planTitle: [\n          { required: true, message: \"计划标题不能为空\", trigger: \"blur\" },\n          { min: 2, max: 100, message: \"长度在 2 到 100 个字符\", trigger: \"blur\" }\n        ],\n        vehicleType: [\n          { required: true, message: \"请选择车辆类型\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"请选择车辆型号\", trigger: \"change\" }\n        ],\n        demandUnit: [\n          { required: true, message: \"需求单位不能为空\", trigger: \"blur\" }\n        ],\n        demandQuantity: [\n          { required: true, message: \"需求数量不能为空\", trigger: \"blur\" },\n          { type: 'number', min: 1, message: \"需求数量至少为1\", trigger: \"blur\" }\n        ],\n        demandStartTime: [\n          { required: true, message: \"请选择需求开始时间\", trigger: \"change\" }\n        ],\n        demandEndTime: [\n          { required: true, message: \"请选择需求结束时间\", trigger: \"change\" }\n        ],\n        usagePurpose: [\n          { required: true, message: \"用途说明不能为空\", trigger: \"blur\" },\n          { min: 10, max: 500, message: \"长度在 10 到 500 个字符\", trigger: \"blur\" }\n        ],\n        teamId: [\n          { required: true, message: \"请选择队伍\", trigger: \"change\" }\n        ],\n        applicant: [\n          { required: true, message: \"申请人不能为空\", trigger: \"blur\" }\n        ],\n        applicantPhone: [\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.loadOptions();\n  },\n  methods: {\n    /** 加载选项数据 */\n    async loadOptions() {\n      try {\n        // 加载车辆类型字典\n        const vehicleTypeRes = await getDicts(\"vehicle_type\");\n        this.vehicleTypeOptions = vehicleTypeRes.data;\n        \n        // 加载队伍选项\n        const teamRes = await getTeamOptions();\n        this.teamOptions = teamRes.data;\n      } catch (error) {\n        this.$modal.msgError(\"加载选项数据失败\");\n      }\n    },\n    \n    /** 车辆类型变化处理 */\n    async handleVehicleTypeChange(value) {\n      this.form.vehicleModel = '';\n      this.vehicleModelOptions = [];\n      \n      if (value) {\n        try {\n          // 根据车辆类型加载对应的型号\n          const modelRes = await getDicts(\"vehicle_model_\" + value);\n          this.vehicleModelOptions = modelRes.data;\n        } catch (error) {\n          console.warn(\"加载车辆型号失败:\", error);\n        }\n      }\n    },\n    \n    /** 队伍变化处理 */\n    handleTeamChange(teamId) {\n      const selectedTeam = this.teamOptions.find(team => team.teamId === teamId);\n      if (selectedTeam) {\n        this.form.applicant = selectedTeam.teamLeader;\n        this.form.applicantPhone = selectedTeam.leaderPhone;\n      }\n    },\n    \n    /** 提交表单 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 验证时间范围\n          if (new Date(this.form.demandEndTime) <= new Date(this.form.demandStartTime)) {\n            this.$modal.msgError(\"需求结束时间必须大于开始时间\");\n            return;\n          }\n          \n          this.submitLoading = true;\n          submitDemand(this.form).then(response => {\n            this.$modal.msgSuccess(\"需求计划申请提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 保存草稿 */\n    saveDraft() {\n      const draftData = { ...this.form, approvalStatus: 'draft' };\n      addDemand(draftData).then(response => {\n        this.$modal.msgSuccess(\"草稿保存成功\");\n      });\n    },\n    \n    /** 重置表单 */\n    resetForm() {\n      this.$refs[\"form\"].resetFields();\n      this.vehicleModelOptions = [];\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/demand');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-form-item {\n  margin-bottom: 22px;\n}\n\n.el-button {\n  margin-right: 10px;\n}\n</style>\n"]}]}