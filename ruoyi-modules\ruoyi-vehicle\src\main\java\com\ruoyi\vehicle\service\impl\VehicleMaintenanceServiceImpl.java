package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.VehicleMaintenanceMapper;
import com.ruoyi.vehicle.domain.VehicleMaintenance;
import com.ruoyi.vehicle.service.IVehicleMaintenanceService;

/**
 * 车辆维修记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@Service
public class VehicleMaintenanceServiceImpl implements IVehicleMaintenanceService 
{
    @Autowired
    private VehicleMaintenanceMapper vehicleMaintenanceMapper;

    /**
     * 查询车辆维修记录
     * 
     * @param maintenanceId 车辆维修记录主键
     * @return 车辆维修记录
     */
    @Override
    public VehicleMaintenance selectVehicleMaintenanceByMaintenanceId(Long maintenanceId)
    {
        return vehicleMaintenanceMapper.selectVehicleMaintenanceByMaintenanceId(maintenanceId);
    }

    /**
     * 查询车辆维修记录列表
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 车辆维修记录
     */
    @Override
    public List<VehicleMaintenance> selectVehicleMaintenanceList(VehicleMaintenance vehicleMaintenance)
    {
        return vehicleMaintenanceMapper.selectVehicleMaintenanceList(vehicleMaintenance);
    }

    /**
     * 新增车辆维修记录
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 结果
     */
    @Override
    public int insertVehicleMaintenance(VehicleMaintenance vehicleMaintenance)
    {
        vehicleMaintenance.setCreateTime(DateUtils.getNowDate());
        return vehicleMaintenanceMapper.insertVehicleMaintenance(vehicleMaintenance);
    }

    /**
     * 修改车辆维修记录
     * 
     * @param vehicleMaintenance 车辆维修记录
     * @return 结果
     */
    @Override
    public int updateVehicleMaintenance(VehicleMaintenance vehicleMaintenance)
    {
        vehicleMaintenance.setUpdateTime(DateUtils.getNowDate());
        return vehicleMaintenanceMapper.updateVehicleMaintenance(vehicleMaintenance);
    }

    /**
     * 批量删除车辆维修记录
     * 
     * @param maintenanceIds 需要删除的车辆维修记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleMaintenanceByMaintenanceIds(Long[] maintenanceIds)
    {
        return vehicleMaintenanceMapper.deleteVehicleMaintenanceByMaintenanceIds(maintenanceIds);
    }

    /**
     * 删除车辆维修记录信息
     * 
     * @param maintenanceId 车辆维修记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleMaintenanceByMaintenanceId(Long maintenanceId)
    {
        return vehicleMaintenanceMapper.deleteVehicleMaintenanceByMaintenanceId(maintenanceId);
    }

    /**
     * 根据车辆ID查询维修记录
     * 
     * @param vehicleId 车辆ID
     * @return 维修记录集合
     */
    @Override
    public List<VehicleMaintenance> selectVehicleMaintenanceByVehicleId(Long vehicleId)
    {
        return vehicleMaintenanceMapper.selectVehicleMaintenanceByVehicleId(vehicleId);
    }

    /**
     * 根据状态查询维修记录
     * 
     * @param status 状态
     * @return 维修记录集合
     */
    @Override
    public List<VehicleMaintenance> selectVehicleMaintenanceByStatus(String status)
    {
        return vehicleMaintenanceMapper.selectVehicleMaintenanceByStatus(status);
    }

    /**
     * 查询即将到期的维修记录
     * 
     * @return 维修记录集合
     */
    @Override
    public List<VehicleMaintenance> selectUpcomingMaintenance()
    {
        return vehicleMaintenanceMapper.selectUpcomingMaintenance();
    }
}
