{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\dashboard\\index.vue", "mtime": 1754138635528}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVmVoaWNsZURhc2hib2FyZCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBzdGF0aXN0aWNzOiB7CiAgICAgICAgdG90YWxWZWhpY2xlczogMCwKICAgICAgICBhdmFpbGFibGVWZWhpY2xlczogMCwKICAgICAgICBwZW5kaW5nT3JkZXJzOiAwLAogICAgICAgIG1haW50ZW5hbmNlVmVoaWNsZXM6IDAKICAgICAgfSwKICAgICAgLy8g5Yqf6IO95qih5Z2XCiAgICAgIGZ1bmN0aW9uTW9kdWxlczogWwogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICLovabovobkv6Hmga/nrqHnkIYiLAogICAgICAgICAgZGVzY3JpcHRpb246ICLnrqHnkIbmnLrmorDovabovobln7rmnKzkv6Hmga/jgIHov53nq6DorrDlvZXjgIHnu7Tkv67orrDlvZUiLAogICAgICAgICAgaWNvbjogImVsLWljb24tdHJ1Y2siLAogICAgICAgICAgcGF0aDogIi92ZWhpY2xlL2luZm8iCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAi6ZyA5rGC6K6h5YiS566h55CGIiwKICAgICAgICAgIGRlc2NyaXB0aW9uOiAi6L2m6L6G6ZyA5rGC6K6h5YiS55Sz6K+35LiO5a6h5om55rWB56iL566h55CGIiwKICAgICAgICAgIGljb246ICJlbC1pY29uLWRvY3VtZW50LWFkZCIsCiAgICAgICAgICBwYXRoOiAiL3ZlaGljbGUvZGVtYW5kIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogIueUqOi9pueUs+ivt+euoeeQhiIsCiAgICAgICAgICBkZXNjcmlwdGlvbjogIuacuuaisOeUqOi9pueUs+ivt+S4juiwg+W6puWuieaOkueuoeeQhiIsCiAgICAgICAgICBpY29uOiAiZWwtaWNvbi1lZGl0LW91dGxpbmUiLAogICAgICAgICAgcGF0aDogIi92ZWhpY2xlL2FwcGxpY2F0aW9uIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbmFtZTogIuiuouWNleeuoeeQhiIsCiAgICAgICAgICBkZXNjcmlwdGlvbjogIueUqOi9puiuouWNleWFqOa1geeoi+i3n+i4quS4jueuoeeQhiIsCiAgICAgICAgICBpY29uOiAiZWwtaWNvbi1zLW9yZGVyIiwKICAgICAgICAgIHBhdGg6ICIvdmVoaWNsZS9vcmRlciIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICLlj7Dnj63lrqHmibkiLAogICAgICAgICAgZGVzY3JpcHRpb246ICLmibnph4/lj7Dnj63lrqHmibnkuI7noa7orqTlip/og70iLAogICAgICAgICAgaWNvbjogImVsLWljb24tcy1jaGVjayIsCiAgICAgICAgICBwYXRoOiAiL3ZlaGljbGUvYXBwcm92YWwiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAi57uf6K6h5YiG5p6QIiwKICAgICAgICAgIGRlc2NyaXB0aW9uOiAi6L2m6L6G5Y+w54+t57uf6K6h5LiO5aSa57u05bqm5YiG5p6QIiwKICAgICAgICAgIGljb246ICJlbC1pY29uLXMtZGF0YSIsCiAgICAgICAgICBwYXRoOiAiL3ZlaGljbGUvc3RhdGlzdGljcyIKICAgICAgICB9CiAgICAgIF0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRTdGF0aXN0aWNzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6I635Y+W57uf6K6h5pWw5o2uICovCiAgICBnZXRTdGF0aXN0aWNzKCkgewogICAgICAvLyDmqKHmi5/mlbDmja7vvIzlrp7pmYXlupTor6XosIPnlKhBUEkKICAgICAgdGhpcy5zdGF0aXN0aWNzID0gewogICAgICAgIHRvdGFsVmVoaWNsZXM6IDE1NiwKICAgICAgICBhdmFpbGFibGVWZWhpY2xlczogMTI4LAogICAgICAgIHBlbmRpbmdPcmRlcnM6IDIzLAogICAgICAgIG1haW50ZW5hbmNlVmVoaWNsZXM6IDgKICAgICAgfTsKICAgIH0sCiAgICAvKiog5a+86Iiq5Yiw5oyH5a6a6aG16Z2iICovCiAgICBuYXZpZ2F0ZVRvKHBhdGgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2gocGF0aCk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/dashboard", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <!-- 系统概览 -->\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 18px; font-weight: bold;\">机械车辆管理系统</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\">系统概览</el-button>\n          </div>\n          <div class=\"system-overview\">\n            <p style=\"font-size: 16px; color: #666; margin-bottom: 20px;\">\n              本系统是一个基于RuoYi-Cloud微服务架构的机械车辆管理平台，提供车辆信息管理、需求计划、用车申请、订单管理等全流程服务。\n            </p>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 统计卡片 -->\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #409EFF;\">\n              <i class=\"el-icon-truck\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ statistics.totalVehicles }}</div>\n              <div class=\"stat-label\">车辆总数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #67C23A;\">\n              <i class=\"el-icon-check\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ statistics.availableVehicles }}</div>\n              <div class=\"stat-label\">可用车辆</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #E6A23C;\">\n              <i class=\"el-icon-document\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ statistics.pendingOrders }}</div>\n              <div class=\"stat-label\">待处理订单</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #F56C6C;\">\n              <i class=\"el-icon-warning\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ statistics.maintenanceVehicles }}</div>\n              <div class=\"stat-label\">维修车辆</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 功能模块 -->\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 16px; font-weight: bold;\">功能模块</span>\n          </div>\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\" v-for=\"module in functionModules\" :key=\"module.name\">\n              <div class=\"function-module\" @click=\"navigateTo(module.path)\">\n                <div class=\"module-icon\">\n                  <i :class=\"module.icon\" style=\"font-size: 32px; color: #409EFF;\"></i>\n                </div>\n                <div class=\"module-content\">\n                  <h3>{{ module.name }}</h3>\n                  <p>{{ module.description }}</p>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 系统特性 -->\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 16px; font-weight: bold;\">系统特性</span>\n          </div>\n          <ul class=\"feature-list\">\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 车辆信息全生命周期管理</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 多级审批流程支持</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 实时消息通知（钉钉集成）</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 台班统计与分析</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 违章记录管理</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 维修记录跟踪</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 批量审批功能</li>\n            <li><i class=\"el-icon-check\" style=\"color: #67C23A;\"></i> 数据导入导出</li>\n          </ul>\n        </el-card>\n      </el-col>\n      <!-- 技术架构 -->\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 16px; font-weight: bold;\">技术架构</span>\n          </div>\n          <ul class=\"tech-list\">\n            <li><strong>后端框架：</strong>Spring Boot + Spring Cloud</li>\n            <li><strong>前端框架：</strong>Vue.js + Element UI</li>\n            <li><strong>数据库：</strong>MySQL</li>\n            <li><strong>缓存：</strong>Redis</li>\n            <li><strong>注册中心：</strong>Nacos</li>\n            <li><strong>网关：</strong>Spring Cloud Gateway</li>\n            <li><strong>认证：</strong>Spring Security + JWT</li>\n            <li><strong>消息通知：</strong>钉钉API</li>\n          </ul>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 业务流程 -->\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 16px; font-weight: bold;\">主要业务流程</span>\n          </div>\n          <el-steps :active=\"4\" align-center>\n            <el-step title=\"需求计划\" description=\"队伍提交车辆需求计划\"></el-step>\n            <el-step title=\"审批流程\" description=\"项目调度室→机械主管→经营部门\"></el-step>\n            <el-step title=\"用车申请\" description=\"队伍提交具体用车申请\"></el-step>\n            <el-step title=\"调度安排\" description=\"调度室分配车辆和司机\"></el-step>\n            <el-step title=\"订单执行\" description=\"司机执行用车任务\"></el-step>\n            <el-step title=\"台班确认\" description=\"多级确认完成台班\"></el-step>\n          </el-steps>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"VehicleDashboard\",\n  data() {\n    return {\n      // 统计数据\n      statistics: {\n        totalVehicles: 0,\n        availableVehicles: 0,\n        pendingOrders: 0,\n        maintenanceVehicles: 0\n      },\n      // 功能模块\n      functionModules: [\n        {\n          name: \"车辆信息管理\",\n          description: \"管理机械车辆基本信息、违章记录、维修记录\",\n          icon: \"el-icon-truck\",\n          path: \"/vehicle/info\"\n        },\n        {\n          name: \"需求计划管理\",\n          description: \"车辆需求计划申请与审批流程管理\",\n          icon: \"el-icon-document-add\",\n          path: \"/vehicle/demand\"\n        },\n        {\n          name: \"用车申请管理\",\n          description: \"机械用车申请与调度安排管理\",\n          icon: \"el-icon-edit-outline\",\n          path: \"/vehicle/application\"\n        },\n        {\n          name: \"订单管理\",\n          description: \"用车订单全流程跟踪与管理\",\n          icon: \"el-icon-s-order\",\n          path: \"/vehicle/order\"\n        },\n        {\n          name: \"台班审批\",\n          description: \"批量台班审批与确认功能\",\n          icon: \"el-icon-s-check\",\n          path: \"/vehicle/approval\"\n        },\n        {\n          name: \"统计分析\",\n          description: \"车辆台班统计与多维度分析\",\n          icon: \"el-icon-s-data\",\n          path: \"/vehicle/statistics\"\n        }\n      ]\n    };\n  },\n  created() {\n    this.getStatistics();\n  },\n  methods: {\n    /** 获取统计数据 */\n    getStatistics() {\n      // 模拟数据，实际应该调用API\n      this.statistics = {\n        totalVehicles: 156,\n        availableVehicles: 128,\n        pendingOrders: 23,\n        maintenanceVehicles: 8\n      };\n    },\n    /** 导航到指定页面 */\n    navigateTo(path) {\n      this.$router.push(path);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.stat-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 8px;\n}\n\n.function-module {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  border: 1px solid #EBEEF5;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.function-module:hover {\n  border-color: #409EFF;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.module-icon {\n  margin-right: 20px;\n}\n\n.module-content h3 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.module-content p {\n  margin: 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.feature-list, .tech-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.feature-list li, .tech-list li {\n  padding: 8px 0;\n  border-bottom: 1px solid #F5F7FA;\n}\n\n.feature-list li:last-child, .tech-list li:last-child {\n  border-bottom: none;\n}\n\n.feature-list i {\n  margin-right: 8px;\n}\n</style>\n"]}]}