version: '3.3'

networks:
  ruoyi-network:
    driver: bridge
    external: false

services:
  ruoyi-mysql:
    container_name: ruoyi-mysql
    image: mysql:5.7
    restart: always
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ry-cloud}
      MYSQL_USER: ruoyi
      MYSQL_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      TZ: Asia/Shanghai
    volumes:
      - ${DATA_ROOT:-/home}/mysql/data:/var/lib/mysql
      - ${DATA_ROOT:-/home}/mysql/logs:/var/log/mysql
      - ${DATA_ROOT:-/home}/mysql/conf/my.cnf:/etc/mysql/conf.d/my.cnf
      - ${DEPLOY_ROOT:-/erdCloud}/sql:/docker-entrypoint-initdb.d
    command: [
      'mysqld',
      '--character-set-server=utf8mb4',
      '--collation-server=utf8mb4_unicode_ci',
      '--default-time-zone=+8:00',
      '--lower-case-table-names=1',
      '--innodb-buffer-pool-size=256M',
      '--max-connections=1000'
    ]
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_MYSQL:-512m}
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-RuoYi@2024}"]
      interval: 30s
      timeout: 10s
      retries: 3

  ruoyi-redis:
    container_name: ruoyi-redis
    image: redis:7.0-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ${DATA_ROOT:-/home}/redis/data:/data
      - ${DATA_ROOT:-/home}/redis/conf/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_REDIS:-256m}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  ruoyi-nacos:
    container_name: ruoyi-nacos
    image: nacos/nacos-server:v2.2.3
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: ruoyi-mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: ${MYSQL_DATABASE_NACOS:-nacos}
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: ${MYSQL_ROOT_PASSWORD:-RuoYi@2024}
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC
      JVM_XMS: 256m
      JVM_XMX: 512m
      TZ: Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    volumes:
      - ${DATA_ROOT:-/home}/nacos/data:/home/<USER>/data
      - ${DATA_ROOT:-/home}/nacos/logs:/home/<USER>/logs
      - ${DATA_ROOT:-/home}/nacos/conf/application.properties:/home/<USER>/conf/application.properties
    depends_on:
      - ruoyi-mysql
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_NACOS:-1g}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      interval: 30s
      timeout: 10s
      retries: 5

  ruoyi-dameng:
    container_name: ruoyi-dameng
    image: laglangyue/dmdb8:latest
    restart: always
    ports:
      - "${DAMENG_PORT_NUM:-5236}:5236"
    environment:
      # 达梦数据库环境变量
      INSTANCE_NAME: ${DAMENG_INSTANCE_NAME:-dm8_test}
      DB_SERVER_NAME: ${DAMENG_INSTANCE_NAME:-dm8_test}
      SYSDBA_PWD: ${DAMENG_SYSDBA_PASSWORD:-SYSDBA001}
      # 数据库初始化参数
      PAGE_SIZE: 16
      EXTENT_SIZE: 16
      CASE_SENSITIVE: N
      CHARSET: 1
      LENGTH_IN_CHAR: Y
      # 时区设置
      TZ: Asia/Shanghai
    volumes:
      # 数据目录 - 直接使用主机目录
      - ${DATA_ROOT:-/home}/dameng/data:/opt/dmdbms/data/DAMENG
      # 日志目录
      - ${DATA_ROOT:-/home}/dameng/log:/opt/dmdbms/log
      # 配置目录
      - ${DATA_ROOT:-/home}/dameng/etc:/opt/dmdbms/etc
      # 备份目录
      - ${DATA_ROOT:-/home}/dameng/backup:/opt/dmdbms/backup
      # 临时目录
      - ${DATA_ROOT:-/home}/dameng/temp:/opt/dmdbms/temp
      # 初始化脚本
      - ${DATA_ROOT:-/home}/dameng/init_dm.sh:/opt/dmdbms/init_dm.sh
      # 初始化SQL脚本
      - ${DEPLOY_ROOT:-/erdCloud}/sql:/docker-entrypoint-initdb.d
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_DAMENG:-1g}
        reservations:
          memory: 512m
    # 达梦数据库健康检查
    healthcheck:
      test: ["CMD-SHELL", "timeout 5 bash -c '</dev/tcp/localhost/5236' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    # 共享内存设置（达梦数据库需要）
    shm_size: 1g
    # 特权模式（某些达梦版本可能需要）
    privileged: false
    # 安全选项
    security_opt:
      - seccomp:unconfined
    # 使用自定义初始化脚本
    entrypoint: ["/bin/bash", "/opt/dmdbms/init_dm.sh"]

  ruoyi-nginx:
    container_name: ruoyi-nginx
    image: nginx:1.24-alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ${DEPLOY_ROOT:-/erdCloud}/nginx/html:/usr/share/nginx/html
      - ${DEPLOY_ROOT:-/erdCloud}/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ${DEPLOY_ROOT:-/erdCloud}/nginx/conf.d:/etc/nginx/conf.d
      - ${DEPLOY_ROOT:-/erdCloud}/nginx/logs:/var/log/nginx
      - ${DEPLOY_ROOT:-/erdCloud}/nginx/ssl:/etc/nginx/ssl
    networks:
      - ruoyi-network
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMITS_NGINX:-128m}
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
