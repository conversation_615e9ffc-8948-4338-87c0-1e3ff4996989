{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\index.vue", "mtime": 1754147167174}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RPcmRlciwgZ2V0T3JkZXIsIHRlYW1Db25maXJtT3JkZXIsIGRpc3BhdGNoQ29uZmlybU9yZGVyLCBtYW5hZ2VyQ29uZmlybU9yZGVyLCByZWplY3RPcmRlciwgY2FsY3VsYXRlT3JkZXJDb3N0LCBiYXRjaENvbmZpcm1PcmRlcnMgfSBmcm9tICJAL2FwaS92ZWhpY2xlL29yZGVyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVmVoaWNsZU9yZGVyIiwKICBkaWN0czogWyd2ZWhpY2xlX3R5cGUnLCAnb3JkZXJfc3RhdHVzJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDorqLljZXooajmoLzmlbDmja4KICAgICAgb3JkZXJMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDor6bmg4XlvLnlh7rlsYIKICAgICAgZGV0YWlsT3BlbjogZmFsc2UsCiAgICAgIC8vIOmAgOWbnuW8ueWHuuWxggogICAgICByZWplY3RPcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgb3JkZXJOdW1iZXI6IG51bGwsCiAgICAgICAgdmVoaWNsZVR5cGU6IG51bGwsCiAgICAgICAgb3JkZXJTdGF0dXM6IG51bGwsCiAgICAgICAgdGVhbU5hbWU6IG51bGwKICAgICAgfSwKICAgICAgLy8g6K6i5Y2V6K+m5oOFCiAgICAgIG9yZGVyRGV0YWlsOiB7fSwKICAgICAgLy8g6YCA5Zue6KGo5Y2VCiAgICAgIHJlamVjdEZvcm06IHsKICAgICAgICBvcmRlcklkOiBudWxsLAogICAgICAgIHJlamVjdFJlYXNvbjogbnVsbAogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouiuouWNleWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdE9yZGVyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMub3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vcmRlcklkKQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAvKiog5p+l55yL6K+m5oOFICovCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLm9yZGVyRGV0YWlsID0gcm93OwogICAgICB0aGlzLmRldGFpbE9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDpmJ/kvI3noa7orqQgKi8KICAgIGhhbmRsZVRlYW1Db25maXJtKHJvdykgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTor6XorqLljZXvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiB0ZWFtQ29uZmlybU9yZGVyKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi56Gu6K6k5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog6LCD5bqm56Gu6K6kICovCiAgICBoYW5kbGVEaXNwYXRjaENvbmZpcm0ocm93KSB7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOivpeiuouWNle+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRpc3BhdGNoQ29uZmlybU9yZGVyKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi56Gu6K6k5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5Li7566h56Gu6K6kICovCiAgICBoYW5kbGVNYW5hZ2VyQ29uZmlybShyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k6K+l6K6i5Y2V77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gbWFuYWdlckNvbmZpcm1PcmRlcihyb3cub3JkZXJJZCk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuehruiupOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOiuoeeul+i0ueeUqCAqLwogICAgaGFuZGxlQ2FsY3VsYXRlQ29zdChyb3cpIHsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm6K6h566X6K+l6K6i5Y2V6LS555So77yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gY2FsY3VsYXRlT3JkZXJDb3N0KHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6LS555So6K6h566X5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog6YCA5Zue6K6i5Y2VICovCiAgICBoYW5kbGVSZWplY3Qocm93KSB7CiAgICAgIHRoaXMucmVqZWN0Rm9ybS5vcmRlcklkID0gcm93Lm9yZGVySWQ7CiAgICAgIHRoaXMucmVqZWN0Rm9ybS5yZWplY3RSZWFzb24gPSBudWxsOwogICAgICB0aGlzLnJlamVjdE9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDmj5DkuqTpgIDlm54gKi8KICAgIHN1Ym1pdFJlamVjdCgpIHsKICAgICAgcmVqZWN0T3JkZXIodGhpcy5yZWplY3RGb3JtLm9yZGVySWQsIHRoaXMucmVqZWN0Rm9ybS5yZWplY3RSZWFzb24pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMucmVqZWN0T3BlbiA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIumAgOWbnuaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5om56YeP56Gu6K6kICovCiAgICBoYW5kbGVCYXRjaENvbmZpcm0oKSB7CiAgICAgIGNvbnN0IG9yZGVySWRzID0gdGhpcy5pZHM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOmAieS4reeahOiuouWNle+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGJhdGNoQ29uZmlybU9yZGVycyhvcmRlcklkcywgInRlYW0iKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5om56YeP56Gu6K6k5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ3ZlaGljbGUvb3JkZXIvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYG9yZGVyXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"订单编号\" prop=\"orderNumber\">\n        <el-input\n          v-model=\"queryParams.orderNumber\"\n          placeholder=\"请输入订单编号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n        <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择订单状态\" clearable>\n          <el-option label=\"待开始\" value=\"pending\"></el-option>\n          <el-option label=\"进行中\" value=\"running\"></el-option>\n          <el-option label=\"司机已结束\" value=\"driver_finished\"></el-option>\n          <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n          <el-option label=\"调度已确认\" value=\"dispatch_confirmed\"></el-option>\n          <el-option label=\"已完成\" value=\"completed\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"队伍名称\" prop=\"teamName\">\n        <el-input\n          v-model=\"queryParams.teamName\"\n          placeholder=\"请输入队伍名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleBatchConfirm\"\n          v-hasPermi=\"['vehicle:order:confirm']\"\n        >批量确认</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:order:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"orderNumber\" />\n      <el-table-column label=\"申请标题\" align=\"center\" prop=\"applicationTitle\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" prop=\"vehicleInfo\" />\n      <el-table-column label=\"队伍名称\" align=\"center\" prop=\"teamName\" />\n      <el-table-column label=\"司机姓名\" align=\"center\" prop=\"driverName\" />\n      <el-table-column label=\"使用地点\" align=\"center\" prop=\"usageLocation\" />\n      <el-table-column label=\"计划时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.plannedStartTime, '{y}-{m}-{d} {h}:{i}') }}</span><br/>\n          <span>{{ parseTime(scope.row.plannedEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.order_status\" :value=\"scope.row.orderStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"费用状态\" align=\"center\" prop=\"costStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.costStatus === 'calculated'\" type=\"warning\">已计算</el-tag>\n          <el-tag v-else-if=\"scope.row.costStatus === 'confirmed'\" type=\"success\">已确认</el-tag>\n          <el-tag v-else type=\"info\">未计算</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalCost\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['vehicle:order:query']\"\n          >详情</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'driver_finished'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleTeamConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >队伍确认</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleDispatchConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >调度确认</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'dispatch_confirmed'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleManagerConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >主管确认</el-button>\n          <el-button\n            v-if=\"scope.row.costStatus !== 'confirmed' && ['driver_finished', 'completed'].includes(scope.row.orderStatus)\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-money\"\n            @click=\"handleCalculateCost(scope.row)\"\n            v-hasPermi=\"['vehicle:order:calculate']\"\n          >计算费用</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-close\"\n            @click=\"handleReject(scope.row)\"\n            v-hasPermi=\"['vehicle:order:reject']\"\n          >退回</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单编号\">{{ orderDetail.orderNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请标题\">{{ orderDetail.applicationTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">{{ orderDetail.vehicleInfo }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍名称\">{{ orderDetail.teamName }}</el-descriptions-item>\n        <el-descriptions-item label=\"司机姓名\">{{ orderDetail.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"使用地点\">{{ orderDetail.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划开始时间\">{{ parseTime(orderDetail.plannedStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划结束时间\">{{ parseTime(orderDetail.plannedEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(orderDetail.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(orderDetail.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <dict-tag :options=\"dict.type.order_status\" :value=\"orderDetail.orderStatus\"/>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用状态\">\n          <el-tag v-if=\"orderDetail.costStatus === 'calculated'\" type=\"warning\">已计算</el-tag>\n          <el-tag v-else-if=\"orderDetail.costStatus === 'confirmed'\" type=\"success\">已确认</el-tag>\n          <el-tag v-else type=\"info\">未计算</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"总费用\">{{ orderDetail.totalCost }}</el-descriptions-item>\n        <el-descriptions-item label=\"费用承担方\">{{ orderDetail.costBearer }}</el-descriptions-item>\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ orderDetail.remark }}</el-descriptions-item>\n      </el-descriptions>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 退回订单对话框 -->\n    <el-dialog title=\"退回订单\" :visible.sync=\"rejectOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"80px\">\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" placeholder=\"请输入退回原因\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrder, getOrder, teamConfirmOrder, dispatchConfirmOrder, managerConfirmOrder, rejectOrder, calculateOrderCost, batchConfirmOrders } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"VehicleOrder\",\n  dicts: ['vehicle_type', 'order_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 订单表格数据\n      orderList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 详情弹出层\n      detailOpen: false,\n      // 退回弹出层\n      rejectOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderNumber: null,\n        vehicleType: null,\n        orderStatus: null,\n        teamName: null\n      },\n      // 订单详情\n      orderDetail: {},\n      // 退回表单\n      rejectForm: {\n        orderId: null,\n        rejectReason: null\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      listOrder(this.queryParams).then(response => {\n        this.orderList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    /** 查看详情 */\n    handleView(row) {\n      this.orderDetail = row;\n      this.detailOpen = true;\n    },\n    /** 队伍确认 */\n    handleTeamConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return teamConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 调度确认 */\n    handleDispatchConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return dispatchConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 主管确认 */\n    handleManagerConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return managerConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 计算费用 */\n    handleCalculateCost(row) {\n      this.$modal.confirm('是否计算该订单费用？').then(function() {\n        return calculateOrderCost(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"费用计算成功\");\n      }).catch(() => {});\n    },\n    /** 退回订单 */\n    handleReject(row) {\n      this.rejectForm.orderId = row.orderId;\n      this.rejectForm.rejectReason = null;\n      this.rejectOpen = true;\n    },\n    /** 提交退回 */\n    submitReject() {\n      rejectOrder(this.rejectForm.orderId, this.rejectForm.rejectReason).then(() => {\n        this.getList();\n        this.rejectOpen = false;\n        this.$modal.msgSuccess(\"退回成功\");\n      });\n    },\n    /** 批量确认 */\n    handleBatchConfirm() {\n      const orderIds = this.ids;\n      this.$modal.confirm('是否确认选中的订单？').then(function() {\n        return batchConfirmOrders(orderIds, \"team\");\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"批量确认成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/order/export', {\n        ...this.queryParams\n      }, `order_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"]}]}