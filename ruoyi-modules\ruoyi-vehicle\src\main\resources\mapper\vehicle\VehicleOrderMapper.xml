<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleOrderMapper">
    
    <resultMap type="VehicleOrder" id="VehicleOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="applicationId"    column="application_id"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="driverName"    column="driver_name"    />
        <result property="usageLocation"    column="usage_location"    />
        <result property="plannedStartTime"    column="planned_start_time"    />
        <result property="plannedEndTime"    column="planned_end_time"    />
        <result property="actualStartTime"    column="actual_start_time"    />
        <result property="actualEndTime"    column="actual_end_time"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="startPhotoUrl"    column="start_photo_url"    />
        <result property="endPhotoUrl"    column="end_photo_url"    />
        <result property="teamConfirmTime"    column="team_confirm_time"    />
        <result property="teamConfirmPerson"    column="team_confirm_person"    />
        <result property="dispatchConfirmTime"    column="dispatch_confirm_time"    />
        <result property="dispatchConfirmPerson"    column="dispatch_confirm_person"    />
        <result property="managerConfirmTime"    column="manager_confirm_time"    />
        <result property="managerConfirmPerson"    column="manager_confirm_person"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="vehicleWeight"    column="vehicle_weight"    />
        <result property="costUnit"    column="cost_unit"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="actualDuration"    column="actual_duration"    />
        <result property="totalCost"    column="total_cost"    />
        <result property="costBearer"    column="cost_bearer"    />
        <result property="costStatus"    column="cost_status"    />
        <result property="costCalculateTime"    column="cost_calculate_time"    />
        <result property="costConfirmTime"    column="cost_confirm_time"    />
        <result property="costConfirmPerson"    column="cost_confirm_person"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleOrderVo">
        select order_id, application_id, vehicle_id, team_id, driver_name, usage_location, 
               planned_start_time, planned_end_time, actual_start_time, actual_end_time, 
               order_status, start_photo_url, end_photo_url, team_confirm_time, team_confirm_person, 
               dispatch_confirm_time, dispatch_confirm_person, manager_confirm_time, manager_confirm_person, 
               reject_reason, vehicle_weight, cost_unit, unit_price, actual_duration, total_cost, 
               cost_bearer, cost_status, cost_calculate_time, cost_confirm_time, cost_confirm_person, 
               create_by, create_time, update_by, update_time, remark 
        from vehicle_order
    </sql>

    <select id="selectVehicleOrderList" parameterType="VehicleOrder" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        <where>  
            <if test="applicationId != null "> and application_id = #{applicationId}</if>
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="usageLocation != null  and usageLocation != ''"> and usage_location like concat('%', #{usageLocation}, '%')</if>
            <if test="plannedStartTime != null "> and planned_start_time &gt;= #{plannedStartTime}</if>
            <if test="plannedEndTime != null "> and planned_end_time &lt;= #{plannedEndTime}</if>
            <if test="actualStartTime != null "> and actual_start_time &gt;= #{actualStartTime}</if>
            <if test="actualEndTime != null "> and actual_end_time &lt;= #{actualEndTime}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="costStatus != null  and costStatus != ''"> and cost_status = #{costStatus}</if>
            <if test="costBearer != null  and costBearer != ''"> and cost_bearer = #{costBearer}</if>
            <if test="params.beginPlannedStartTime != null and params.beginPlannedStartTime != ''"><!-- 开始时间检索 -->
                and date_format(planned_start_time,'%y%m%d') &gt;= date_format(#{params.beginPlannedStartTime},'%y%m%d')
            </if>
            <if test="params.endPlannedStartTime != null and params.endPlannedStartTime != ''"><!-- 结束时间检索 -->
                and date_format(planned_start_time,'%y%m%d') &lt;= date_format(#{params.endPlannedStartTime},'%y%m%d')
            </if>
            <if test="params.beginActualEndTime != null and params.beginActualEndTime != ''"><!-- 实际结束时间检索 -->
                and date_format(actual_end_time,'%y%m%d') &gt;= date_format(#{params.beginActualEndTime},'%y%m%d')
            </if>
            <if test="params.endActualEndTime != null and params.endActualEndTime != ''"><!-- 实际结束时间检索 -->
                and date_format(actual_end_time,'%y%m%d') &lt;= date_format(#{params.endActualEndTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleOrderByOrderId" parameterType="Long" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectVehicleOrderByStatus" parameterType="String" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        where order_status = #{orderStatus}
        order by create_time desc
    </select>

    <select id="selectVehicleOrderByVehicleId" parameterType="Long" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        where vehicle_id = #{vehicleId}
        order by create_time desc
    </select>

    <select id="selectVehicleOrderByTeamId" parameterType="Long" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        where team_id = #{teamId}
        order by create_time desc
    </select>

    <select id="selectVehicleOrderByApplicationId" parameterType="Long" resultMap="VehicleOrderResult">
        <include refid="selectVehicleOrderVo"/>
        where application_id = #{applicationId}
    </select>
        
    <insert id="insertVehicleOrder" parameterType="VehicleOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into vehicle_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationId != null">application_id,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="usageLocation != null and usageLocation != ''">usage_location,</if>
            <if test="plannedStartTime != null">planned_start_time,</if>
            <if test="plannedEndTime != null">planned_end_time,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="orderStatus != null and orderStatus != ''">order_status,</if>
            <if test="startPhotoUrl != null">start_photo_url,</if>
            <if test="endPhotoUrl != null">end_photo_url,</if>
            <if test="teamConfirmTime != null">team_confirm_time,</if>
            <if test="teamConfirmPerson != null">team_confirm_person,</if>
            <if test="dispatchConfirmTime != null">dispatch_confirm_time,</if>
            <if test="dispatchConfirmPerson != null">dispatch_confirm_person,</if>
            <if test="managerConfirmTime != null">manager_confirm_time,</if>
            <if test="managerConfirmPerson != null">manager_confirm_person,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="vehicleWeight != null">vehicle_weight,</if>
            <if test="costUnit != null">cost_unit,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="actualDuration != null">actual_duration,</if>
            <if test="totalCost != null">total_cost,</if>
            <if test="costBearer != null">cost_bearer,</if>
            <if test="costStatus != null">cost_status,</if>
            <if test="costCalculateTime != null">cost_calculate_time,</if>
            <if test="costConfirmTime != null">cost_confirm_time,</if>
            <if test="costConfirmPerson != null">cost_confirm_person,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationId != null">#{applicationId},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="usageLocation != null and usageLocation != ''">#{usageLocation},</if>
            <if test="plannedStartTime != null">#{plannedStartTime},</if>
            <if test="plannedEndTime != null">#{plannedEndTime},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="orderStatus != null and orderStatus != ''">#{orderStatus},</if>
            <if test="startPhotoUrl != null">#{startPhotoUrl},</if>
            <if test="endPhotoUrl != null">#{endPhotoUrl},</if>
            <if test="teamConfirmTime != null">#{teamConfirmTime},</if>
            <if test="teamConfirmPerson != null">#{teamConfirmPerson},</if>
            <if test="dispatchConfirmTime != null">#{dispatchConfirmTime},</if>
            <if test="dispatchConfirmPerson != null">#{dispatchConfirmPerson},</if>
            <if test="managerConfirmTime != null">#{managerConfirmTime},</if>
            <if test="managerConfirmPerson != null">#{managerConfirmPerson},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="vehicleWeight != null">#{vehicleWeight},</if>
            <if test="costUnit != null">#{costUnit},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="actualDuration != null">#{actualDuration},</if>
            <if test="totalCost != null">#{totalCost},</if>
            <if test="costBearer != null">#{costBearer},</if>
            <if test="costStatus != null">#{costStatus},</if>
            <if test="costCalculateTime != null">#{costCalculateTime},</if>
            <if test="costConfirmTime != null">#{costConfirmTime},</if>
            <if test="costConfirmPerson != null">#{costConfirmPerson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleOrder" parameterType="VehicleOrder">
        update vehicle_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationId != null">application_id = #{applicationId},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="usageLocation != null and usageLocation != ''">usage_location = #{usageLocation},</if>
            <if test="plannedStartTime != null">planned_start_time = #{plannedStartTime},</if>
            <if test="plannedEndTime != null">planned_end_time = #{plannedEndTime},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="orderStatus != null and orderStatus != ''">order_status = #{orderStatus},</if>
            <if test="startPhotoUrl != null">start_photo_url = #{startPhotoUrl},</if>
            <if test="endPhotoUrl != null">end_photo_url = #{endPhotoUrl},</if>
            <if test="teamConfirmTime != null">team_confirm_time = #{teamConfirmTime},</if>
            <if test="teamConfirmPerson != null">team_confirm_person = #{teamConfirmPerson},</if>
            <if test="dispatchConfirmTime != null">dispatch_confirm_time = #{dispatchConfirmTime},</if>
            <if test="dispatchConfirmPerson != null">dispatch_confirm_person = #{dispatchConfirmPerson},</if>
            <if test="managerConfirmTime != null">manager_confirm_time = #{managerConfirmTime},</if>
            <if test="managerConfirmPerson != null">manager_confirm_person = #{managerConfirmPerson},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="vehicleWeight != null">vehicle_weight = #{vehicleWeight},</if>
            <if test="costUnit != null">cost_unit = #{costUnit},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="actualDuration != null">actual_duration = #{actualDuration},</if>
            <if test="totalCost != null">total_cost = #{totalCost},</if>
            <if test="costBearer != null">cost_bearer = #{costBearer},</if>
            <if test="costStatus != null">cost_status = #{costStatus},</if>
            <if test="costCalculateTime != null">cost_calculate_time = #{costCalculateTime},</if>
            <if test="costConfirmTime != null">cost_confirm_time = #{costConfirmTime},</if>
            <if test="costConfirmPerson != null">cost_confirm_person = #{costConfirmPerson},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteVehicleOrderByOrderId" parameterType="Long">
        delete from vehicle_order where order_id = #{orderId}
    </delete>

    <delete id="deleteVehicleOrderByOrderIds" parameterType="String">
        delete from vehicle_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>
