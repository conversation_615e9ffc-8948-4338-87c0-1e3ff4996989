{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\approve.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\demand\\approve.vue", "mtime": 1754142746752}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demand", "require", "name", "data", "form", "approvalForm", "approvalStatus", "approvalComments", "submitLoading", "currentStep", "canCurrentUserApprove", "approvalHistory", "approvalRules", "required", "message", "trigger", "min", "max", "created", "planId", "$route", "params", "loadPlanDetail", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "p", "n", "<PERSON><PERSON><PERSON><PERSON>", "v", "updateCurrentStep", "checkApprovalPermission", "$modal", "msgError", "goBack", "a", "statusStepMap", "currentStatus", "includes", "submitApproval", "_this2", "$refs", "validate", "valid", "<PERSON><PERSON><PERSON><PERSON>", "then", "msgSuccess", "catch", "resetApprovalForm", "resetFields", "getStepDescription", "status", "currentStepIndex", "targetStepIndex", "getStatusTagType", "statusMap", "getStatusText", "$router", "push"], "sources": ["src/views/vehicle/demand/approve.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">需求计划审批</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n      \n      <!-- 申请信息展示 -->\n      <el-descriptions title=\"申请信息\" :column=\"2\" border>\n        <el-descriptions-item label=\"计划标题\" :span=\"2\">{{ form.planTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆类型\">{{ form.vehicleType }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆型号\">{{ form.vehicleModel }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求单位\">{{ form.demandUnit }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求数量\">{{ form.demandQuantity }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求开始时间\">{{ parseTime(form.demandStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"需求结束时间\">{{ parseTime(form.demandEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请人\">{{ form.applicant }}</el-descriptions-item>\n        <el-descriptions-item label=\"联系电话\">{{ form.applicantPhone }}</el-descriptions-item>\n        <el-descriptions-item label=\"当前状态\">\n          <el-tag :type=\"getStatusTagType(form.approvalStatus)\">\n            {{ getStatusText(form.approvalStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"申请时间\">{{ parseTime(form.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用途说明\" :span=\"2\">{{ form.usagePurpose }}</el-descriptions-item>\n        <el-descriptions-item v-if=\"form.remark\" label=\"备注\" :span=\"2\">{{ form.remark }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 审批流程展示 -->\n      <div style=\"margin-top: 20px;\">\n        <h3>审批流程</h3>\n        <el-steps :active=\"currentStep\" finish-status=\"success\" align-center>\n          <el-step title=\"项目调度室审批\" :description=\"getStepDescription('pending_level1')\"></el-step>\n          <el-step title=\"机械主管审批\" :description=\"getStepDescription('pending_level2')\"></el-step>\n          <el-step title=\"经营部门审批\" :description=\"getStepDescription('pending_level3')\"></el-step>\n          <el-step title=\"审批完成\" :description=\"getStepDescription('approved')\"></el-step>\n        </el-steps>\n      </div>\n\n      <!-- 审批操作 -->\n      <div v-if=\"canCurrentUserApprove\" style=\"margin-top: 30px;\">\n        <el-divider content-position=\"left\">审批操作</el-divider>\n        <el-form ref=\"approvalForm\" :model=\"approvalForm\" :rules=\"approvalRules\" label-width=\"100px\">\n          <el-form-item label=\"审批结果\" prop=\"approvalStatus\">\n            <el-radio-group v-model=\"approvalForm.approvalStatus\">\n              <el-radio label=\"approved\">通过</el-radio>\n              <el-radio label=\"rejected\">拒绝</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"审批意见\" prop=\"approvalComments\">\n            <el-input\n              v-model=\"approvalForm.approvalComments\"\n              type=\"textarea\"\n              :rows=\"4\"\n              placeholder=\"请输入审批意见\"\n              maxlength=\"500\"\n              show-word-limit />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"submitApproval\" :loading=\"submitLoading\">\n              <i class=\"el-icon-check\"></i> 提交审批\n            </el-button>\n            <el-button @click=\"resetApprovalForm\">\n              <i class=\"el-icon-refresh-left\"></i> 重置\n            </el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <!-- 审批历史 -->\n      <div v-if=\"approvalHistory.length > 0\" style=\"margin-top: 30px;\">\n        <el-divider content-position=\"left\">审批历史</el-divider>\n        <el-timeline>\n          <el-timeline-item\n            v-for=\"(item, index) in approvalHistory\"\n            :key=\"index\"\n            :timestamp=\"parseTime(item.approvalTime)\"\n            placement=\"top\">\n            <el-card>\n              <h4>{{ item.approverName }} - {{ item.approvalLevel }}</h4>\n              <p>审批结果：\n                <el-tag :type=\"item.approvalStatus === 'approved' ? 'success' : 'danger'\" size=\"mini\">\n                  {{ item.approvalStatus === 'approved' ? '通过' : '拒绝' }}\n                </el-tag>\n              </p>\n              <p v-if=\"item.approvalComments\">审批意见：{{ item.approvalComments }}</p>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { getDemand, approveDemand } from \"@/api/vehicle/demand\";\n\nexport default {\n  name: \"DemandApprove\",\n  data() {\n    return {\n      // 申请信息\n      form: {},\n      // 审批表单\n      approvalForm: {\n        approvalStatus: 'approved',\n        approvalComments: ''\n      },\n      // 提交状态\n      submitLoading: false,\n      // 当前步骤\n      currentStep: 0,\n      // 是否可以审批\n      canCurrentUserApprove: false,\n      // 审批历史\n      approvalHistory: [],\n      // 审批表单验证规则\n      approvalRules: {\n        approvalStatus: [\n          { required: true, message: \"请选择审批结果\", trigger: \"change\" }\n        ],\n        approvalComments: [\n          { required: true, message: \"审批意见不能为空\", trigger: \"blur\" },\n          { min: 5, max: 500, message: \"长度在 5 到 500 个字符\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    const planId = this.$route.params.planId;\n    if (planId) {\n      this.loadPlanDetail(planId);\n    }\n  },\n  methods: {\n    /** 加载计划详情 */\n    async loadPlanDetail(planId) {\n      try {\n        const response = await getDemand(planId);\n        this.form = response.data;\n        this.updateCurrentStep();\n        this.checkApprovalPermission();\n        // TODO: 加载审批历史\n        // this.loadApprovalHistory(planId);\n      } catch (error) {\n        this.$modal.msgError(\"加载计划详情失败\");\n        this.goBack();\n      }\n    },\n    \n    /** 更新当前步骤 */\n    updateCurrentStep() {\n      const statusStepMap = {\n        'pending_level1': 0,\n        'pending_level2': 1,\n        'pending_level3': 2,\n        'approved': 3,\n        'rejected': -1\n      };\n      this.currentStep = statusStepMap[this.form.approvalStatus] || 0;\n    },\n    \n    /** 检查审批权限 */\n    checkApprovalPermission() {\n      // TODO: 根据当前用户角色和审批状态判断是否可以审批\n      // 这里简化处理，实际应该根据用户角色判断\n      const currentStatus = this.form.approvalStatus;\n      this.canCurrentUserApprove = ['pending_level1', 'pending_level2', 'pending_level3'].includes(currentStatus);\n    },\n    \n    /** 提交审批 */\n    submitApproval() {\n      this.$refs[\"approvalForm\"].validate(valid => {\n        if (valid) {\n          this.submitLoading = true;\n          const planId = this.$route.params.planId;\n          \n          approveDemand(planId, this.approvalForm).then(response => {\n            this.$modal.msgSuccess(\"审批提交成功\");\n            this.goBack();\n          }).catch(() => {\n            this.submitLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 重置审批表单 */\n    resetApprovalForm() {\n      this.$refs[\"approvalForm\"].resetFields();\n    },\n    \n    /** 获取步骤描述 */\n    getStepDescription(status) {\n      if (this.form.approvalStatus === 'rejected') {\n        return '审批被拒绝';\n      }\n      \n      const statusStepMap = {\n        'pending_level1': 0,\n        'pending_level2': 1,\n        'pending_level3': 2,\n        'approved': 3\n      };\n      \n      const currentStepIndex = statusStepMap[this.form.approvalStatus];\n      const targetStepIndex = statusStepMap[status];\n      \n      if (targetStepIndex < currentStepIndex) {\n        return '已完成';\n      } else if (targetStepIndex === currentStepIndex) {\n        return '进行中';\n      } else {\n        return '待处理';\n      }\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'draft': 'info',\n        'pending_level1': 'warning',\n        'pending_level2': 'warning', \n        'pending_level3': 'warning',\n        'approved': 'success',\n        'rejected': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'draft': '草稿',\n        'pending_level1': '待项目调度室审批',\n        'pending_level2': '待机械主管审批',\n        'pending_level3': '待经营部门审批',\n        'approved': '已通过',\n        'rejected': '已拒绝'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/demand');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.el-descriptions {\n  margin-bottom: 20px;\n}\n\n.el-steps {\n  margin: 20px 0;\n}\n\n.el-timeline {\n  padding-left: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;AAgGA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;MACA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;MACA;MACAC,qBAAA;MACA;MACAC,eAAA;MACA;MACAC,aAAA;QACAN,cAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,gBAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAF,MAAA;IACA,IAAAA,MAAA;MACA,KAAAG,cAAA,CAAAH,MAAA;IACA;EACA;EACAI,OAAA;IACA,aACAD,cAAA,WAAAA,eAAAH,MAAA;MAAA,IAAAK,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,iBAAA,EAAAjB,MAAA;YAAA;cAAAW,QAAA,GAAAG,QAAA,CAAAI,CAAA;cACAb,KAAA,CAAApB,IAAA,GAAA0B,QAAA,CAAA3B,IAAA;cACAqB,KAAA,CAAAc,iBAAA;cACAd,KAAA,CAAAe,uBAAA;cACA;cACA;cAAAN,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAb,KAAA,CAAAgB,MAAA,CAAAC,QAAA;cACAjB,KAAA,CAAAkB,MAAA;YAAA;cAAA,OAAAT,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,aACAS,iBAAA,WAAAA,kBAAA;MACA,IAAAM,aAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAnC,WAAA,GAAAmC,aAAA,MAAAxC,IAAA,CAAAE,cAAA;IACA;IAEA,aACAiC,uBAAA,WAAAA,wBAAA;MACA;MACA;MACA,IAAAM,aAAA,QAAAzC,IAAA,CAAAE,cAAA;MACA,KAAAI,qBAAA,0DAAAoC,QAAA,CAAAD,aAAA;IACA;IAEA,WACAE,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,iBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAxC,aAAA;UACA,IAAAW,MAAA,GAAA6B,MAAA,CAAA5B,MAAA,CAAAC,MAAA,CAAAF,MAAA;UAEA,IAAAiC,qBAAA,EAAAjC,MAAA,EAAA6B,MAAA,CAAA3C,YAAA,EAAAgD,IAAA,WAAAvB,QAAA;YACAkB,MAAA,CAAAR,MAAA,CAAAc,UAAA;YACAN,MAAA,CAAAN,MAAA;UACA,GAAAa,KAAA;YACAP,MAAA,CAAAxC,aAAA;UACA;QACA;MACA;IACA;IAEA,aACAgD,iBAAA,WAAAA,kBAAA;MACA,KAAAP,KAAA,iBAAAQ,WAAA;IACA;IAEA,aACAC,kBAAA,WAAAA,mBAAAC,MAAA;MACA,SAAAvD,IAAA,CAAAE,cAAA;QACA;MACA;MAEA,IAAAsC,aAAA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAgB,gBAAA,GAAAhB,aAAA,MAAAxC,IAAA,CAAAE,cAAA;MACA,IAAAuD,eAAA,GAAAjB,aAAA,CAAAe,MAAA;MAEA,IAAAE,eAAA,GAAAD,gBAAA;QACA;MACA,WAAAC,eAAA,KAAAD,gBAAA;QACA;MACA;QACA;MACA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAH,MAAA;MACA,IAAAI,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAJ,MAAA;IACA;IAEA,aACAK,aAAA,WAAAA,cAAAL,MAAA;MACA,IAAAI,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAJ,MAAA;IACA;IAEA,WACAjB,MAAA,WAAAA,OAAA;MACA,KAAAuB,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}