#!/bin/bash

# =============================================================================
# RuoYi-Cloud 中间件一键部署脚本
# 用于部署MySQL、Redis、Nacos等中间件服务
# =============================================================================

# 设置错误处理
set -e
set -o pipefail

# 错误处理函数
error_handler() {
    local line_number=$1
    local error_code=$2
    local command="$3"

    # 忽略某些预期的错误情况
    if [[ "$command" =~ "kill.*progress_pid" ]] || [[ "$command" =~ "wait.*progress_pid" ]]; then
        return 0
    fi

    # 忽略临时禁用错误退出时的错误
    if [[ "$command" =~ "set \+e" ]] || [[ "$command" =~ "set -e" ]]; then
        return 0
    fi

    # 忽略timeout命令的错误（这些是预期的）
    if [[ "$command" =~ "timeout" ]]; then
        return 0
    fi

    # 忽略yum和pip安装的错误（这些在函数内部已经处理）
    if [[ "$command" =~ "yum install" ]] || [[ "$command" =~ "pip install" ]]; then
        return 0
    fi

    # 忽略Docker相关命令的错误（这些在函数内部已经处理）
    if [[ "$command" =~ "docker ps" ]] || [[ "$command" =~ "docker logs" ]] || [[ "$command" =~ "docker inspect" ]]; then
        return 0
    fi

    # 忽略网络检查命令的错误
    if [[ "$command" =~ "nc -z" ]] || [[ "$command" =~ "/dev/tcp" ]]; then
        return 0
    fi

    # 忽略算术运算的错误
    if [[ "$command" =~ "\(\(" ]] || [[ "$command" =~ "ready_count" ]] || [[ "$command" =~ "success_count" ]]; then
        return 0
    fi

    echo ""
    echo "========================================"
    echo "           脚本执行出错"
    echo "========================================"
    echo "错误位置: 第 $line_number 行"
    echo "错误代码: $error_code"
    echo "执行命令: $command"
    echo "========================================"
    echo ""
    echo "请检查以下可能的原因："
    echo "1. 网络连接是否正常"
    echo "2. 系统权限是否足够（建议使用root用户）"
    echo "3. 磁盘空间是否充足"
    echo "4. 防火墙设置是否正确"
    echo "5. 查看临时日志文件: /tmp/*_install_*.log"
    echo ""
    echo "如需帮助，请查看日志文件或联系技术支持"
    exit $error_code
}

# 设置错误陷阱
trap 'error_handler ${LINENO} $? "$BASH_COMMAND"' ERR

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 加载配置文件
source "${SCRIPT_DIR}/config.sh"

# 必需的系统命令列表
REQUIRED_COMMANDS=(
    "docker"
    "docker-compose"
    "curl"
    "wget"
    "nc"
    "systemctl"
    "firewall-cmd"
    "awk"
    "sed"
    "grep"
    "tar"
    "unzip"
)

# 函数：显示帮助信息
show_help() {
    echo "RuoYi-Cloud 中间件一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -c, --check             仅检查系统环境"
    echo "  -p, --portainer-only    仅安装Portainer"
    echo "  -f, --force             强制重新安装（删除现有容器并重新创建）"
    echo "  --skip-firewall         跳过防火墙配置"
    echo "  --skip-portainer        跳过Portainer安装"
    echo ""
    echo "说明:"
    echo "  本脚本会同时安装MySQL和达梦数据库作为中间件"
    echo "  在应用部署时可以选择使用哪个数据库"
    echo "  脚本会自动检查容器是否已存在："
    echo "    - 如果容器已存在且运行正常，将跳过安装"
    echo "    - 如果容器已存在但未运行，将尝试启动"
    echo "    - 如果容器不存在，将创建新容器"
    echo "    - 使用 -f 参数可强制重新创建所有容器"
    echo ""
    echo "示例:"
    echo "  $0                      完整安装中间件（MySQL+达梦+Redis+Nacos+Portainer）"
    echo "  $0 -c                   检查系统环境"
    echo "  $0 -p                   仅安装Portainer"
    echo "  $0 -f                   强制重新安装所有中间件"
    echo ""
}

# 函数：批量安装所有依赖命令
batch_install_dependencies() {
    local commands=("$@")
    local install_results=()

    log_info "开始批量安装所有依赖命令..."
    log_info "将尝试安装 ${#commands[@]} 个命令，单个失败不会中断流程"

    # 临时禁用错误退出，确保所有命令都尝试安装
    set +e

    for cmd in "${commands[@]}"; do
        log_info "正在安装: $cmd"

        local install_success=false
        case "$cmd" in
            "docker")
                install_docker
                if [[ $? -eq 0 ]]; then
                    install_success=true
                fi
                ;;
            "docker-compose")
                install_docker_compose
                if [[ $? -eq 0 ]]; then
                    install_success=true
                fi
                ;;
            "curl")
                install_with_progress "curl" "curl"
                if command -v curl >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "wget")
                install_with_progress "wget" "wget"
                if command -v wget >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "nc")
                install_with_progress "nc" "nc nmap-ncat"
                if command -v nc >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "systemctl")
                # systemctl是系统内置命令，直接标记为成功
                if command -v systemctl >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "firewall-cmd")
                install_with_progress "firewall-cmd" "firewalld"
                if command -v firewall-cmd >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "awk"|"sed"|"grep")
                install_with_progress "基础工具" "gawk sed grep"
                if command -v awk >/dev/null 2>&1 && command -v sed >/dev/null 2>&1 && command -v grep >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            "tar"|"unzip")
                install_with_progress "压缩工具" "tar unzip"
                if command -v tar >/dev/null 2>&1 && command -v unzip >/dev/null 2>&1; then
                    install_success=true
                fi
                ;;
            *)
                log_warn "不知道如何安装命令: $cmd"
                ;;
        esac

        # 记录安装结果
        if [[ "$install_success" == "true" ]]; then
            install_results+=("$cmd:SUCCESS")
            log_info "$cmd 安装成功"
        else
            install_results+=("$cmd:FAILED")
            log_warn "$cmd 安装失败"
        fi
    done

    # 重新启用错误退出
    set -e

    log_info "批量安装完成，开始检查最终结果..."

    # 调用最终检查函数
    check_final_dependencies_result "${install_results[@]}"
    return $?
}

# 函数：检查依赖安装的最终结果
check_final_dependencies_result() {
    local results=("$@")
    local critical_missing=()
    local optional_missing=()
    local success_count=0
    local total_count=${#results[@]}

    # 定义关键命令（缺失会导致部署失败）
    local critical_commands=("docker" "docker-compose" "systemctl")

    log_info "========================================"
    log_info "           依赖安装结果汇总"
    log_info "========================================"

    # 分析安装结果
    for result in "${results[@]}"; do
        local cmd="${result%:*}"
        local status="${result#*:}"

        if [[ "$status" == "SUCCESS" ]]; then
            echo -e "  ${GREEN}✓${NC} $cmd - 安装成功"
            success_count=$((success_count + 1))
        else
            echo -e "  ${RED}✗${NC} $cmd - 安装失败"

            # 判断是否为关键命令
            local is_critical=false
            for critical_cmd in "${critical_commands[@]}"; do
                if [[ "$cmd" == "$critical_cmd" ]]; then
                    critical_missing+=("$cmd")
                    is_critical=true
                    break
                fi
            done

            if [[ "$is_critical" == "false" ]]; then
                optional_missing+=("$cmd")
            fi
        fi
    done

    echo ""
    log_info "安装统计: $success_count/$total_count 成功"

    # 处理关键命令缺失
    if [[ ${#critical_missing[@]} -gt 0 ]]; then
        echo ""
        log_error "========================================"
        log_error "           部署终止"
        log_error "========================================"
        log_error "以下关键命令安装失败，无法继续部署："
        for cmd in "${critical_missing[@]}"; do
            echo -e "  ${RED}✗${NC} $cmd"
        done
        echo ""
        log_error "终止原因: 关键组件缺失会导致部署失败"
        log_error "解决方案:"
        echo "  1. 检查网络连接"
        echo "  2. 手动安装缺失的关键命令"
        echo "  3. 重新运行部署脚本"
        return 1
    fi

    # 处理可选命令缺失
    if [[ ${#optional_missing[@]} -gt 0 ]]; then
        echo ""
        log_warn "以下可选命令安装失败，但不影响核心部署："
        for cmd in "${optional_missing[@]}"; do
            echo -e "  ${YELLOW}!${NC} $cmd"
        done
        log_warn "这些命令的缺失可能影响部分功能，建议后续手动安装"
    fi

    echo ""
    log_info "依赖检查通过，继续部署流程..."
    return 0
}

# 函数：带进度提示的安装
install_with_progress() {
    local desc=$1
    local packages=$2

    # 检查软件包是否已安装
    local all_installed=true
    for pkg in $packages; do
        if command -v $pkg >/dev/null 2>&1 || rpm -q "$pkg" >/dev/null 2>&1; then
            continue
        else
            all_installed=false
            break
        fi
    done

    if [[ "$all_installed" == "true" ]]; then
        echo -e "  $desc ... ${GREEN}✓ 已安装${NC}"
        return 0
    fi

    echo -n "  安装 $desc ... "

    # 使用后台进程显示进度
    show_progress &
    local progress_pid=$!

    # 创建临时日志文件
    local temp_log="/tmp/yum_install_${desc// /_}_$$.log"

    # 临时禁用错误退出
    set +e

    # 首先尝试更新yum缓存
    yum clean all >"$temp_log" 2>&1
    yum makecache fast >>"$temp_log" 2>&1

    # 安装软件包，增加超时和重试机制
    timeout 300 yum install -y $packages >>"$temp_log" 2>&1
    local install_result=$?

    # 如果第一次失败，尝试启用EPEL源再安装
    if [[ $install_result -ne 0 ]]; then
        yum install -y epel-release >>"$temp_log" 2>&1
        timeout 300 yum install -y $packages >>"$temp_log" 2>&1
        install_result=$?
    fi

    set -e

    if [[ $install_result -eq 0 ]]; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"
        rm -f "$temp_log"
    else
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${RED}✗ 失败${NC}"
        log_warn "$desc 安装失败，但继续安装其他组件"

        # 显示错误信息
        if [[ -f "$temp_log" ]]; then
            log_warn "yum安装错误信息："
            tail -3 "$temp_log" | while read line; do
                log_warn "  $line"
            done
            rm -f "$temp_log"
        fi
    fi
}

# 函数：显示安装进度
show_progress() {
    local chars="/-\|"
    local i=0
    while true; do
        printf "\b${chars:$i:1}"
        i=$(( (i+1) % 4 ))
        sleep 0.5
    done
}

# 函数：检查网络连接
check_network_connectivity() {
    log_info "检查网络连接..."

    local test_urls=(
        "https://get.docker.com"
        "https://www.baidu.com"
        "https://mirrors.aliyun.com"
    )

    local success_count=0
    for url in "${test_urls[@]}"; do
        echo -n "  测试连接: $url ... "
        if curl -s --connect-timeout 10 --max-time 30 "$url" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC}"
            success_count=$((success_count + 1))
        else
            echo -e "${RED}✗${NC}"
        fi
    done

    if [[ $success_count -eq 0 ]]; then
        log_error "所有网络连接测试都失败，请检查网络设置"
        log_info "可能的解决方案："
        log_info "  1. 检查防火墙设置"
        log_info "  2. 检查DNS配置"
        log_info "  3. 检查代理设置"
        return 1
    elif [[ $success_count -lt ${#test_urls[@]} ]]; then
        log_warn "部分网络连接有问题，可能影响安装速度"
    else
        log_info "网络连接检查通过"
    fi

    return 0
}

# 函数：检查系统资源
check_system_resources() {
    log_info "检查系统资源..."

    # 检查CPU核心数
    local cpu_cores=$(nproc)
    if [[ $cpu_cores -lt 2 ]]; then
        log_warn "CPU核心数较少 ($cpu_cores)，建议至少2核"
    else
        log_info "CPU核心数: $cpu_cores"
    fi

    # 检查内存
    local total_memory=$(free -g | awk 'NR==2{print $2}')
    local available_memory=$(free -g | awk 'NR==2{print $7}')
    if [[ $total_memory -lt 4 ]]; then
        log_warn "总内存较少 (${total_memory}GB)，建议至少4GB"
    fi
    if [[ $available_memory -lt 2 ]]; then
        log_warn "可用内存较少 (${available_memory}GB)，建议至少2GB"
    fi
    log_info "内存状态: 总计${total_memory}GB，可用${available_memory}GB"

    # 检查磁盘空间
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    local available_space=$(df / | awk 'NR==2 {print int($4/1024/1024)}')
    if [[ $disk_usage -gt 80 ]]; then
        log_warn "磁盘使用率较高 (${disk_usage}%)"
    fi
    if [[ $available_space -lt 10 ]]; then
        log_warn "磁盘空间较少 (${available_space}GB)，建议至少10GB，但继续安装"
    fi
    log_info "磁盘空间: 可用${available_space}GB，使用率${disk_usage}%"

    return 0
}

# 函数：配置Docker daemon（镜像加速器等）
configure_docker_daemon() {
    log_info "配置Docker daemon..."

    # 检查是否有root权限
    if [[ $EUID -ne 0 ]]; then
        log_error "配置Docker daemon需要root权限，请使用sudo运行脚本"
        return 1
    fi

    # 创建Docker配置目录
    if ! mkdir -p /etc/docker; then
        log_error "创建Docker配置目录失败"
        return 1
    fi

    # 构建镜像源列表（使用config.sh中的DOCKER_REGISTRIES配置）
    local mirror_list=""
    for registry in "${DOCKER_REGISTRIES[@]}"; do
        # 跳过docker.io官方源，只添加镜像加速器
        if [[ "$registry" != "docker.io" ]]; then
            if [[ -n "$mirror_list" ]]; then
                mirror_list="${mirror_list},"
            fi
            mirror_list="${mirror_list}\"https://${registry}\""
        fi
    done

    # 配置Docker daemon（使用DOCKER_REGISTRIES配置）
    if ! cat > /etc/docker/daemon.json << EOF
{
    "registry-mirrors": [
        ${mirror_list}
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "${LOG_MAX_SIZE}",
        "max-file": "${LOG_MAX_FILES}"
    },
    "storage-driver": "overlay2",
    "live-restore": true
}
EOF
    then
        log_error "写入Docker daemon配置文件失败"
        return 1
    fi

    log_info "Docker daemon配置文件已创建: /etc/docker/daemon.json"

    # 重新加载Docker配置
    set +e
    systemctl daemon-reload
    systemctl restart docker
    local restart_result=$?
    set -e

    if [[ $restart_result -eq 0 ]]; then
        log_info "Docker daemon配置完成"
    else
        log_warn "Docker daemon重启失败，但继续安装流程"
    fi
}

# 函数：确保Docker daemon配置正确（在镜像拉取前调用）
ensure_docker_daemon_config() {
    log_info "检查Docker daemon配置..."

    # 检查Docker daemon配置文件是否存在且配置了镜像源
    if [[ -f "/etc/docker/daemon.json" ]] && grep -q "registry-mirrors" /etc/docker/daemon.json 2>/dev/null; then
        log_info "Docker daemon镜像源配置已存在"

        # 显示当前配置的镜像源
        local mirrors=$(grep -A 10 "registry-mirrors" /etc/docker/daemon.json | grep -o '"https://[^"]*"' | head -3)
        if [[ -n "$mirrors" ]]; then
            log_info "当前配置的镜像源: $mirrors"
        fi

        return 0
    fi

    log_info "Docker daemon未配置镜像源，正在配置..."

    # 调用配置函数
    configure_docker_daemon

    # 验证配置是否成功
    if [[ -f "/etc/docker/daemon.json" ]] && grep -q "registry-mirrors" /etc/docker/daemon.json 2>/dev/null; then
        log_info "Docker daemon镜像源配置完成"

        # 验证Docker服务状态
        if systemctl is-active --quiet docker; then
            log_info "Docker服务运行正常"
        else
            log_warn "Docker服务未运行，尝试启动..."
            systemctl start docker
            sleep 3

            if systemctl is-active --quiet docker; then
                log_info "Docker服务启动成功"
            else
                log_error "Docker服务启动失败"
                return 1
            fi
        fi
    else
        log_error "Docker daemon配置失败"
        return 1
    fi
}

# 函数：安装Docker
install_docker() {
    # 检查Docker是否已经安装
    if command -v docker >/dev/null 2>&1; then
        log_info "Docker已安装，跳过安装步骤"
        # 确保Docker服务运行
        systemctl enable docker >/dev/null 2>&1
        systemctl start docker >/dev/null 2>&1

        # 检查并配置Docker镜像加速器（即使Docker已安装）
        if [[ ! -f "/etc/docker/daemon.json" ]] || ! grep -q "registry-mirrors" /etc/docker/daemon.json 2>/dev/null; then
            log_info "检测到Docker daemon未配置镜像加速器，正在配置..."
            configure_docker_daemon
        else
            log_info "Docker镜像加速器已配置"
        fi
        return 0
    fi

    log_info "安装Docker..."

    # 使用阿里云Docker CE源安装
    echo -n "  使用阿里云Docker CE源安装 ... "
    show_progress &
    local progress_pid=$!

    # 创建临时日志文件来捕获错误信息
    local temp_log="/tmp/docker_aliyun_install_$$.log"

    # 临时禁用错误退出
    set +e

    # 清理yum缓存
    yum clean all >"$temp_log" 2>&1

    # 安装必要的依赖包（增加超时）
    timeout 300 yum install -y yum-utils device-mapper-persistent-data lvm2 >>"$temp_log" 2>&1

    # 添加阿里云Docker CE GPG密钥（增加超时）
    timeout 60 rpm --import https://mirrors.aliyun.com/docker-ce/linux/centos/gpg >>"$temp_log" 2>&1

    # 添加阿里云Docker CE源（增加超时）
    timeout 60 yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo >>"$temp_log" 2>&1

    # 更新yum缓存（增加超时）
    timeout 300 yum makecache fast >>"$temp_log" 2>&1

    # 安装Docker CE（增加超时）
    timeout 600 yum install -y docker-ce docker-ce-cli containerd.io >>"$temp_log" 2>&1
    local install_result=$?

    set -e

    if [[ $install_result -eq 0 ]]; then
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${GREEN}✓ 成功${NC}"

        # 启动并设置开机自启
        systemctl enable docker >/dev/null 2>&1
        systemctl start docker >/dev/null 2>&1

        # 配置Docker镜像加速器
        configure_docker_daemon

        log_info "Docker安装成功"
        rm -f "$temp_log"
        return 0
    else
        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${RED}✗ 失败${NC}"

        # 显示错误信息
        if [[ -f "$temp_log" ]]; then
            log_error "Docker安装失败，错误信息："
            tail -5 "$temp_log" | while read line; do
                log_error "  $line"
            done
            rm -f "$temp_log"
        fi
    fi

    log_error "Docker安装失败"
    log_error "请尝试手动安装Docker："
    log_error "  1. 检查网络连接是否正常"
    log_error "  2. 手动添加阿里云源: yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo"
    log_error "  3. 手动安装: yum install -y docker-ce docker-ce-cli containerd.io"
    return 1
}

# 函数：安装Docker Compose
install_docker_compose() {
    # 检查Docker Compose是否已经安装
    if command -v docker-compose >/dev/null 2>&1; then
        log_info "Docker Compose已安装，跳过安装步骤"
        return 0
    fi

    log_info "安装Docker Compose..."

    # 定义多种安装方法，按优先级排序
    local install_methods=(
        "install_docker_compose_epel"
        "install_docker_compose_binary"
        "install_docker_compose_pip"
    )

    # 临时禁用错误退出，确保所有方法都尝试
    set +e

    # 逐一尝试每种安装方法
    for method in "${install_methods[@]}"; do
        log_info "尝试方法: $method"

        # 调用对应的安装函数
        $method
        local method_result=$?

        # 验证安装是否成功
        if [[ $method_result -eq 0 ]] && command -v docker-compose >/dev/null 2>&1; then
            log_info "Docker Compose安装成功 (使用方法: $method)"
            set -e
            return 0
        else
            log_warn "方法 $method 安装失败，尝试下一种方法..."
        fi
    done

    # 重新启用错误退出
    set -e

    log_error "所有安装方法都失败了"
    log_error "请尝试手动安装Docker Compose："
    log_error "  1. EPEL源: yum install -y epel-release && yum install -y docker-compose"
    log_error "  2. 官方二进制: curl -L \"https://github.com/docker/compose/releases/download/1.29.2/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose && chmod +x /usr/local/bin/docker-compose"
    log_error "  3. pip安装: yum install -y python-pip && pip install docker-compose"
    return 1
}

# 函数：通过EPEL源安装Docker Compose
install_docker_compose_epel() {
    echo -n "  使用EPEL源安装Docker Compose ... "
    show_progress &
    local progress_pid=$!

    # 创建临时日志文件
    local temp_log="/tmp/docker_compose_epel_install_$$.log"

    # 临时禁用错误退出
    set +e

    # 首先安装EPEL源
    timeout 300 yum install -y epel-release >"$temp_log" 2>&1

    # 更新yum缓存
    timeout 300 yum makecache fast >>"$temp_log" 2>&1

    # 安装docker-compose
    timeout 300 yum install -y docker-compose >>"$temp_log" 2>&1
    local install_result=$?

    set -e

    kill $progress_pid 2>/dev/null
    wait $progress_pid 2>/dev/null

    if [[ $install_result -eq 0 ]] && command -v docker-compose >/dev/null 2>&1; then
        echo -e " ${GREEN}✓ 成功${NC}"
        rm -f "$temp_log"
        return 0
    else
        echo -e " ${RED}✗ 失败${NC}"

        # 显示错误信息
        if [[ -f "$temp_log" ]]; then
            log_warn "EPEL源安装失败，错误信息："
            tail -3 "$temp_log" | while read line; do
                log_warn "  $line"
            done
            rm -f "$temp_log"
        fi
        return 1
    fi
}

# 函数：通过官方二进制文件安装Docker Compose
install_docker_compose_binary() {
    echo -n "  下载官方二进制文件安装Docker Compose ... "
    show_progress &
    local progress_pid=$!

    # 创建临时日志文件
    local temp_log="/tmp/docker_compose_binary_install_$$.log"

    # 临时禁用错误退出
    set +e

    # 定义Docker Compose版本和下载URL
    local compose_version="1.29.2"
    local download_url="https://github.com/docker/compose/releases/download/${compose_version}/docker-compose-$(uname -s)-$(uname -m)"
    local install_path="/usr/local/bin/docker-compose"

    # 尝试多个镜像源
    local mirror_urls=(
        "$download_url"
        "https://get.daocloud.io/docker/compose/releases/download/${compose_version}/docker-compose-$(uname -s)-$(uname -m)"
        "https://mirrors.aliyun.com/docker-toolbox/linux/compose/releases/download/${compose_version}/docker-compose-$(uname -s)-$(uname -m)"
    )

    local download_success=false
    for url in "${mirror_urls[@]}"; do
        log_debug "尝试从 $url 下载..."

        # 下载文件（增加超时和重试）
        if timeout 300 curl -L "$url" -o "$install_path" >>"$temp_log" 2>&1; then
            download_success=true
            break
        else
            log_debug "从 $url 下载失败，尝试下一个源..."
        fi
    done

    if [[ "$download_success" == "true" ]]; then
        # 设置执行权限
        chmod +x "$install_path" >>"$temp_log" 2>&1
        local chmod_result=$?

        set -e

        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null

        if [[ $chmod_result -eq 0 ]] && command -v docker-compose >/dev/null 2>&1; then
            echo -e " ${GREEN}✓ 成功${NC}"
            rm -f "$temp_log"
            return 0
        else
            echo -e " ${RED}✗ 失败${NC}"
            log_warn "二进制文件下载成功但设置权限失败"
            rm -f "$install_path" 2>/dev/null
            rm -f "$temp_log"
            return 1
        fi
    else
        set -e

        kill $progress_pid 2>/dev/null
        wait $progress_pid 2>/dev/null
        echo -e " ${RED}✗ 失败${NC}"

        # 显示错误信息
        if [[ -f "$temp_log" ]]; then
            log_warn "二进制文件下载失败，错误信息："
            tail -3 "$temp_log" | while read line; do
                log_warn "  $line"
            done
            rm -f "$temp_log"
        fi
        return 1
    fi
}

# 函数：通过pip安装Docker Compose
install_docker_compose_pip() {
    echo -n "  使用pip安装Docker Compose ... "
    show_progress &
    local progress_pid=$!

    # 创建临时日志文件
    local temp_log="/tmp/docker_compose_pip_install_$$.log"

    # 临时禁用错误退出
    set +e

    # 尝试多种方式安装pip
    local pip_installed=false

    # 方法1: 安装python-pip
    if ! command -v pip >/dev/null 2>&1; then
        timeout 300 yum install -y python-pip >"$temp_log" 2>&1
        if command -v pip >/dev/null 2>&1; then
            pip_installed=true
        fi
    else
        pip_installed=true
    fi

    # 方法2: 安装python2-pip (如果python-pip失败)
    if [[ "$pip_installed" == "false" ]]; then
        timeout 300 yum install -y python2-pip >>"$temp_log" 2>&1
        if command -v pip >/dev/null 2>&1; then
            pip_installed=true
        fi
    fi

    # 方法3: 安装python3-pip (如果前面都失败)
    if [[ "$pip_installed" == "false" ]]; then
        timeout 300 yum install -y python3-pip >>"$temp_log" 2>&1
        if command -v pip3 >/dev/null 2>&1; then
            # 创建pip软链接
            ln -sf /usr/bin/pip3 /usr/bin/pip 2>>"$temp_log"
            if command -v pip >/dev/null 2>&1; then
                pip_installed=true
            fi
        fi
    fi

    # 方法4: 使用get-pip.py脚本安装pip
    if [[ "$pip_installed" == "false" ]]; then
        log_debug "尝试使用get-pip.py安装pip..."

        # 下载get-pip.py
        if timeout 60 curl -s https://bootstrap.pypa.io/get-pip.py -o /tmp/get-pip.py >>"$temp_log" 2>&1; then
            # 使用python安装pip
            timeout 300 python /tmp/get-pip.py >>"$temp_log" 2>&1
            rm -f /tmp/get-pip.py

            if command -v pip >/dev/null 2>&1; then
                pip_installed=true
            fi
        fi
    fi

    # 如果pip安装成功，尝试安装docker-compose
    local install_result=1
    if [[ "$pip_installed" == "true" ]]; then
        # 升级pip到最新版本
        timeout 300 pip install --upgrade pip >>"$temp_log" 2>&1

        # 安装docker-compose
        timeout 300 pip install docker-compose >>"$temp_log" 2>&1
        install_result=$?
    fi

    set -e

    kill $progress_pid 2>/dev/null
    wait $progress_pid 2>/dev/null

    if [[ $install_result -eq 0 ]] && command -v docker-compose >/dev/null 2>&1; then
        echo -e " ${GREEN}✓ 成功${NC}"
        rm -f "$temp_log"
        return 0
    else
        echo -e " ${RED}✗ 失败${NC}"

        # 显示错误信息
        if [[ -f "$temp_log" ]]; then
            log_warn "pip安装失败，错误信息："
            tail -5 "$temp_log" | while read line; do
                log_warn "  $line"
            done
            rm -f "$temp_log"
        fi
        return 1
    fi
}

# 函数：检查系统环境
check_system_environment() {
    log_info "开始检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/centos-release ]]; then
        log_error "此脚本仅支持CentOS 7系统"
        exit 1
    fi
    
    local centos_version=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    if [[ "$centos_version" != "7" ]]; then
        log_error "此脚本仅支持CentOS 7系统，当前版本: $centos_version"
        exit 1
    fi
    
    log_info "操作系统检查通过: CentOS 7"
    
    # 检查并自动安装必需命令
    local missing_commands=()
    for cmd in "${REQUIRED_COMMANDS[@]}"; do
        if ! check_command "$cmd"; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_warn "检测到以下命令未安装，将尝试自动安装:"
        for cmd in "${missing_commands[@]}"; do
            echo "  - $cmd"
        done
        echo ""

        # 批量安装所有缺失的命令
        if ! batch_install_dependencies "${missing_commands[@]}"; then
            log_error "关键依赖安装失败，无法继续部署"
            exit 1
        fi
    fi
    
    log_info "命令检查通过，继续检查Docker服务..."
    
    # 检查Docker服务
    if ! check_docker_status; then
        log_warn "Docker服务未运行，尝试启动Docker服务..."
        set +e
        systemctl enable docker
        systemctl start docker
        sleep 5
        set -e

        if ! check_docker_status; then
            log_error "Docker服务启动失败，请手动启动Docker服务"
            log_error "命令: systemctl start docker"
            exit 1
        else
            log_info "Docker服务启动成功"
        fi
    fi

    # 检查磁盘空间（不因检查失败而退出）
    set +e
    check_disk_space 10
    set -e
    
    # 检查内存
    check_memory 4
    
    # 检查网络连接
    log_info "检查网络连接..."
    if ! curl -s --connect-timeout 10 https://www.baidu.com > /dev/null; then
        log_warn "网络连接可能有问题，请检查网络设置"
    else
        log_info "网络连接正常"
    fi
    
    log_info "系统环境检查完成，准备开始部署中间件..."
}

# 函数：配置防火墙
configure_firewall() {
    if [[ "$SKIP_FIREWALL" == "true" ]]; then
        log_info "跳过防火墙配置"
        return 0
    fi
    
    log_info "配置防火墙..."

    # 启动防火墙服务
    if ! systemctl is-enabled firewalld >/dev/null 2>&1; then
        log_info "启用防火墙服务"
        systemctl enable firewalld >/dev/null 2>&1
    else
        log_info "防火墙服务已启用"
    fi

    if ! systemctl is-active firewalld >/dev/null 2>&1; then
        log_info "启动防火墙服务"
        systemctl start firewalld >/dev/null 2>&1
    else
        log_info "防火墙服务已运行"
    fi
    
    # 开放必要端口
    local ports_to_open=(
        "${PORTS[mysql]}"
        "${PORTS[dameng]}"
        "${PORTS[redis]}"
        "${PORTS[nacos]}"
        "${PORTS[nacos_grpc1]}"
        "${PORTS[nacos_grpc2]}"
        "${PORTS[nginx]}"
        "${PORTS[portainer]}"
    )

    for port in "${ports_to_open[@]}"; do
        if [[ -n "$port" ]]; then
            # 检查端口是否已经开放
            if firewall-cmd --query-port=${port}/tcp >/dev/null 2>&1; then
                log_info "端口 $port 已开放，跳过配置"
            else
                log_info "开放端口: $port"
                if firewall-cmd --permanent --add-port=${port}/tcp >/dev/null 2>&1; then
                    log_info "端口 $port 开放成功"
                else
                    log_warn "端口 $port 开放失败"
                fi
            fi
        else
            log_warn "跳过空端口配置"
        fi
    done
    
    # 重新加载防火墙规则
    log_info "重新加载防火墙规则"
    if firewall-cmd --reload >/dev/null 2>&1; then
        log_info "防火墙规则重新加载成功"
    else
        log_warn "防火墙规则重新加载失败"
    fi

    log_info "防火墙配置完成"
}

# 函数：创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    # 创建主要目录
    create_directory "$DEPLOY_ROOT"
    create_directory "$DEPLOY_ROOT/middleware"
    create_directory "$DEPLOY_ROOT/logs"
    create_directory "$DEPLOY_ROOT/config"
    
    # 创建MySQL数据目录
    create_directory "$DATA_ROOT/mysql/data"
    create_directory "$DATA_ROOT/mysql/logs"
    create_directory "$DATA_ROOT/mysql/conf"

    # 创建达梦数据库目录
    create_directory "$DATA_ROOT/dameng/data"
    create_directory "$DATA_ROOT/dameng/data/ctl_bak"
    create_directory "$DATA_ROOT/dameng/log"
    create_directory "$DATA_ROOT/dameng/log/arch"
    create_directory "$DATA_ROOT/dameng/etc"
    create_directory "$DATA_ROOT/dameng/backup"
    create_directory "$DATA_ROOT/dameng/temp"

    # 设置达梦数据库目录权限
    log_info "设置达梦数据库目录权限..."
    chmod -R 755 "$DATA_ROOT/dameng" 2>/dev/null || true
    # 如果是root用户，设置合适的所有者
    if [[ $EUID -eq 0 ]]; then
        chown -R 1001:1001 "$DATA_ROOT/dameng" 2>/dev/null || true
    fi

    # 创建其他中间件目录
    create_directory "$DATA_ROOT/redis/data"
    create_directory "$DATA_ROOT/redis/conf"
    create_directory "$DATA_ROOT/nacos/data"
    create_directory "$DATA_ROOT/nacos/logs"
    create_directory "$DATA_ROOT/nacos/conf"
    create_directory "$DATA_ROOT/portainer/data"
    
    # 创建MySQL初始化SQL目录
    create_directory "$DEPLOY_ROOT/sql"
    
    log_info "目录结构创建完成"
}

# 函数：检查容器是否存在
check_container_exists() {
    local container_name=$1
    if docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        return 0  # 容器存在
    else
        return 1  # 容器不存在
    fi
}

# 函数：检查容器是否正在运行
check_container_running() {
    local container_name=$1
    if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        return 0  # 容器正在运行
    else
        return 1  # 容器未运行
    fi
}

# 函数：批量拉取所有中间件镜像
batch_pull_middleware_images() {
    local pull_results=()

    log_info "开始批量拉取所有中间件镜像..."

    local images_to_pull=(
        "${IMAGES[mysql]}"
        "${IMAGES[dameng]}"
        "${IMAGES[redis]}"
        "${IMAGES[nacos]}"
        "${IMAGES[nginx]}"
    )

    if [[ "$SKIP_PORTAINER" != "true" ]]; then
        images_to_pull+=("${IMAGES[portainer_cn]}")
    fi

    log_info "将尝试拉取 ${#images_to_pull[@]} 个镜像，单个失败不会中断流程"

    # 临时禁用错误退出，确保所有镜像都尝试拉取
    set +e

    for image in "${images_to_pull[@]}"; do
        local image_name=$(echo "$image" | cut -d':' -f1)
        local image_tag=$(echo "$image" | cut -d':' -f2)

        # 检查镜像是否已存在
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
            log_info "镜像 $image 已存在，跳过拉取"
            pull_results+=("$image:SUCCESS")
            continue
        fi

        log_info "正在拉取镜像: $image"

        # 调用pull_image函数并获取返回值
        local pull_result=0
        pull_image "$image_name" "$image_tag"
        pull_result=$?

        # 额外验证：检查镜像是否真的存在（这是最可靠的判断方式）
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
            pull_results+=("$image:SUCCESS")
            log_info "$image 拉取成功 (返回值: $pull_result, 镜像已确认存在)"
        else
            pull_results+=("$image:FAILED")
            log_warn "$image 拉取失败 (返回值: $pull_result, 镜像不存在于本地)"
        fi
    done

    # 重新启用错误退出
    set -e

    log_info "批量拉取完成，开始检查最终结果..."

    # 调用最终检查函数
    check_final_images_result "${pull_results[@]}"
    return $?
}

# 函数：检查镜像拉取的最终结果
check_final_images_result() {
    local results=("$@")
    local critical_missing=()
    local optional_missing=()
    local success_count=0
    local total_count=${#results[@]}

    # 定义关键镜像（缺失会导致核心功能无法使用）
    local critical_images=("mysql" "redis" "nacos")

    log_info "========================================"
    log_info "           镜像拉取结果汇总"
    log_info "========================================"

    # 分析拉取结果
    for result in "${results[@]}"; do
        # 调试信息：显示原始结果字符串
        log_debug "调试: 原始结果字符串: '$result'"

        # 修复字符串解析逻辑：从右边开始分割，避免镜像名中的冒号干扰
        local status="${result##*:}"  # 获取最后一个冒号后的内容
        local image="${result%:*}"    # 获取最后一个冒号前的内容
        local image_name=$(echo "$image" | cut -d':' -f1 | sed 's/.*\///')

        # 调试信息：显示解析后的值
        log_debug "调试: 解析结果 - image='$image', status='$status', image_name='$image_name'"

        if [[ "$status" == "SUCCESS" ]]; then
            echo -e "  ${GREEN}✓${NC} $image - 拉取成功"
            success_count=$((success_count + 1))
        else
            echo -e "  ${RED}✗${NC} $image - 拉取失败"

            # 判断是否为关键镜像
            local is_critical=false
            for critical_img in "${critical_images[@]}"; do
                if [[ "$image_name" == "$critical_img" ]] || [[ "$image" =~ $critical_img ]]; then
                    critical_missing+=("$image")
                    is_critical=true
                    break
                fi
            done

            if [[ "$is_critical" == "false" ]]; then
                optional_missing+=("$image")
            fi
        fi
    done

    echo ""
    log_info "拉取统计: $success_count/$total_count 成功"

    # 处理关键镜像缺失
    if [[ ${#critical_missing[@]} -gt 0 ]]; then
        echo ""
        log_error "========================================"
        log_error "           部署终止"
        log_error "========================================"
        log_error "以下关键镜像拉取失败，无法继续部署："
        for image in "${critical_missing[@]}"; do
            echo -e "  ${RED}✗${NC} $image"
        done
        echo ""
        log_error "终止原因: 关键镜像缺失会导致核心服务无法启动"
        log_error "解决方案:"
        echo "  1. 检查网络连接和Docker镜像加速器配置"
        echo "  2. 手动拉取缺失的关键镜像"
        echo "  3. 重新运行部署脚本"
        return 1
    fi

    # 处理可选镜像缺失
    if [[ ${#optional_missing[@]} -gt 0 ]]; then
        echo ""
        log_warn "以下可选镜像拉取失败，但不影响核心功能："
        for image in "${optional_missing[@]}"; do
            echo -e "  ${YELLOW}!${NC} $image"
        done
        log_warn "这些镜像的缺失可能影响部分功能，建议后续手动拉取"
    fi

    echo ""
    log_info "镜像检查通过，继续部署流程..."
    return 0
}

# 函数：安装Portainer
install_portainer() {
    if [[ "$SKIP_PORTAINER" == "true" ]]; then
        log_info "跳过Portainer安装"
        return 0
    fi

    log_info "安装中文版Portainer..."

    # 如果启用强制重新安装，先删除现有容器
    if [[ "$FORCE_INSTALL" == "true" ]]; then
        if check_container_exists "portainer"; then
            log_info "强制重新安装：停止并删除现有Portainer容器"
            docker stop portainer 2>/dev/null || true
            docker rm portainer 2>/dev/null || true
        fi
    else
        # 检查Portainer容器是否已存在且正在运行
        if check_container_running "portainer"; then
            log_info "Portainer容器已存在且正在运行，跳过安装"
            log_info "访问地址: http://$(hostname -I | awk '{print $1}'):${PORTS[portainer]}"
            log_info "如需强制重新安装，请使用 -f 或 --force 参数"
            return 0
        fi

        # 检查容器是否存在但未运行
        if check_container_exists "portainer"; then
            log_info "Portainer容器已存在但未运行，尝试启动..."
            if docker start portainer >/dev/null 2>&1; then
                log_info "Portainer容器启动成功"
                log_info "访问地址: http://$(hostname -I | awk '{print $1}'):${PORTS[portainer]}"
                return 0
            else
                log_warn "Portainer容器启动失败，将重新创建容器"
                # 删除已存在的容器
                docker rm portainer 2>/dev/null || true
            fi
        fi
    fi

    # 启动Portainer容器
    docker run -d \
        --name portainer \
        --restart=always \
        -p ${PORTS[portainer]}:9000 \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v ${DATA_ROOT}/portainer/data:/data \
        --memory=${MEMORY_LIMITS[portainer]} \
        ${IMAGES[portainer_cn]}

    if [[ $? -eq 0 ]]; then
        log_info "Portainer安装成功"
        log_info "访问地址: http://$(hostname -I | awk '{print $1}'):${PORTS[portainer]}"
        log_info "首次访问需要设置管理员密码"
    else
        log_error "Portainer安装失败"
        return 1
    fi
}

# 函数：创建Docker网络
create_docker_network() {
    log_info "创建Docker网络: $DOCKER_NETWORK"
    
    if docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "Docker网络已存在: $DOCKER_NETWORK"
    else
        docker network create "$DOCKER_NETWORK"
        log_info "Docker网络创建成功: $DOCKER_NETWORK"
    fi
}

# 函数：生成配置文件
generate_config_files() {
    log_info "生成配置文件..."

    # 生成MySQL配置
    cat > "${DATA_ROOT}/mysql/conf/my.cnf" << EOF
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-time-zone=+8:00
lower-case-table-names=1
innodb-buffer-pool-size=256M
max_connections=1000
EOF

    # 生成Redis配置
    cat > "${DATA_ROOT}/redis/conf/redis.conf" << EOF
bind 0.0.0.0
port 6379
requirepass ${REDIS_PASSWORD}
appendonly yes
appendfsync everysec
save 900 1
save 300 10
save 60 10000
maxmemory 256mb
maxmemory-policy allkeys-lru
EOF

    # 生成Nacos配置
    cat > "${DATA_ROOT}/nacos/conf/application.properties" << EOF
server.contextPath=/nacos
server.servlet.contextPath=/nacos
server.port=8848

spring.datasource.platform=mysql
db.num=1
db.url.0=*****************************/${MYSQL_DATABASE_NACOS}?characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC
db.user.0=root
db.password.0=${MYSQL_ROOT_PASSWORD}

nacos.cmdb.dumpTaskInterval=3600
nacos.cmdb.eventTaskInterval=10
nacos.cmdb.labelTaskInterval=300
nacos.cmdb.loadDataAtStart=false

management.metrics.export.elastic.enabled=false
management.metrics.export.influx.enabled=false

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %{User-Agent}i
server.tomcat.basedir=${DATA_ROOT}/nacos/work

nacos.security.ignore.urls=/,/error,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-ui/public/**,/v1/auth/**,/v1/console/health/**,/actuator/**,/v1/console/server/**

nacos.naming.distro.taskDispatchThreadCount=10
nacos.naming.distro.taskDispatchPeriod=200
nacos.naming.distro.batchSyncKeyCount=1000
nacos.naming.distro.initDataRatio=0.9
nacos.naming.distro.syncRetryDelay=5000
nacos.naming.data.warmup=true
nacos.naming.expireInstance=true
EOF

    # 创建nginx目录结构
    create_directory "$DEPLOY_ROOT/nginx/html"
    create_directory "$DEPLOY_ROOT/nginx/conf"
    create_directory "$DEPLOY_ROOT/nginx/conf.d"
    create_directory "$DEPLOY_ROOT/nginx/logs"
    create_directory "$DEPLOY_ROOT/nginx/ssl"

    # 检查并清理nginx.conf如果它是目录
    if [[ -d "${DEPLOY_ROOT}/nginx/conf/nginx.conf" ]]; then
        log_warn "检测到nginx.conf是目录，正在清理..."
        rm -rf "${DEPLOY_ROOT}/nginx/conf/nginx.conf"
    fi

    # 生成nginx主配置文件
    cat > "${DEPLOY_ROOT}/nginx/conf/nginx.conf" << EOF
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;

    # 默认服务器配置
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name _;
        root /usr/share/nginx/html;

        # 默认首页
        location / {
            index index.html index.htm;
        }

        # 错误页面
        error_page 404 /404.html;
        location = /404.html {
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
        }
    }
}
EOF

    # 生成默认首页
    cat > "${DEPLOY_ROOT}/nginx/html/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>RuoYi-Cloud 部署成功</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; color: #333; }
        .info { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .service { margin: 10px 0; }
        .service strong { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🎉 RuoYi-Cloud 中间件部署成功！</h1>

        <div class="info">
            <h3>服务访问地址：</h3>
            <div class="service"><strong>Nacos控制台：</strong> <a href="http://localhost:8848/nacos" target="_blank">http://localhost:8848/nacos</a></div>
            <div class="service"><strong>Portainer管理：</strong> <a href="http://localhost:9000" target="_blank">http://localhost:9000</a></div>
        </div>

        <div class="info">
            <h3>数据库连接信息：</h3>
            <div class="service"><strong>MySQL：</strong> localhost:3306</div>
            <div class="service"><strong>达梦数据库：</strong> localhost:5236</div>
            <div class="service"><strong>Redis：</strong> localhost:6379</div>
        </div>

        <div class="info">
            <h3>下一步：</h3>
            <p>运行应用部署脚本：<code>./install-application.sh</code></p>
        </div>
    </div>
</body>
</html>
EOF

    # 生成达梦数据库初始化配置
    log_info "生成达梦数据库配置文件..."

    # 创建达梦数据库实例目录（注意：这里要创建正确的目录结构）
    create_directory "${DATA_ROOT}/dameng/data/DAMENG"

    # 生成 dm.ini 配置文件（修复版本）
    cat > "${DATA_ROOT}/dameng/data/DAMENG/dm.ini" << EOF
INSTANCE_NAME = ${DAMENG_INSTANCE_NAME:-dm8_test}
PORT_NUM = 5236
DB_PATH = /opt/dmdbms/data/DAMENG
CTL_PATH = /opt/dmdbms/data/DAMENG
CTL_BAK_PATH = /opt/dmdbms/data/ctl_bak
LOG_PATH = /opt/dmdbms/log
ARCH_PATH = /opt/dmdbms/log/arch
BAK_PATH = /opt/dmdbms/backup
TEMP_PATH = /opt/dmdbms/temp
PAGE_SIZE = 16
EXTENT_SIZE = 16
CASE_SENSITIVE = N
CHARSET = 1
LENGTH_IN_CHAR = Y
MAX_SESSIONS = 100
MEMORY_POOL = 200
BUFFER = 100
LOG_BUFFER_SIZE = 16
SORT_BUF_SIZE = 10
TIME_ZONE = +08:00
EOF

    # 生成达梦数据库初始化脚本
    # 使用新的初始化脚本替换旧的生成方式
    log_info "使用新的初始化脚本替换旧的生成方式..."

    # 复制新的初始化脚本到目标目录
    if [ -f "${SCRIPT_DIR}/init_dm.sh" ]; then
        log_info "从脚本目录复制新的初始化脚本..."
        cp "${SCRIPT_DIR}/init_dm.sh" "${DATA_ROOT}/dameng/init_dm.sh"
    else
        log_info "从Docker目录复制新的初始化脚本..."
        cp "${SCRIPT_DIR}/Docker/init_dm.sh" "${DATA_ROOT}/dameng/init_dm.sh"
    fi

    # 设置初始化脚本权限
    chmod +x "${DATA_ROOT}/dameng/init_dm.sh"

    log_info "配置文件生成完成"
}

# 函数：复制MySQL初始化SQL文件
copy_mysql_init_sql() {
    log_info "复制MySQL初始化SQL文件..."
    
    local mysql_init_dir="${SCRIPT_DIR}/mysql"
    local target_sql_dir="${DEPLOY_ROOT}/sql"
    
    # 检查MySQL初始化目录是否存在
    if [[ -d "$mysql_init_dir" ]]; then
        log_info "发现MySQL初始化目录: $mysql_init_dir"
        
        # 复制所有.sql文件到目标目录
        local sql_files_found=false
        for sql_file in "$mysql_init_dir"/*.sql; do
            if [[ -f "$sql_file" ]]; then
                cp "$sql_file" "$target_sql_dir/"
                log_info "已复制SQL文件: $(basename "$sql_file")"
                sql_files_found=true
            fi
        done
        
        if [[ "$sql_files_found" == "true" ]]; then
            log_info "MySQL初始化SQL文件复制完成"
        else
            log_warn "MySQL初始化目录中没有找到.sql文件"
        fi
    else
        log_warn "未找到MySQL初始化目录: $mysql_init_dir"
    fi
}

# 函数：初始化达梦数据库
initialize_dameng_database() {
    log_info "初始化达梦数据库..."

    # 检查配置文件是否存在
    if [[ ! -f "${DATA_ROOT}/dameng/data/DAMENG/dm.ini" ]]; then
        log_info "dm.ini 配置文件不存在，重新生成..."

        # 确保目录存在
        create_directory "${DATA_ROOT}/dameng/data/DAMENG"

        # 重新生成配置文件
        cat > "${DATA_ROOT}/dameng/data/DAMENG/dm.ini" << EOF
INSTANCE_NAME = ${DAMENG_INSTANCE_NAME:-dm8_test}
PORT_NUM = 5236
DB_PATH = /opt/dmdbms/data/DAMENG
CTL_PATH = /opt/dmdbms/data/DAMENG
CTL_BAK_PATH = /opt/dmdbms/data/ctl_bak
LOG_PATH = /opt/dmdbms/log
ARCH_PATH = /opt/dmdbms/log/arch
BAK_PATH = /opt/dmdbms/backup
TEMP_PATH = /opt/dmdbms/temp
PAGE_SIZE = 16
EXTENT_SIZE = 16
CASE_SENSITIVE = N
CHARSET = 1
LENGTH_IN_CHAR = Y
MAX_SESSIONS = 100
MEMORY_POOL = 200
BUFFER = 100
LOG_BUFFER_SIZE = 16
SORT_BUF_SIZE = 10
TIME_ZONE = 480
EOF
    fi

    # 检查初始化脚本是否存在
    if [[ ! -f "${DATA_ROOT}/dameng/init_dm.sh" ]]; then
        log_info "使用新的初始化脚本替换旧的生成方式..."

        # 复制新的初始化脚本到目标目录
        if [ -f "${SCRIPT_DIR}/init_dm.sh" ]; then
            log_info "从脚本目录复制新的初始化脚本..."
            cp "${SCRIPT_DIR}/init_dm.sh" "${DATA_ROOT}/dameng/init_dm.sh"
        else
            log_info "从Docker目录复制新的初始化脚本..."
            cp "${SCRIPT_DIR}/Docker/init_dm.sh" "${DATA_ROOT}/dameng/init_dm.sh"
        fi

        chmod +x "${DATA_ROOT}/dameng/init_dm.sh"
    fi

    # 设置目录权限
    chmod -R 755 "$DATA_ROOT/dameng" 2>/dev/null || true
    if [[ $EUID -eq 0 ]]; then
        chown -R 1001:1001 "$DATA_ROOT/dameng" 2>/dev/null || true
    fi

    # 验证配置文件
    if [[ -f "${DATA_ROOT}/dameng/data/DAMENG/dm.ini" ]]; then
        log_info "✓ dm.ini 配置文件已创建"
    else
        log_warn "✗ dm.ini 配置文件创建失败"
    fi

    if [[ -f "${DATA_ROOT}/dameng/init_dm.sh" ]]; then
        log_info "✓ 初始化脚本已创建"
    else
        log_warn "✗ 初始化脚本创建失败"
    fi

    log_info "达梦数据库初始化完成"
}

# 函数：智能等待容器启动（10秒检查一次，最多检查3次）
wait_for_container_startup() {
    local container_name=$1
    local max_checks=${2:-3}
    local check_interval=10

    log_info "等待容器 $container_name 启动..."

    # 临时禁用错误退出，避免容器检查失败导致脚本退出
    set +e

    for ((i=1; i<=max_checks; i++)); do
        log_info "第 $i/$max_checks 次检查容器状态..."

        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            log_info "容器 $container_name 已启动"
            set -e  # 重新启用错误退出
            return 0
        fi

        if [[ $i -lt $max_checks ]]; then
            log_info "容器尚未启动，${check_interval}秒后重试..."
            sleep $check_interval
        fi
    done

    log_warn "容器 $container_name 在 $((max_checks * check_interval)) 秒内未能启动"
    set -e  # 重新启用错误退出
    return 1
}


# 函数：检查单个服务端口
check_service_port() {
    local port=$1
    local timeout_seconds=${2:-3}

    # 使用nc命令检查端口
    if command -v nc >/dev/null 2>&1; then
        if timeout "$timeout_seconds" nc -z localhost "$port" 2>/dev/null; then
            return 0
        fi
    fi

    # 如果nc不可用，使用bash内置的网络检查
    if timeout "$timeout_seconds" bash -c "exec 3<>/dev/tcp/localhost/$port && exec 3<&-" 2>/dev/null; then
        return 0
    fi

    return 1
}

# 函数：等待并检查服务启动状态
wait_for_services_startup() {
    log_info "等待服务启动并检查状态..."

    # 定义需要检查的服务及其端口
    local services=(
        "ruoyi-mysql:${PORTS[mysql]}"
        "ruoyi-redis:${PORTS[redis]}"
        "ruoyi-nacos:${PORTS[nacos]}"
        "ruoyi-dameng:${PORTS[dameng]}"
        "ruoyi-nginx:${PORTS[nginx]}"
    )

    log_info "将检查以下 ${#services[@]} 个服务："
    for service_info in "${services[@]}"; do
        local container_name="${service_info%:*}"
        local port="${service_info#*:}"
        log_info "  - $container_name (端口: $port)"
    done

    local max_checks=3
    local check_interval=10
    local check_count=0

    while [[ $check_count -lt $max_checks ]]; do
        check_count=$((check_count + 1))
        local all_ready=true
        local ready_count=0
        local total_count=${#services[@]}

        log_info "第 $check_count/$max_checks 次检查服务状态..."

        # 临时禁用错误退出，确保循环能够继续
        set +e

        for service_info in "${services[@]}"; do
            local container_name="${service_info%:*}"
            local port="${service_info#*:}"

            # 检查容器是否运行
            if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
                # 使用端口检查函数
                if check_service_port "$port" 3; then
                    echo -e "  ${GREEN}✓${NC} $container_name (端口 $port) - 运行正常"
                    ready_count=$((ready_count + 1))
                else
                    echo -e "  ${YELLOW}⚠${NC} $container_name (端口 $port) - 容器运行但端口未就绪"
                    all_ready=false
                fi
            else
                echo -e "  ${RED}✗${NC} $container_name - 容器未运行"
                all_ready=false
            fi
        done

        # 重新启用错误退出
        set -e

        log_info "本轮检查完成，服务就绪状态: $ready_count/$total_count"

        # 如果所有服务都就绪，退出等待
        if [[ "$all_ready" == "true" ]]; then
            log_info "所有服务启动完成！"
            return 0
        fi

        # 如果还有检查次数，继续等待
        if [[ $check_count -lt $max_checks ]]; then
            log_info "等待 ${check_interval} 秒后重新检查..."
            sleep $check_interval
        fi
    done

    # 检查完成，继续执行
    log_warn "服务启动检查完成，$ready_count/$total_count 个服务就绪，继续执行后续步骤"
    return 1
}

# 函数：检查中间件容器状态
check_middleware_containers_status() {
    log_info "检查中间件容器状态..."

    # 定义需要检查的容器名称
    local containers=(
        "ruoyi-mysql"
        "ruoyi-dameng"
        "ruoyi-redis"
        "ruoyi-nacos"
        "ruoyi-nginx"
    )

    # 如果启用强制重新安装，先停止并删除所有相关容器
    if [[ "$FORCE_INSTALL" == "true" ]]; then
        log_info "强制重新安装模式：停止并删除现有容器..."
        for container in "${containers[@]}"; do
            if check_container_exists "$container"; then
                log_info "停止并删除容器: $container"
                docker stop "$container" 2>/dev/null || true
                docker rm "$container" 2>/dev/null || true
            fi
        done
        log_info "所有现有容器已清理，将重新创建"
        return 0
    fi

    local running_containers=()
    local stopped_containers=()
    local missing_containers=()

    # 检查每个容器的状态
    for container in "${containers[@]}"; do
        if check_container_running "$container"; then
            running_containers+=("$container")
            log_info "容器 $container 已存在且正在运行"
        elif check_container_exists "$container"; then
            stopped_containers+=("$container")
            log_info "容器 $container 已存在但未运行"
        else
            missing_containers+=("$container")
            log_info "容器 $container 不存在，将创建新容器"
        fi
    done

    # 显示检查结果汇总
    if [[ ${#running_containers[@]} -gt 0 ]]; then
        log_info "正在运行的容器 (${#running_containers[@]}个): ${running_containers[*]}"
    fi

    if [[ ${#stopped_containers[@]} -gt 0 ]]; then
        log_info "已停止的容器 (${#stopped_containers[@]}个): ${stopped_containers[*]}"
        log_info "这些容器将通过 docker-compose 重新启动"
    fi

    if [[ ${#missing_containers[@]} -gt 0 ]]; then
        log_info "缺失的容器 (${#missing_containers[@]}个): ${missing_containers[*]}"
        log_info "这些容器将通过 docker-compose 创建"
    fi

    # 如果所有容器都在运行，提示用户
    if [[ ${#running_containers[@]} -eq ${#containers[@]} ]]; then
        log_info "所有中间件容器都已在运行"
        log_info "docker-compose 将检查配置并确保服务正常运行"
        log_info "如需强制重新创建容器，请使用 -f 或 --force 参数"
    fi
}

# 主函数
main() {
    local CHECK_ONLY=false
    local PORTAINER_ONLY=false
    local FORCE_INSTALL=false
    local SKIP_FIREWALL=false
    local SKIP_PORTAINER=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -p|--portainer-only)
                PORTAINER_ONLY=true
                shift
                ;;
            -f|--force)
                FORCE_INSTALL=true
                shift
                ;;
            --skip-firewall)
                SKIP_FIREWALL=true
                shift
                ;;
            --skip-portainer)
                SKIP_PORTAINER=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示欢迎信息
    echo "========================================"
    echo "    RuoYi-Cloud 中间件一键部署脚本"
    echo "========================================"
    echo ""
    echo "将安装以下中间件:"
    echo "  - MySQL 5.7 数据库"
    echo "  - 达梦 DM8 数据库"
    echo "  - Redis 7.0 缓存"
    echo "  - Nacos 2.2.3 注册中心"
    echo "  - Nginx 1.24 反向代理"
    if [[ "$SKIP_PORTAINER" != "true" ]]; then
        echo "  - Portainer 容器管理"
    fi
    echo ""
    
    # 检查系统环境
    check_system_environment

    # 检查系统资源（不因检查失败而退出）
    set +e
    check_system_resources
    set -e

    if [[ "$CHECK_ONLY" == "true" ]]; then
        log_info "环境检查完成"
        exit 0
    fi
    
    # 仅安装Portainer
    if [[ "$PORTAINER_ONLY" == "true" ]]; then
        install_portainer
        exit 0
    fi
    
    # 开始安装
    log_info "开始安装中间件..."
    
    # 配置防火墙
    configure_firewall
    
    # 创建目录结构
    create_directories
    
    # 创建Docker网络
    create_docker_network
    
    # 生成配置文件
    generate_config_files
    
    # 复制MySQL初始化SQL文件
    copy_mysql_init_sql
    
    # 初始化达梦数据库
    initialize_dameng_database

    # 确保Docker daemon配置正确（在拉取镜像之前）
    ensure_docker_daemon_config

    # 批量拉取镜像
    if ! batch_pull_middleware_images; then
        log_error "关键镜像拉取失败，无法继续部署"
        exit 1
    fi
    
    # 安装Portainer
    install_portainer

    # 检查中间件容器状态
    check_middleware_containers_status

    # 启动中间件服务
    log_info "启动中间件服务..."
    cd "${SCRIPT_DIR}"

    # 设置环境变量
    export DEPLOY_ROOT
    export DATA_ROOT
    export MYSQL_ROOT_PASSWORD
    export MYSQL_DATABASE
    export MYSQL_DATABASE_NACOS
    export MYSQL_USER
    export MYSQL_PASSWORD
    export DAMENG_SYSDBA_PASSWORD
    export DAMENG_DATABASE
    export DAMENG_USER
    export DAMENG_PASSWORD
    export DAMENG_INSTANCE_NAME
    export DAMENG_PORT_NUM
    export REDIS_PASSWORD

    # 启动服务
    docker-compose -f docker-compose-middleware.yml up -d

    if [[ $? -eq 0 ]]; then
        log_info "中间件服务启动命令执行成功"

        # 智能等待容器初始化（10秒检查一次，检查3次）
        log_info "等待容器初始化..."
        for ((i=1; i<=3; i++)); do
            log_info "第 $i/3 次检查容器初始化状态..."

            # 检查主要容器是否已启动
            local containers_started=0
            local total_containers=0

            # 临时禁用错误退出，避免容器检查失败导致脚本退出
            set +e

            for container in "ruoyi-mysql" "ruoyi-redis" "ruoyi-nacos" "ruoyi-dameng" "ruoyi-nginx"; do
                total_containers=$((total_containers + 1))
                if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
                    containers_started=$((containers_started + 1))
                fi
            done

            # 重新启用错误退出
            set -e

            log_info "容器启动状态: $containers_started/$total_containers"

            if [[ $containers_started -eq $total_containers ]]; then
                log_info "主要容器已启动完成"
                break
            fi

            if [[ $i -lt 3 ]]; then
                log_info "容器尚未完全启动，10秒后重试..."
                sleep 10
            fi
        done
        
        # 智能等待服务启动
        wait_for_services_startup
        local wait_result=$?

        # 显示最终状态
        echo ""
        log_info "显示所有容器状态:"
        docker-compose -f docker-compose-middleware.yml ps

        if [[ $wait_result -ne 0 ]]; then
            log_warn "部分服务可能未完全启动，请检查上述状态信息"

            # 提供故障排除建议
            echo ""
            log_info "故障排除建议："
            echo "1. 检查系统资源："
            echo "   - 内存使用: free -h"
            echo "   - 磁盘空间: df -h"
            echo ""
            echo "2. 查看具体服务日志："
            echo "   - MySQL: docker logs ruoyi-mysql"
            echo "   - 达梦数据库: docker logs ruoyi-dameng"
            echo "   - Redis: docker logs ruoyi-redis"
            echo "   - Nacos: docker logs ruoyi-nacos"
            echo ""
            echo "3. 重启特定服务："
            echo "   - docker-compose -f docker-compose-middleware.yml restart [服务名]"
            echo ""

        fi
    else
        log_error "中间件服务启动失败"
        return 1
    fi

    log_info "中间件安装完成！"
    echo ""
    echo "========================================"
    echo "           安装完成信息"
    echo "========================================"
    echo "部署路径: $DEPLOY_ROOT"
    echo "数据路径: $DATA_ROOT"
    echo ""

    # 显示服务运行状态
    echo "服务运行状态:"
    local services_status=(
        "ruoyi-mysql:MySQL数据库"
        "ruoyi-dameng:达梦数据库"
        "ruoyi-redis:Redis缓存"
        "ruoyi-nacos:Nacos注册中心"
        "ruoyi-nginx:Nginx代理"
    )

    for service_info in "${services_status[@]}"; do
        local container_name="${service_info%:*}"
        local service_desc="${service_info#*:}"

        if docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            echo -e "  ${GREEN}✓${NC} $service_desc - 运行中"
        else
            echo -e "  ${RED}✗${NC} $service_desc - 未运行"
        fi
    done
    echo ""
    echo "数据库访问信息:"
    echo "  MySQL: $(hostname -I | awk '{print $1}'):${PORTS[mysql]}"
    echo "    数据库: $MYSQL_DATABASE"
    echo "    用户名: root"
    echo "    密码: $MYSQL_ROOT_PASSWORD"
    echo ""
    echo "  达梦数据库: $(hostname -I | awk '{print $1}'):${PORTS[dameng]}"
    echo "    实例名: $DAMENG_INSTANCE_NAME"
    echo "    数据库: $DAMENG_DATABASE"
    echo "    用户名: $DAMENG_USER"
    echo "    密码: $DAMENG_PASSWORD"
    echo ""
    echo "其他服务:"
    echo "  Redis: $(hostname -I | awk '{print $1}'):${PORTS[redis]}"
    echo "  Nacos: http://$(hostname -I | awk '{print $1}'):${PORTS[nacos]}/nacos"
    if [[ "$SKIP_PORTAINER" != "true" ]]; then
        echo "  Portainer: http://$(hostname -I | awk '{print $1}'):${PORTS[portainer]}"
    fi
    echo ""
    echo "下一步: 运行应用部署脚本"
    echo "  ./install-application.sh"
    echo "  (部署时可以选择使用MySQL或达梦数据库)"
    echo "========================================"
}

# 执行主函数
main "$@"
