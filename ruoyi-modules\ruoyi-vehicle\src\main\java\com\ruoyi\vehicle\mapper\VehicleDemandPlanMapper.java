package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleDemandPlan;

/**
 * 车辆需求计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleDemandPlanMapper 
{
    /**
     * 查询车辆需求计划
     * 
     * @param planId 车辆需求计划主键
     * @return 车辆需求计划
     */
    public VehicleDemandPlan selectVehicleDemandPlanByPlanId(Long planId);

    /**
     * 查询车辆需求计划列表
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 车辆需求计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanList(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 新增车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    public int insertVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 修改车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    public int updateVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 删除车辆需求计划
     * 
     * @param planId 车辆需求计划主键
     * @return 结果
     */
    public int deleteVehicleDemandPlanByPlanId(Long planId);

    /**
     * 批量删除车辆需求计划
     * 
     * @param planIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleDemandPlanByPlanIds(Long[] planIds);

    /**
     * 根据审批状态查询计划列表
     * 
     * @param approvalStatus 审批状态
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanByStatus(String approvalStatus);

    /**
     * 根据队伍ID查询计划列表
     * 
     * @param teamId 队伍ID
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanByTeamId(Long teamId);
}
