{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\index.vue", "mtime": 1754147167174}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9Xb3JrL2Nhci9BQS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9Xb3JrL2Nhci9BQS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKdmFyIF9vcmRlciA9IHJlcXVpcmUoIkAvYXBpL3ZlaGljbGUvb3JkZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJWZWhpY2xlT3JkZXIiLAogIGRpY3RzOiBbJ3ZlaGljbGVfdHlwZScsICdvcmRlcl9zdGF0dXMnXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOiuouWNleihqOagvOaVsOaNrgogICAgICBvcmRlckxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOivpuaDheW8ueWHuuWxggogICAgICBkZXRhaWxPcGVuOiBmYWxzZSwKICAgICAgLy8g6YCA5Zue5by55Ye65bGCCiAgICAgIHJlamVjdE9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBvcmRlck51bWJlcjogbnVsbCwKICAgICAgICB2ZWhpY2xlVHlwZTogbnVsbCwKICAgICAgICBvcmRlclN0YXR1czogbnVsbCwKICAgICAgICB0ZWFtTmFtZTogbnVsbAogICAgICB9LAogICAgICAvLyDorqLljZXor6bmg4UKICAgICAgb3JkZXJEZXRhaWw6IHt9LAogICAgICAvLyDpgIDlm57ooajljZUKICAgICAgcmVqZWN0Rm9ybTogewogICAgICAgIG9yZGVySWQ6IG51bGwsCiAgICAgICAgcmVqZWN0UmVhc29uOiBudWxsCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6K6i5Y2V5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9vcmRlci5saXN0T3JkZXIpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMub3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm9yZGVySWQ7CiAgICAgIH0pOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOafpeeci+ivpuaDhSAqL2hhbmRsZVZpZXc6IGZ1bmN0aW9uIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIHRoaXMub3JkZXJEZXRhaWwgPSByb3c7CiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOmYn+S8jeehruiupCAqL2hhbmRsZVRlYW1Db25maXJtOiBmdW5jdGlvbiBoYW5kbGVUZWFtQ29uZmlybShyb3cpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOivpeiuouWNle+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX29yZGVyLnRlYW1Db25maXJtT3JkZXIpKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLmdldExpc3QoKTsKICAgICAgICBfdGhpczIuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuehruiupOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOiwg+W6puehruiupCAqL2hhbmRsZURpc3BhdGNoQ29uZmlybTogZnVuY3Rpb24gaGFuZGxlRGlzcGF0Y2hDb25maXJtKHJvdykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k6K+l6K6i5Y2V77yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfb3JkZXIuZGlzcGF0Y2hDb25maXJtT3JkZXIpKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuehruiupOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOS4u+euoeehruiupCAqL2hhbmRsZU1hbmFnZXJDb25maXJtOiBmdW5jdGlvbiBoYW5kbGVNYW5hZ2VyQ29uZmlybShyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOivpeiuouWNle+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX29yZGVyLm1hbmFnZXJDb25maXJtT3JkZXIpKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM0LmdldExpc3QoKTsKICAgICAgICBfdGhpczQuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuehruiupOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOiuoeeul+i0ueeUqCAqL2hhbmRsZUNhbGN1bGF0ZUNvc3Q6IGZ1bmN0aW9uIGhhbmRsZUNhbGN1bGF0ZUNvc3Qocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKborqHnrpfor6XorqLljZXotLnnlKjvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9vcmRlci5jYWxjdWxhdGVPcmRlckNvc3QpKHJvdy5vcmRlcklkKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LmdldExpc3QoKTsKICAgICAgICBfdGhpczUuJG1vZGFsLm1zZ1N1Y2Nlc3MoIui0ueeUqOiuoeeul+aIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOmAgOWbnuiuouWNlSAqL2hhbmRsZVJlamVjdDogZnVuY3Rpb24gaGFuZGxlUmVqZWN0KHJvdykgewogICAgICB0aGlzLnJlamVjdEZvcm0ub3JkZXJJZCA9IHJvdy5vcmRlcklkOwogICAgICB0aGlzLnJlamVjdEZvcm0ucmVqZWN0UmVhc29uID0gbnVsbDsKICAgICAgdGhpcy5yZWplY3RPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk6YCA5ZueICovc3VibWl0UmVqZWN0OiBmdW5jdGlvbiBzdWJtaXRSZWplY3QoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICAoMCwgX29yZGVyLnJlamVjdE9yZGVyKSh0aGlzLnJlamVjdEZvcm0ub3JkZXJJZCwgdGhpcy5yZWplY3RGb3JtLnJlamVjdFJlYXNvbikudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM2LmdldExpc3QoKTsKICAgICAgICBfdGhpczYucmVqZWN0T3BlbiA9IGZhbHNlOwogICAgICAgIF90aGlzNi4kbW9kYWwubXNnU3VjY2Vzcygi6YCA5Zue5oiQ5YqfIik7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmibnph4/noa7orqQgKi9oYW5kbGVCYXRjaENvbmZpcm06IGZ1bmN0aW9uIGhhbmRsZUJhdGNoQ29uZmlybSgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciBvcmRlcklkcyA9IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTpgInkuK3nmoTorqLljZXvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9vcmRlci5iYXRjaENvbmZpcm1PcmRlcnMpKG9yZGVySWRzLCAidGVhbSIpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczcuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNy4kbW9kYWwubXNnU3VjY2Vzcygi5om56YeP56Gu6K6k5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ3ZlaGljbGUvb3JkZXIvZXhwb3J0JywgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKSwgIm9yZGVyXyIuY29uY2F0KG5ldyBEYXRlKCkuZ2V0VGltZSgpLCAiLnhsc3giKSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_order", "require", "name", "dicts", "data", "loading", "ids", "multiple", "showSearch", "total", "orderList", "title", "open", "detailOpen", "rejectOpen", "queryParams", "pageNum", "pageSize", "orderNumber", "vehicleType", "orderStatus", "teamName", "orderDetail", "rejectForm", "orderId", "rejectReason", "created", "getList", "methods", "_this", "listOrder", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleView", "row", "handleTeamConfirm", "_this2", "$modal", "confirm", "teamConfirmOrder", "msgSuccess", "catch", "handleDispatchConfirm", "_this3", "dispatchConfirmOrder", "handleManagerConfirm", "_this4", "managerConfirmOrder", "handleCalculateCost", "_this5", "calculateOrderCost", "handleReject", "submitReject", "_this6", "rejectOrder", "handleBatchConfirm", "_this7", "orderIds", "batchConfirmOrders", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/vehicle/order/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"订单编号\" prop=\"orderNumber\">\n        <el-input\n          v-model=\"queryParams.orderNumber\"\n          placeholder=\"请输入订单编号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n        <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择订单状态\" clearable>\n          <el-option label=\"待开始\" value=\"pending\"></el-option>\n          <el-option label=\"进行中\" value=\"running\"></el-option>\n          <el-option label=\"司机已结束\" value=\"driver_finished\"></el-option>\n          <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n          <el-option label=\"调度已确认\" value=\"dispatch_confirmed\"></el-option>\n          <el-option label=\"已完成\" value=\"completed\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"队伍名称\" prop=\"teamName\">\n        <el-input\n          v-model=\"queryParams.teamName\"\n          placeholder=\"请输入队伍名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-check\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleBatchConfirm\"\n          v-hasPermi=\"['vehicle:order:confirm']\"\n        >批量确认</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:order:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"订单编号\" align=\"center\" prop=\"orderNumber\" />\n      <el-table-column label=\"申请标题\" align=\"center\" prop=\"applicationTitle\" />\n      <el-table-column label=\"车辆信息\" align=\"center\" prop=\"vehicleInfo\" />\n      <el-table-column label=\"队伍名称\" align=\"center\" prop=\"teamName\" />\n      <el-table-column label=\"司机姓名\" align=\"center\" prop=\"driverName\" />\n      <el-table-column label=\"使用地点\" align=\"center\" prop=\"usageLocation\" />\n      <el-table-column label=\"计划时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.plannedStartTime, '{y}-{m}-{d} {h}:{i}') }}</span><br/>\n          <span>{{ parseTime(scope.row.plannedEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.order_status\" :value=\"scope.row.orderStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"费用状态\" align=\"center\" prop=\"costStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.costStatus === 'calculated'\" type=\"warning\">已计算</el-tag>\n          <el-tag v-else-if=\"scope.row.costStatus === 'confirmed'\" type=\"success\">已确认</el-tag>\n          <el-tag v-else type=\"info\">未计算</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalCost\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['vehicle:order:query']\"\n          >详情</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'driver_finished'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleTeamConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >队伍确认</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleDispatchConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >调度确认</el-button>\n          <el-button\n            v-if=\"scope.row.orderStatus === 'dispatch_confirmed'\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-check\"\n            @click=\"handleManagerConfirm(scope.row)\"\n            v-hasPermi=\"['vehicle:order:confirm']\"\n          >主管确认</el-button>\n          <el-button\n            v-if=\"scope.row.costStatus !== 'confirmed' && ['driver_finished', 'completed'].includes(scope.row.orderStatus)\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-money\"\n            @click=\"handleCalculateCost(scope.row)\"\n            v-hasPermi=\"['vehicle:order:calculate']\"\n          >计算费用</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-close\"\n            @click=\"handleReject(scope.row)\"\n            v-hasPermi=\"['vehicle:order:reject']\"\n          >退回</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单编号\">{{ orderDetail.orderNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请标题\">{{ orderDetail.applicationTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">{{ orderDetail.vehicleInfo }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍名称\">{{ orderDetail.teamName }}</el-descriptions-item>\n        <el-descriptions-item label=\"司机姓名\">{{ orderDetail.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"使用地点\">{{ orderDetail.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划开始时间\">{{ parseTime(orderDetail.plannedStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"计划结束时间\">{{ parseTime(orderDetail.plannedEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(orderDetail.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(orderDetail.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <dict-tag :options=\"dict.type.order_status\" :value=\"orderDetail.orderStatus\"/>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用状态\">\n          <el-tag v-if=\"orderDetail.costStatus === 'calculated'\" type=\"warning\">已计算</el-tag>\n          <el-tag v-else-if=\"orderDetail.costStatus === 'confirmed'\" type=\"success\">已确认</el-tag>\n          <el-tag v-else type=\"info\">未计算</el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"总费用\">{{ orderDetail.totalCost }}</el-descriptions-item>\n        <el-descriptions-item label=\"费用承担方\">{{ orderDetail.costBearer }}</el-descriptions-item>\n        <el-descriptions-item label=\"备注\" :span=\"2\">{{ orderDetail.remark }}</el-descriptions-item>\n      </el-descriptions>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 退回订单对话框 -->\n    <el-dialog title=\"退回订单\" :visible.sync=\"rejectOpen\" width=\"500px\" append-to-body>\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" label-width=\"80px\">\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" placeholder=\"请输入退回原因\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rejectOpen = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitReject\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrder, getOrder, teamConfirmOrder, dispatchConfirmOrder, managerConfirmOrder, rejectOrder, calculateOrderCost, batchConfirmOrders } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"VehicleOrder\",\n  dicts: ['vehicle_type', 'order_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 订单表格数据\n      orderList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 详情弹出层\n      detailOpen: false,\n      // 退回弹出层\n      rejectOpen: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderNumber: null,\n        vehicleType: null,\n        orderStatus: null,\n        teamName: null\n      },\n      // 订单详情\n      orderDetail: {},\n      // 退回表单\n      rejectForm: {\n        orderId: null,\n        rejectReason: null\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      listOrder(this.queryParams).then(response => {\n        this.orderList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    /** 查看详情 */\n    handleView(row) {\n      this.orderDetail = row;\n      this.detailOpen = true;\n    },\n    /** 队伍确认 */\n    handleTeamConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return teamConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 调度确认 */\n    handleDispatchConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return dispatchConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 主管确认 */\n    handleManagerConfirm(row) {\n      this.$modal.confirm('是否确认该订单？').then(function() {\n        return managerConfirmOrder(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"确认成功\");\n      }).catch(() => {});\n    },\n    /** 计算费用 */\n    handleCalculateCost(row) {\n      this.$modal.confirm('是否计算该订单费用？').then(function() {\n        return calculateOrderCost(row.orderId);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"费用计算成功\");\n      }).catch(() => {});\n    },\n    /** 退回订单 */\n    handleReject(row) {\n      this.rejectForm.orderId = row.orderId;\n      this.rejectForm.rejectReason = null;\n      this.rejectOpen = true;\n    },\n    /** 提交退回 */\n    submitReject() {\n      rejectOrder(this.rejectForm.orderId, this.rejectForm.rejectReason).then(() => {\n        this.getList();\n        this.rejectOpen = false;\n        this.$modal.msgSuccess(\"退回成功\");\n      });\n    },\n    /** 批量确认 */\n    handleBatchConfirm() {\n      const orderIds = this.ids;\n      this.$modal.confirm('是否确认选中的订单？').then(function() {\n        return batchConfirmOrders(orderIds, \"team\");\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"批量确认成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/order/export', {\n        ...this.queryParams\n      }, `order_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AA2MA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,QAAA;MACA;MACA;MACAC,WAAA;MACA;MACAC,UAAA;QACAC,OAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,gBAAA,OAAAf,WAAA,EAAAgB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnB,SAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAoB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA,aACA6B,WAAA,WAAAA,YAAA;MACA,KAAAnB,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhC,GAAA,GAAAgC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,OAAA;MAAA;MACA,KAAAjB,QAAA,IAAA+B,SAAA,CAAAG,MAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAArB,WAAA,GAAAqB,GAAA;MACA,KAAA9B,UAAA;IACA;IACA,WACA+B,iBAAA,WAAAA,kBAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,aAAAhB,IAAA;QACA,WAAAiB,uBAAA,EAAAL,GAAA,CAAAnB,OAAA;MACA,GAAAO,IAAA;QACAc,MAAA,CAAAlB,OAAA;QACAkB,MAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACAC,qBAAA,WAAAA,sBAAAR,GAAA;MAAA,IAAAS,MAAA;MACA,KAAAN,MAAA,CAAAC,OAAA,aAAAhB,IAAA;QACA,WAAAsB,2BAAA,EAAAV,GAAA,CAAAnB,OAAA;MACA,GAAAO,IAAA;QACAqB,MAAA,CAAAzB,OAAA;QACAyB,MAAA,CAAAN,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACAI,oBAAA,WAAAA,qBAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,KAAAT,MAAA,CAAAC,OAAA,aAAAhB,IAAA;QACA,WAAAyB,0BAAA,EAAAb,GAAA,CAAAnB,OAAA;MACA,GAAAO,IAAA;QACAwB,MAAA,CAAA5B,OAAA;QACA4B,MAAA,CAAAT,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACAO,mBAAA,WAAAA,oBAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,KAAAZ,MAAA,CAAAC,OAAA,eAAAhB,IAAA;QACA,WAAA4B,yBAAA,EAAAhB,GAAA,CAAAnB,OAAA;MACA,GAAAO,IAAA;QACA2B,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAZ,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,WACAU,YAAA,WAAAA,aAAAjB,GAAA;MACA,KAAApB,UAAA,CAAAC,OAAA,GAAAmB,GAAA,CAAAnB,OAAA;MACA,KAAAD,UAAA,CAAAE,YAAA;MACA,KAAAX,UAAA;IACA;IACA,WACA+C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,kBAAA,OAAAxC,UAAA,CAAAC,OAAA,OAAAD,UAAA,CAAAE,YAAA,EAAAM,IAAA;QACA+B,MAAA,CAAAnC,OAAA;QACAmC,MAAA,CAAAhD,UAAA;QACAgD,MAAA,CAAAhB,MAAA,CAAAG,UAAA;MACA;IACA;IACA,WACAe,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,QAAA5D,GAAA;MACA,KAAAwC,MAAA,CAAAC,OAAA,eAAAhB,IAAA;QACA,WAAAoC,yBAAA,EAAAD,QAAA;MACA,GAAAnC,IAAA;QACAkC,MAAA,CAAAtC,OAAA;QACAsC,MAAA,CAAAnB,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACAkB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,6BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAxD,WAAA,YAAAyD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}