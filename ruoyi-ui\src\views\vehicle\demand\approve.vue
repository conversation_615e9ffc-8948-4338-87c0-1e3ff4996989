<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">需求计划审批</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>
      
      <!-- 申请信息展示 -->
      <el-descriptions title="申请信息" :column="2" border>
        <el-descriptions-item label="计划标题" :span="2">{{ form.planTitle }}</el-descriptions-item>
        <el-descriptions-item label="车辆类型">{{ form.vehicleType }}</el-descriptions-item>
        <el-descriptions-item label="车辆型号">{{ form.vehicleModel }}</el-descriptions-item>
        <el-descriptions-item label="需求单位">{{ form.demandUnit }}</el-descriptions-item>
        <el-descriptions-item label="需求数量">{{ form.demandQuantity }}</el-descriptions-item>
        <el-descriptions-item label="需求开始时间">{{ parseTime(form.demandStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="需求结束时间">{{ parseTime(form.demandEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ form.applicant }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ form.applicantPhone }}</el-descriptions-item>
        <el-descriptions-item label="当前状态">
          <el-tag :type="getStatusTagType(form.approvalStatus)">
            {{ getStatusText(form.approvalStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="用途说明" :span="2">{{ form.usagePurpose }}</el-descriptions-item>
        <el-descriptions-item v-if="form.remark" label="备注" :span="2">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>

      <!-- 审批流程展示 -->
      <div style="margin-top: 20px;">
        <h3>审批流程</h3>
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="项目调度室审批" :description="getStepDescription('pending_level1')"></el-step>
          <el-step title="机械主管审批" :description="getStepDescription('pending_level2')"></el-step>
          <el-step title="经营部门审批" :description="getStepDescription('pending_level3')"></el-step>
          <el-step title="审批完成" :description="getStepDescription('approved')"></el-step>
        </el-steps>
      </div>

      <!-- 审批操作 -->
      <div v-if="canCurrentUserApprove" style="margin-top: 30px;">
        <el-divider content-position="left">审批操作</el-divider>
        <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
          <el-form-item label="审批结果" prop="approvalStatus">
            <el-radio-group v-model="approvalForm.approvalStatus">
              <el-radio label="approved">通过</el-radio>
              <el-radio label="rejected">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="approvalComments">
            <el-input
              v-model="approvalForm.approvalComments"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="500"
              show-word-limit />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitApproval" :loading="submitLoading">
              <i class="el-icon-check"></i> 提交审批
            </el-button>
            <el-button @click="resetApprovalForm">
              <i class="el-icon-refresh-left"></i> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 审批历史 -->
      <div v-if="approvalHistory.length > 0" style="margin-top: 30px;">
        <el-divider content-position="left">审批历史</el-divider>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in approvalHistory"
            :key="index"
            :timestamp="parseTime(item.approvalTime)"
            placement="top">
            <el-card>
              <h4>{{ item.approverName }} - {{ item.approvalLevel }}</h4>
              <p>审批结果：
                <el-tag :type="item.approvalStatus === 'approved' ? 'success' : 'danger'" size="mini">
                  {{ item.approvalStatus === 'approved' ? '通过' : '拒绝' }}
                </el-tag>
              </p>
              <p v-if="item.approvalComments">审批意见：{{ item.approvalComments }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDemand, approveDemand } from "@/api/vehicle/demand";

export default {
  name: "DemandApprove",
  data() {
    return {
      // 申请信息
      form: {},
      // 审批表单
      approvalForm: {
        approvalStatus: 'approved',
        approvalComments: ''
      },
      // 提交状态
      submitLoading: false,
      // 当前步骤
      currentStep: 0,
      // 是否可以审批
      canCurrentUserApprove: false,
      // 审批历史
      approvalHistory: [],
      // 审批表单验证规则
      approvalRules: {
        approvalStatus: [
          { required: true, message: "请选择审批结果", trigger: "change" }
        ],
        approvalComments: [
          { required: true, message: "审批意见不能为空", trigger: "blur" },
          { min: 5, max: 500, message: "长度在 5 到 500 个字符", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    const planId = this.$route.params.planId;
    if (planId) {
      this.loadPlanDetail(planId);
    }
  },
  methods: {
    /** 加载计划详情 */
    async loadPlanDetail(planId) {
      try {
        const response = await getDemand(planId);
        this.form = response.data;
        this.updateCurrentStep();
        this.checkApprovalPermission();
        // TODO: 加载审批历史
        // this.loadApprovalHistory(planId);
      } catch (error) {
        this.$modal.msgError("加载计划详情失败");
        this.goBack();
      }
    },
    
    /** 更新当前步骤 */
    updateCurrentStep() {
      const statusStepMap = {
        'pending_level1': 0,
        'pending_level2': 1,
        'pending_level3': 2,
        'approved': 3,
        'rejected': -1
      };
      this.currentStep = statusStepMap[this.form.approvalStatus] || 0;
    },
    
    /** 检查审批权限 */
    checkApprovalPermission() {
      // TODO: 根据当前用户角色和审批状态判断是否可以审批
      // 这里简化处理，实际应该根据用户角色判断
      const currentStatus = this.form.approvalStatus;
      this.canCurrentUserApprove = ['pending_level1', 'pending_level2', 'pending_level3'].includes(currentStatus);
    },
    
    /** 提交审批 */
    submitApproval() {
      this.$refs["approvalForm"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          const planId = this.$route.params.planId;
          
          approveDemand(planId, this.approvalForm).then(response => {
            this.$modal.msgSuccess("审批提交成功");
            this.goBack();
          }).catch(() => {
            this.submitLoading = false;
          });
        }
      });
    },
    
    /** 重置审批表单 */
    resetApprovalForm() {
      this.$refs["approvalForm"].resetFields();
    },
    
    /** 获取步骤描述 */
    getStepDescription(status) {
      if (this.form.approvalStatus === 'rejected') {
        return '审批被拒绝';
      }
      
      const statusStepMap = {
        'pending_level1': 0,
        'pending_level2': 1,
        'pending_level3': 2,
        'approved': 3
      };
      
      const currentStepIndex = statusStepMap[this.form.approvalStatus];
      const targetStepIndex = statusStepMap[status];
      
      if (targetStepIndex < currentStepIndex) {
        return '已完成';
      } else if (targetStepIndex === currentStepIndex) {
        return '进行中';
      } else {
        return '待处理';
      }
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'draft': 'info',
        'pending_level1': 'warning',
        'pending_level2': 'warning', 
        'pending_level3': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'draft': '草稿',
        'pending_level1': '待项目调度室审批',
        'pending_level2': '待机械主管审批',
        'pending_level3': '待经营部门审批',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '未知';
    },
    
    /** 返回列表 */
    goBack() {
      this.$router.push('/vehicle/demand');
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-steps {
  margin: 20px 0;
}

.el-timeline {
  padding-left: 20px;
}
</style>
