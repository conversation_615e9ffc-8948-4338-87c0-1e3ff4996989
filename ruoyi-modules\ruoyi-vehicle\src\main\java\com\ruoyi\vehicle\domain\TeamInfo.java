package com.ruoyi.vehicle.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 队伍信息对象 team_info
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class TeamInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 队伍ID */
    private Long teamId;

    /** 队伍名称 */
    @Excel(name = "队伍名称")
    private String teamName;

    /** 队伍编码 */
    @Excel(name = "队伍编码")
    private String teamCode;

    /** 队伍负责人 */
    @Excel(name = "队伍负责人")
    private String teamLeader;

    /** 负责人联系方式 */
    @Excel(name = "负责人联系方式")
    private String leaderPhone;

    /** 队伍类型 */
    @Excel(name = "队伍类型")
    private String teamType;

    /** 工作区域 */
    @Excel(name = "工作区域")
    private String workArea;

    /** 队伍人数 */
    @Excel(name = "队伍人数")
    private Integer memberCount;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "正常=0,停用=1")
    private String status;

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    public Long getTeamId() 
    {
        return teamId;
    }

    public void setTeamName(String teamName) 
    {
        this.teamName = teamName;
    }

    @NotBlank(message = "队伍名称不能为空")
    @Size(min = 0, max = 100, message = "队伍名称长度不能超过100个字符")
    public String getTeamName()
    {
        return teamName;
    }

    public void setTeamCode(String teamCode)
    {
        this.teamCode = teamCode;
    }

    @Size(min = 0, max = 50, message = "队伍编码长度不能超过50个字符")
    public String getTeamCode()
    {
        return teamCode;
    }

    public void setTeamLeader(String teamLeader) 
    {
        this.teamLeader = teamLeader;
    }

    @NotBlank(message = "队伍负责人不能为空")
    @Size(min = 0, max = 50, message = "队伍负责人长度不能超过50个字符")
    public String getTeamLeader() 
    {
        return teamLeader;
    }

    public void setLeaderPhone(String leaderPhone) 
    {
        this.leaderPhone = leaderPhone;
    }

    @Size(min = 0, max = 20, message = "负责人联系方式长度不能超过20个字符")
    public String getLeaderPhone() 
    {
        return leaderPhone;
    }

    public void setTeamType(String teamType) 
    {
        this.teamType = teamType;
    }

    @Size(min = 0, max = 50, message = "队伍类型长度不能超过50个字符")
    public String getTeamType()
    {
        return teamType;
    }

    public void setWorkArea(String workArea)
    {
        this.workArea = workArea;
    }

    @Size(min = 0, max = 200, message = "工作区域长度不能超过200个字符")
    public String getWorkArea()
    {
        return workArea;
    }

    public void setMemberCount(Integer memberCount)
    {
        this.memberCount = memberCount;
    }

    public Integer getMemberCount()
    {
        return memberCount;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    @Size(min = 0, max = 20, message = "状态长度不能超过20个字符")
    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teamId", getTeamId())
            .append("teamName", getTeamName())
            .append("teamCode", getTeamCode())
            .append("teamLeader", getTeamLeader())
            .append("leaderPhone", getLeaderPhone())
            .append("teamType", getTeamType())
            .append("workArea", getWorkArea())
            .append("memberCount", getMemberCount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
