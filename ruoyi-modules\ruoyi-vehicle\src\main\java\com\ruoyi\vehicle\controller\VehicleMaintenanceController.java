package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleMaintenance;
import com.ruoyi.vehicle.service.IVehicleMaintenanceService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 车辆维修记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/maintenance")
public class VehicleMaintenanceController extends BaseController
{
    @Autowired
    private IVehicleMaintenanceService vehicleMaintenanceService;

    /**
     * 查询车辆维修记录列表
     */
    @RequiresPermissions("vehicle:maintenance:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleMaintenance vehicleMaintenance)
    {
        startPage();
        List<VehicleMaintenance> list = vehicleMaintenanceService.selectVehicleMaintenanceList(vehicleMaintenance);
        return getDataTable(list);
    }

    /**
     * 导出车辆维修记录列表
     */
    @RequiresPermissions("vehicle:maintenance:export")
    @Log(title = "车辆维修记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleMaintenance vehicleMaintenance)
    {
        List<VehicleMaintenance> list = vehicleMaintenanceService.selectVehicleMaintenanceList(vehicleMaintenance);
        ExcelUtil<VehicleMaintenance> util = new ExcelUtil<VehicleMaintenance>(VehicleMaintenance.class);
        util.exportExcel(response, list, "车辆维修记录数据");
    }

    /**
     * 获取车辆维修记录详细信息
     */
    @RequiresPermissions("vehicle:maintenance:query")
    @GetMapping(value = "/{maintenanceId}")
    public AjaxResult getInfo(@PathVariable("maintenanceId") Long maintenanceId)
    {
        return success(vehicleMaintenanceService.selectVehicleMaintenanceByMaintenanceId(maintenanceId));
    }

    /**
     * 新增车辆维修记录
     */
    @RequiresPermissions("vehicle:maintenance:add")
    @Log(title = "车辆维修记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleMaintenance vehicleMaintenance)
    {
        vehicleMaintenance.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleMaintenanceService.insertVehicleMaintenance(vehicleMaintenance));
    }

    /**
     * 修改车辆维修记录
     */
    @RequiresPermissions("vehicle:maintenance:edit")
    @Log(title = "车辆维修记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleMaintenance vehicleMaintenance)
    {
        vehicleMaintenance.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleMaintenanceService.updateVehicleMaintenance(vehicleMaintenance));
    }

    /**
     * 删除车辆维修记录
     */
    @RequiresPermissions("vehicle:maintenance:remove")
    @Log(title = "车辆维修记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{maintenanceIds}")
    public AjaxResult remove(@PathVariable Long[] maintenanceIds)
    {
        return toAjax(vehicleMaintenanceService.deleteVehicleMaintenanceByMaintenanceIds(maintenanceIds));
    }

    /**
     * 根据车辆ID查询维修记录
     */
    @RequiresPermissions("vehicle:maintenance:list")
    @GetMapping("/vehicle/{vehicleId}")
    public AjaxResult getByVehicleId(@PathVariable Long vehicleId)
    {
        List<VehicleMaintenance> list = vehicleMaintenanceService.selectVehicleMaintenanceByVehicleId(vehicleId);
        return success(list);
    }

    /**
     * 根据维修状态查询维修记录
     */
    @RequiresPermissions("vehicle:maintenance:list")
    @GetMapping("/status/{status}")
    public AjaxResult getByStatus(@PathVariable String status)
    {
        List<VehicleMaintenance> list = vehicleMaintenanceService.selectVehicleMaintenanceByStatus(status);
        return success(list);
    }

    /**
     * 获取维修费用统计
     */
    @RequiresPermissions("vehicle:maintenance:list")
    @GetMapping("/cost-statistics")
    public AjaxResult getCostStatistics()
    {
        // TODO: 实现维修费用统计逻辑
        return success("维修费用统计功能开发中...");
    }

    /**
     * 获取维修频次统计
     */
    @RequiresPermissions("vehicle:maintenance:list")
    @GetMapping("/frequency-statistics")
    public AjaxResult getFrequencyStatistics()
    {
        // TODO: 实现维修频次统计逻辑
        return success("维修频次统计功能开发中...");
    }
}
