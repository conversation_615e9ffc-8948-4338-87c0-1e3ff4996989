package com.ruoyi.vehicle.service.impl;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.vehicle.mapper.VehicleOrderMapper;
import com.ruoyi.vehicle.mapper.VehicleApplicationMapper;
import com.ruoyi.vehicle.mapper.VehicleInfoMapper;
import com.ruoyi.vehicle.domain.VehicleOrder;
import com.ruoyi.vehicle.domain.VehicleApplication;
import com.ruoyi.vehicle.domain.VehicleInfo;
import com.ruoyi.vehicle.service.IVehicleOrderService;
import com.ruoyi.vehicle.service.IVehicleNotificationService;

/**
 * 用车订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleOrderServiceImpl implements IVehicleOrderService 
{
    @Autowired
    private VehicleOrderMapper vehicleOrderMapper;

    @Autowired
    private VehicleApplicationMapper vehicleApplicationMapper;

    @Autowired
    private VehicleInfoMapper vehicleInfoMapper;

    @Autowired
    private IVehicleNotificationService notificationService;

    /**
     * 查询用车订单
     * 
     * @param orderId 用车订单主键
     * @return 用车订单
     */
    @Override
    public VehicleOrder selectVehicleOrderByOrderId(Long orderId)
    {
        return vehicleOrderMapper.selectVehicleOrderByOrderId(orderId);
    }

    /**
     * 查询用车订单列表
     * 
     * @param vehicleOrder 用车订单
     * @return 用车订单
     */
    @Override
    public List<VehicleOrder> selectVehicleOrderList(VehicleOrder vehicleOrder)
    {
        return vehicleOrderMapper.selectVehicleOrderList(vehicleOrder);
    }

    /**
     * 新增用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    @Override
    public int insertVehicleOrder(VehicleOrder vehicleOrder)
    {
        vehicleOrder.setCreateTime(DateUtils.getNowDate());
        vehicleOrder.setOrderStatus("pending"); // 默认待开始状态
        return vehicleOrderMapper.insertVehicleOrder(vehicleOrder);
    }

    /**
     * 修改用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    @Override
    public int updateVehicleOrder(VehicleOrder vehicleOrder)
    {
        vehicleOrder.setUpdateTime(DateUtils.getNowDate());
        return vehicleOrderMapper.updateVehicleOrder(vehicleOrder);
    }

    /**
     * 批量删除用车订单
     * 
     * @param orderIds 需要删除的用车订单主键
     * @return 结果
     */
    @Override
    public int deleteVehicleOrderByOrderIds(Long[] orderIds)
    {
        return vehicleOrderMapper.deleteVehicleOrderByOrderIds(orderIds);
    }

    /**
     * 删除用车订单信息
     * 
     * @param orderId 用车订单主键
     * @return 结果
     */
    @Override
    public int deleteVehicleOrderByOrderId(Long orderId)
    {
        return vehicleOrderMapper.deleteVehicleOrderByOrderId(orderId);
    }

    /**
     * 根据申请ID创建订单
     * 
     * @param applicationId 申请ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int createOrderFromApplication(Long applicationId, String operName)
    {
        // 1. 获取申请信息
        VehicleApplication application = vehicleApplicationMapper.selectVehicleApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("用车申请不存在");
        }
        
        if (!"approved".equals(application.getApprovalStatus())) {
            throw new ServiceException("只能为已审批通过的申请创建订单");
        }
        
        if (application.getAssignedVehicleId() == null) {
            throw new ServiceException("申请尚未分配车辆");
        }
        
        // 2. 检查是否已存在订单
        VehicleOrder existingOrder = vehicleOrderMapper.selectVehicleOrderByApplicationId(applicationId);
        if (existingOrder != null) {
            throw new ServiceException("该申请已创建订单");
        }
        
        // 3. 创建订单
        VehicleOrder order = new VehicleOrder();
        order.setApplicationId(applicationId);
        order.setVehicleId(application.getAssignedVehicleId());
        order.setTeamId(application.getTeamId());
        order.setDriverName(application.getAssignedDriver());
        order.setUsageLocation(application.getUsageLocation());
        order.setPlannedStartTime(application.getStartTime());
        order.setPlannedEndTime(application.getEndTime());
        order.setOrderStatus("pending");
        order.setCreateBy(operName);
        
        // 4. 检查50吨阈值
        VehicleInfo vehicle = vehicleInfoMapper.selectVehicleInfoByVehicleId(application.getAssignedVehicleId());
        if (vehicle != null && vehicle.getVehicleWeight() != null && 
            vehicle.getVehicleWeight().compareTo(new java.math.BigDecimal("50")) > 0) {
            // 超过50吨需要特殊处理
            order.setRemark("车辆重量超过50吨，需要特殊审批流程");
        }
        
        int result = insertVehicleOrder(order);
        
        if (result > 0) {
            // 发送订单创建通知
            notificationService.sendOrderNotification(order.getOrderId(), "create", operName);
        }
        
        return result;
    }

    /**
     * 司机开始用车
     * 
     * @param orderId 订单ID
     * @param startPhotoUrl 开始拍照URL
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int startOrder(Long orderId, String startPhotoUrl, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        if (!"pending".equals(order.getOrderStatus())) {
            throw new ServiceException("只能开始待开始状态的订单");
        }
        
        order.setOrderStatus("running");
        order.setActualStartTime(DateUtils.getNowDate());
        order.setStartPhotoUrl(startPhotoUrl);
        order.setUpdateBy(operName);
        
        int result = updateVehicleOrder(order);
        
        if (result > 0) {
            // 发送开始用车通知
            notificationService.sendOrderNotification(orderId, "start", operName);
        }
        
        return result;
    }

    /**
     * 司机结束用车
     * 
     * @param orderId 订单ID
     * @param endPhotoUrl 结束拍照URL
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int finishOrder(Long orderId, String endPhotoUrl, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        if (!"running".equals(order.getOrderStatus())) {
            throw new ServiceException("只能结束进行中的订单");
        }
        
        order.setOrderStatus("driver_finished");
        order.setActualEndTime(DateUtils.getNowDate());
        order.setEndPhotoUrl(endPhotoUrl);
        order.setUpdateBy(operName);
        
        int result = updateVehicleOrder(order);
        
        if (result > 0) {
            // 发送结束用车通知给队伍
            notificationService.sendOrderNotification(orderId, "finish", operName);
        }
        
        return result;
    }

    /**
     * 队伍确认订单
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int teamConfirmOrder(Long orderId, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!"driver_finished".equals(order.getOrderStatus())) {
            throw new ServiceException("只能确认司机已结束的订单");
        }

        order.setOrderStatus("team_confirmed");
        order.setTeamConfirmTime(DateUtils.getNowDate());
        order.setTeamConfirmPerson(operName);
        order.setUpdateBy(operName);

        int result = updateVehicleOrder(order);

        if (result > 0) {
            // 发送队伍确认通知给调度室
            notificationService.sendOrderNotification(orderId, "team_confirm", operName);
        }

        return result;
    }

    /**
     * 调度室确认订单
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int dispatchConfirmOrder(Long orderId, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!"team_confirmed".equals(order.getOrderStatus())) {
            throw new ServiceException("只能确认队伍已确认的订单");
        }

        order.setOrderStatus("dispatch_confirmed");
        order.setDispatchConfirmTime(DateUtils.getNowDate());
        order.setDispatchConfirmPerson(operName);
        order.setUpdateBy(operName);

        int result = updateVehicleOrder(order);

        if (result > 0) {
            // 发送调度室确认通知给主管
            notificationService.sendOrderNotification(orderId, "dispatch_confirm", operName);
        }

        return result;
    }

    /**
     * 主管确认订单
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int managerConfirmOrder(Long orderId, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!"dispatch_confirmed".equals(order.getOrderStatus())) {
            throw new ServiceException("只能确认调度室已确认的订单");
        }

        order.setOrderStatus("completed");
        order.setManagerConfirmTime(DateUtils.getNowDate());
        order.setManagerConfirmPerson(operName);
        order.setUpdateBy(operName);

        int result = updateVehicleOrder(order);

        if (result > 0) {
            // 发送主管确认通知，订单完成
            notificationService.sendOrderNotification(orderId, "manager_confirm", operName);
        }

        return result;
    }

    /**
     * 退回订单
     *
     * @param orderId 订单ID
     * @param rejectReason 退回原因
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectOrder(Long orderId, String rejectReason, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if ("completed".equals(order.getOrderStatus())) {
            throw new ServiceException("已完成的订单不能退回");
        }

        // 根据当前状态确定退回到的状态
        String previousStatus = getPreviousStatus(order.getOrderStatus());

        order.setOrderStatus(previousStatus);
        order.setRejectReason(rejectReason);
        order.setUpdateBy(operName);

        int result = updateVehicleOrder(order);

        if (result > 0) {
            // 发送退回通知
            notificationService.sendOrderNotification(orderId, "reject", operName);
        }

        return result;
    }

    /**
     * 根据订单状态查询订单列表
     *
     * @param orderStatus 订单状态
     * @return 订单集合
     */
    @Override
    public List<VehicleOrder> selectVehicleOrderByStatus(String orderStatus)
    {
        return vehicleOrderMapper.selectVehicleOrderByStatus(orderStatus);
    }

    /**
     * 根据车辆ID查询订单列表
     *
     * @param vehicleId 车辆ID
     * @return 订单集合
     */
    @Override
    public List<VehicleOrder> selectVehicleOrderByVehicleId(Long vehicleId)
    {
        return vehicleOrderMapper.selectVehicleOrderByVehicleId(vehicleId);
    }

    /**
     * 根据队伍ID查询订单列表
     *
     * @param teamId 队伍ID
     * @return 订单集合
     */
    @Override
    public List<VehicleOrder> selectVehicleOrderByTeamId(Long teamId)
    {
        return vehicleOrderMapper.selectVehicleOrderByTeamId(teamId);
    }

    /**
     * 查询待确认的订单列表
     *
     * @param confirmType 确认类型（team、dispatch、manager）
     * @return 订单集合
     */
    @Override
    public List<VehicleOrder> selectPendingConfirmOrders(String confirmType)
    {
        String status;
        switch (confirmType) {
            case "team":
                status = "driver_finished";
                break;
            case "dispatch":
                status = "team_confirmed";
                break;
            case "manager":
                status = "dispatch_confirmed";
                break;
            default:
                throw new ServiceException("无效的确认类型");
        }

        return selectVehicleOrderByStatus(status);
    }

    /**
     * 获取待确认订单列表（别名方法）
     *
     * @param confirmType 确认类型
     * @return 订单列表
     */
    @Override
    public List<VehicleOrder> getPendingConfirmOrders(String confirmType)
    {
        return selectPendingConfirmOrders(confirmType);
    }

    /**
     * 批量确认订单
     *
     * @param orderIds 订单ID数组
     * @param confirmType 确认类型
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int batchConfirmOrders(Long[] orderIds, String confirmType, String operName)
    {
        int successCount = 0;
        for (Long orderId : orderIds) {
            try {
                int result = 0;
                switch (confirmType) {
                    case "team":
                        result = teamConfirmOrder(orderId, operName);
                        break;
                    case "dispatch":
                        result = dispatchConfirmOrder(orderId, operName);
                        break;
                    case "manager":
                        result = managerConfirmOrder(orderId, operName);
                        break;
                    default:
                        continue;
                }

                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他订单
                continue;
            }
        }
        return successCount;
    }

    /**
     * 获取前一个状态
     */
    private String getPreviousStatus(String currentStatus) {
        switch (currentStatus) {
            case "running":
                return "pending";
            case "driver_finished":
                return "running";
            case "team_confirmed":
                return "driver_finished";
            case "dispatch_confirmed":
                return "team_confirmed";
            case "manager_confirmed":
                return "dispatch_confirmed";
            default:
                return "pending";
        }
    }

    /**
     * 计算订单费用
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int calculateOrderCost(Long orderId, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!"driver_finished".equals(order.getOrderStatus()) && !"completed".equals(order.getOrderStatus())) {
            throw new ServiceException("只能为已结束的订单计算费用");
        }

        if (order.getActualStartTime() == null || order.getActualEndTime() == null) {
            throw new ServiceException("订单缺少实际开始或结束时间");
        }

        // 获取车辆信息
        VehicleInfo vehicle = vehicleInfoMapper.selectVehicleInfoByVehicleId(order.getVehicleId());
        if (vehicle == null) {
            throw new ServiceException("车辆信息不存在");
        }

        // 计算时长
        long diffInMillies = order.getActualEndTime().getTime() - order.getActualStartTime().getTime();
        BigDecimal duration;

        if ("day".equals(order.getCostUnit())) {
            // 按天计算
            duration = new BigDecimal(diffInMillies / (1000 * 60 * 60 * 24));
            if (duration.compareTo(BigDecimal.ZERO) == 0) {
                duration = BigDecimal.ONE; // 不足一天按一天计算
            }
        } else {
            // 按小时计算
            duration = new BigDecimal(diffInMillies / (1000.0 * 60 * 60));
        }

        // 获取单价（从车辆信息或配置中获取）
        BigDecimal unitPrice = vehicle.getCostPerHour() != null ? vehicle.getCostPerHour() : BigDecimal.ZERO;

        // 计算总费用
        BigDecimal totalCost = duration.multiply(unitPrice);

        // 确定费用承担方（50吨阈值判断）
        String costBearer = determineCostBearer(vehicle.getVehicleWeight());

        // 更新订单费用信息
        order.setActualDuration(duration);
        order.setUnitPrice(unitPrice);
        order.setTotalCost(totalCost);
        order.setCostBearer(costBearer);
        order.setCostStatus("calculated");
        order.setCostCalculateTime(DateUtils.getNowDate());
        order.setUpdateBy(operName);

        return updateVehicleOrder(order);
    }

    /**
     * 确认订单费用
     *
     * @param orderId 订单ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmOrderCost(Long orderId, String operName)
    {
        VehicleOrder order = selectVehicleOrderByOrderId(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        if (!"calculated".equals(order.getCostStatus())) {
            throw new ServiceException("只能确认已计算费用的订单");
        }

        order.setCostStatus("confirmed");
        order.setCostConfirmTime(DateUtils.getNowDate());
        order.setCostConfirmPerson(operName);
        order.setUpdateBy(operName);

        return updateVehicleOrder(order);
    }

    /**
     * 根据车辆重量确定费用承担方
     *
     * @param vehicleWeight 车辆重量
     * @return 费用承担方
     */
    private String determineCostBearer(BigDecimal vehicleWeight) {
        if (vehicleWeight != null && vehicleWeight.compareTo(new BigDecimal("50")) > 0) {
            return "项目承担"; // 超过50吨由项目承担
        } else {
            return "队伍承担"; // 50吨及以下由队伍承担
        }
    }

    /**
     * 检查是否需要特殊审批流程（50吨阈值）
     *
     * @param vehicleWeight 车辆重量
     * @return 是否需要特殊审批
     */
    private boolean needsSpecialApproval(BigDecimal vehicleWeight) {
        return vehicleWeight != null && vehicleWeight.compareTo(new BigDecimal("50")) > 0;
    }

    /**
     * 主管审批订单
     *
     * @param approvalData 审批数据
     * @return 结果
     */
    @Override
    @Transactional
    public int managerApproveOrder(Object approvalData)
    {
        // 这里需要根据具体的审批数据结构来实现
        // 暂时返回成功，具体实现需要根据前端传递的数据结构来完善
        return 1;
    }
}
