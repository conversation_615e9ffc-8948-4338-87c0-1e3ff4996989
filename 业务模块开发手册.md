# RuoYi-Cloud 业务模块开发手册

## 目录
- [项目架构概述](#项目架构概述)
- [技术栈](#技术栈)
- [开发环境准备](#开发环境准备)
- [后端业务模块开发](#后端业务模块开发)
- [前端业务模块开发](#前端业务模块开发)
- [数据库设计](#数据库设计)
- [API接口规范](#api接口规范)
- [权限控制](#权限控制)
- [部署指南](#部署指南)
- [常见问题](#常见问题)

## 项目架构概述

RuoYi-Cloud 是一个基于 Spring Boot、Spring Cloud & Alibaba 的微服务架构平台，采用前后端分离模式。

### 系统模块结构
```
com.ruoyi     
├── ruoyi-ui              // 前端框架 [80]
├── ruoyi-gateway         // 网关模块 [8080]
├── ruoyi-auth            // 认证中心 [9200]
├── ruoyi-api             // 接口模块
│       └── ruoyi-api-system                          // 系统接口
├── ruoyi-common          // 通用模块
│       └── ruoyi-common-core                         // 核心模块
│       └── ruoyi-common-datascope                    // 权限范围
│       └── ruoyi-common-datasource                   // 多数据源
│       └── ruoyi-common-log                          // 日志记录
│       └── ruoyi-common-redis                        // 缓存服务
│       └── ruoyi-common-seata                        // 分布式事务
│       └── ruoyi-common-security                     // 安全模块
│       └── ruoyi-common-sensitive                    // 数据脱敏
│       └── ruoyi-common-swagger                      // 系统接口
├── ruoyi-modules         // 业务模块
│       └── ruoyi-system                              // 系统模块 [9201]
│       └── ruoyi-gen                                 // 代码生成 [9202]
│       └── ruoyi-job                                 // 定时任务 [9203]
│       └── ruoyi-file                                // 文件服务 [9300]
├── ruoyi-visual          // 图形化管理模块
│       └── ruoyi-visual-monitor                      // 监控中心 [9100]
```

## 技术栈

### 后端技术栈
- **Spring Boot 2.7.18** - 基础框架
- **Spring Cloud 2021.0.9** - 微服务框架
- **Spring Cloud Alibaba 2021.0.6.1** - 阿里微服务组件
- **Nacos** - 注册中心和配置中心
- **Sentinel** - 流量控制框架
- **Seata** - 分布式事务
- **Redis** - 缓存服务
- **MySQL** - 数据库
- **MyBatis** - ORM框架
- **Swagger** - API文档

### 前端技术栈
- **Vue 2.x** - 前端框架
- **Element UI** - UI组件库
- **Vue Router** - 路由管理
- **Vuex** - 状态管理
- **Axios** - HTTP客户端

## 开发环境准备

### 环境要求
- JDK >= 1.8
- MySQL >= 5.7
- Maven >= 3.0
- Node >= 12
- Redis >= 3
- Nacos >= 2.0

### 开发工具
- IDE: IntelliJ IDEA 或 Eclipse
- 数据库工具: Navicat 或 MySQL Workbench
- API测试: Postman 或 Swagger UI

## 后端业务模块开发

### 1. 创建新业务模块

#### 1.1 在 ruoyi-modules 下创建新模块
```bash
# 创建模块目录结构
ruoyi-modules/ruoyi-example/
├── pom.xml
├── src/main/java/com/ruoyi/example/
│   ├── controller/
│   ├── service/
│   ├── mapper/
│   ├── domain/
│   └── RuoYiExampleApplication.java
└── src/main/resources/
    ├── bootstrap.yml
    └── mapper/
```

#### 1.2 配置 pom.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi-modules</artifactId>
        <version>3.6.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>ruoyi-modules-example</artifactId>
    <description>示例业务模块</description>
    
    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        
        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        
        <!-- RuoYi Common DataSource -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-datasource</artifactId>
        </dependency>
        
        <!-- RuoYi Common DataScope -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-datascope</artifactId>
        </dependency>
        
        <!-- RuoYi Common Log -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-log</artifactId>
        </dependency>
        
        <!-- RuoYi Common Swagger -->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-swagger</artifactId>
        </dependency>
    </dependencies>
</project>
```

#### 1.3 创建启动类
```java
package com.ruoyi.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;

@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class RuoYiExampleApplication {
    public static void main(String[] args) {
        SpringApplication.run(RuoYiExampleApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  示例模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
```

#### 1.4 配置 bootstrap.yml
```yaml
# Tomcat
server:
  port: 9204

# Spring
spring: 
  application:
    name: ruoyi-example
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yml
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
```

### 2. 创建实体类 (Domain)

#### 2.1 基础实体类
```java
package com.ruoyi.example.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

public class Example extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 示例ID */
    @Excel(name = "示例ID", cellType = Excel.ColumnType.NUMERIC)
    private Long exampleId;

    /** 示例名称 */
    @Excel(name = "示例名称")
    private String exampleName;

    /** 示例描述 */
    @Excel(name = "示例描述")
    private String description;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    // getter和setter方法
    public Long getExampleId() {
        return exampleId;
    }

    public void setExampleId(Long exampleId) {
        this.exampleId = exampleId;
    }

    @NotBlank(message = "示例名称不能为空")
    @Size(min = 0, max = 100, message = "示例名称长度不能超过100个字符")
    public String getExampleName() {
        return exampleName;
    }

    public void setExampleName(String exampleName) {
        this.exampleName = exampleName;
    }

    @Size(min = 0, max = 500, message = "示例描述长度不能超过500个字符")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("exampleId", getExampleId())
            .append("exampleName", getExampleName())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
```

### 3. 创建 Mapper 接口

```java
package com.ruoyi.example.mapper;

import com.ruoyi.example.domain.Example;
import java.util.List;

public interface ExampleMapper {
    /**
     * 查询示例列表
     * 
     * @param example 示例信息
     * @return 示例集合
     */
    public List<Example> selectExampleList(Example example);

    /**
     * 查询示例详细
     * 
     * @param exampleId 示例ID
     * @return 示例信息
     */
    public Example selectExampleById(Long exampleId);

    /**
     * 新增示例
     * 
     * @param example 示例信息
     * @return 结果
     */
    public int insertExample(Example example);

    /**
     * 修改示例
     * 
     * @param example 示例信息
     * @return 结果
     */
    public int updateExample(Example example);

    /**
     * 删除示例
     * 
     * @param exampleId 示例ID
     * @return 结果
     */
    public int deleteExampleById(Long exampleId);

    /**
     * 批量删除示例
     * 
     * @param exampleIds 需要删除的示例ID
     * @return 结果
     */
    public int deleteExampleByIds(Long[] exampleIds);
}
```

### 4. 创建 Mapper XML

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.example.mapper.ExampleMapper">
    
    <resultMap type="Example" id="ExampleResult">
        <result property="exampleId"    column="example_id"    />
        <result property="exampleName"  column="example_name"  />
        <result property="description"  column="description"   />
        <result property="status"       column="status"        />
        <result property="createBy"     column="create_by"     />
        <result property="createTime"   column="create_time"   />
        <result property="updateBy"     column="update_by"     />
        <result property="updateTime"   column="update_time"   />
        <result property="remark"       column="remark"        />
    </resultMap>

    <sql id="selectExampleVo">
        select example_id, example_name, description, status, create_by, create_time, update_by, update_time, remark from example
    </sql>

    <select id="selectExampleList" parameterType="Example" resultMap="ExampleResult">
        <include refid="selectExampleVo"/>
        <where>  
            <if test="exampleName != null  and exampleName != ''"> and example_name like concat('%', #{exampleName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectExampleById" parameterType="Long" resultMap="ExampleResult">
        <include refid="selectExampleVo"/>
        where example_id = #{exampleId}
    </select>
        
    <insert id="insertExample" parameterType="Example" useGeneratedKeys="true" keyProperty="exampleId">
        insert into example
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exampleName != null and exampleName != ''">example_name,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="exampleName != null and exampleName != ''">#{exampleName},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExample" parameterType="Example">
        update example
        <trim prefix="SET" suffixOverrides=",">
            <if test="exampleName != null and exampleName != ''">example_name = #{exampleName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where example_id = #{exampleId}
    </update>

    <delete id="deleteExampleById" parameterType="Long">
        delete from example where example_id = #{exampleId}
    </delete>

    <delete id="deleteExampleByIds" parameterType="String">
        delete from example where example_id in 
        <foreach item="exampleId" collection="array" open="(" separator="," close=")">
            #{exampleId}
        </foreach>
    </delete>
</mapper>
```

### 5. 创建 Service 接口

```java
package com.ruoyi.example.service;

import com.ruoyi.example.domain.Example;
import java.util.List;

public interface IExampleService {
    /**
     * 查询示例列表
     * 
     * @param example 示例信息
     * @return 示例集合
     */
    public List<Example> selectExampleList(Example example);

    /**
     * 查询示例详细
     * 
     * @param exampleId 示例ID
     * @return 示例信息
     */
    public Example selectExampleById(Long exampleId);

    /**
     * 新增示例
     * 
     * @param example 示例信息
     * @return 结果
     */
    public int insertExample(Example example);

    /**
     * 修改示例
     * 
     * @param example 示例信息
     * @return 结果
     */
    public int updateExample(Example example);

    /**
     * 批量删除示例
     * 
     * @param exampleIds 需要删除的示例ID
     * @return 结果
     */
    public int deleteExampleByIds(Long[] exampleIds);

    /**
     * 删除示例信息
     * 
     * @param exampleId 示例ID
     * @return 结果
     */
    public int deleteExampleById(Long exampleId);
}
```

### 6. 创建 Service 实现类

```java
package com.ruoyi.example.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.example.mapper.ExampleMapper;
import com.ruoyi.example.domain.Example;
import com.ruoyi.example.service.IExampleService;

@Service
public class ExampleServiceImpl implements IExampleService {
    @Autowired
    private ExampleMapper exampleMapper;

    @Override
    public List<Example> selectExampleList(Example example) {
        return exampleMapper.selectExampleList(example);
    }

    @Override
    public Example selectExampleById(Long exampleId) {
        return exampleMapper.selectExampleById(exampleId);
    }

    @Override
    public int insertExample(Example example) {
        return exampleMapper.insertExample(example);
    }

    @Override
    public int updateExample(Example example) {
        return exampleMapper.updateExample(example);
    }

    @Override
    public int deleteExampleByIds(Long[] exampleIds) {
        return exampleMapper.deleteExampleByIds(exampleIds);
    }

    @Override
    public int deleteExampleById(Long exampleId) {
        return exampleMapper.deleteExampleById(exampleId);
    }
}
```

### 7. 创建 Controller

```java
package com.ruoyi.example.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.example.domain.Example;
import com.ruoyi.example.service.IExampleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

@RestController
@RequestMapping("/example")
public class ExampleController extends BaseController {
    @Autowired
    private IExampleService exampleService;

    /**
     * 查询示例列表
     */
    @PreAuthorize("@ss.hasPermi('example:example:list')")
    @GetMapping("/list")
    public TableDataInfo list(Example example) {
        startPage();
        List<Example> list = exampleService.selectExampleList(example);
        return getDataTable(list);
    }

    /**
     * 导出示例列表
     */
    @PreAuthorize("@ss.hasPermi('example:example:export')")
    @Log(title = "示例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Example example) {
        List<Example> list = exampleService.selectExampleList(example);
        ExcelUtil<Example> util = new ExcelUtil<Example>(Example.class);
        util.exportExcel(response, list, "示例数据");
    }

    /**
     * 获取示例详细信息
     */
    @PreAuthorize("@ss.hasPermi('example:example:query')")
    @GetMapping(value = "/{exampleId}")
    public AjaxResult getInfo(@PathVariable("exampleId") Long exampleId) {
        return success(exampleService.selectExampleById(exampleId));
    }

    /**
     * 新增示例
     */
    @PreAuthorize("@ss.hasPermi('example:example:add')")
    @Log(title = "示例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Example example) {
        return toAjax(exampleService.insertExample(example));
    }

    /**
     * 修改示例
     */
    @PreAuthorize("@ss.hasPermi('example:example:edit')")
    @Log(title = "示例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Example example) {
        return toAjax(exampleService.updateExample(example));
    }

    /**
     * 删除示例
     */
    @PreAuthorize("@ss.hasPermi('example:example:remove')")
    @Log(title = "示例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exampleIds}")
    public AjaxResult remove(@PathVariable Long[] exampleIds) {
        return toAjax(exampleService.deleteExampleByIds(exampleIds));
    }
}
```

## 前端业务模块开发

### 1. 创建 API 接口文件

在 `ruoyi-ui/src/api/` 下创建 `example.js`：

```javascript
import request from '@/utils/request'

// 查询示例列表
export function listExample(query) {
  return request({
    url: '/example/list',
    method: 'get',
    params: query
  })
}

// 查询示例详细
export function getExample(exampleId) {
  return request({
    url: '/example/' + exampleId,
    method: 'get'
  })
}

// 新增示例
export function addExample(data) {
  return request({
    url: '/example',
    method: 'post',
    data: data
  })
}

// 修改示例
export function updateExample(data) {
  return request({
    url: '/example',
    method: 'put',
    data: data
  })
}

// 删除示例
export function delExample(exampleId) {
  return request({
    url: '/example/' + exampleId,
    method: 'delete'
  })
}

// 导出示例
export function exportExample(query) {
  return request({
    url: '/example/export',
    method: 'post',
    data: query
  })
}
```

### 2. 创建前端页面

在 `ruoyi-ui/src/views/` 下创建 `example/` 目录，并创建 `index.vue`：

```vue
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="示例名称" prop="exampleName">
        <el-input
          v-model="queryParams.exampleName"
          placeholder="请输入示例名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="示例状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['example:example:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['example:example:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['example:example:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['example:example:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="exampleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="示例ID" align="center" prop="exampleId" />
      <el-table-column label="示例名称" align="center" prop="exampleName" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['example:example:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['example:example:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改示例对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="示例名称" prop="exampleName">
          <el-input v-model="form.exampleName" placeholder="请输入示例名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExample, getExample, delExample, addExample, updateExample } from "@/api/example";

export default {
  name: "Example",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 示例表格数据
      exampleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        exampleName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        exampleName: [
          { required: true, message: "示例名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询示例列表 */
    getList() {
      this.loading = true;
      listExample(this.queryParams).then(response => {
        this.exampleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        exampleId: null,
        exampleName: null,
        description: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.exampleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加示例";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const exampleId = row.exampleId || this.ids
      getExample(exampleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改示例";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.exampleId != null) {
            updateExample(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExample(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const exampleIds = row.exampleId || this.ids;
      this.$modal.confirm('是否确认删除示例编号为"' + exampleIds + '"的数据项？').then(function() {
        return delExample(exampleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('example/export', {
        ...this.queryParams
      }, `example_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
```

### 3. 配置路由

在 `ruoyi-ui/src/router/index.js` 中添加路由配置：

```javascript
{
  path: '/example',
  component: Layout,
  hidden: false,
  redirect: 'noredirect',
  name: 'Example',
  meta: {
    title: '示例管理',
    icon: 'example'
  },
  children: [
    {
      path: 'example',
      component: () => import('@/views/example/index'),
      name: 'Example',
      meta: { title: '示例管理', icon: 'example' }
    }
  ]
}
```

## 数据库设计

### 1. 创建数据表

```sql
-- 示例表
CREATE TABLE `example` (
  `example_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '示例ID',
  `example_name` varchar(100) NOT NULL COMMENT '示例名称',
  `description` varchar(500) DEFAULT NULL COMMENT '示例描述',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`example_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```

### 2. 数据库设计规范

- **表名规范**: 使用小写字母，单词间用下划线分隔
- **字段命名**: 使用小写字母，单词间用下划线分隔
- **主键**: 统一使用 `id` 或 `表名_id` 格式
- **时间字段**: 统一使用 `create_time`、`update_time` 格式
- **状态字段**: 统一使用 `status` 字段，0表示正常，1表示停用
- **备注字段**: 统一使用 `remark` 字段

## API接口规范

### 1. RESTful API 设计

- **GET**: 查询数据
- **POST**: 新增数据
- **PUT**: 更新数据
- **DELETE**: 删除数据

### 2. 统一响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 3. 分页查询格式

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [],
  "total": 100
}
```

### 4. 错误码规范

- **200**: 成功
- **401**: 未授权
- **403**: 禁止访问
- **404**: 资源不存在
- **500**: 服务器内部错误

## 权限控制

### 1. 后端权限注解

```java
// 需要特定权限
@PreAuthorize("@ss.hasPermi('example:example:list')")

// 需要特定角色
@PreAuthorize("@ss.hasRole('admin')")

// 内部调用（微服务间调用）
@InnerAuth
```

### 2. 前端权限控制

```vue
<!-- 按钮权限控制 -->
<el-button v-hasPermi="['example:example:add']">新增</el-button>

<!-- 菜单权限控制 -->
<!-- 在路由配置中设置 meta.permissions -->
```

### 3. 权限标识规范

权限标识格式：`模块:功能:操作`

- **模块**: 业务模块名称，如 `example`
- **功能**: 功能名称，如 `example`
- **操作**: 操作类型，如 `list`、`add`、`edit`、`remove`、`export`

## 部署指南

### 1. 后端部署

#### 1.1 打包
```bash
# 在项目根目录执行
mvn clean package -Dmaven.test.skip=true
```

#### 1.2 启动顺序
1. **Nacos** - 注册中心和配置中心
2. **Redis** - 缓存服务
3. **MySQL** - 数据库
4. **Gateway** - 网关服务
5. **Auth** - 认证服务
6. **业务模块** - 各个业务服务

#### 1.3 启动命令
```bash
# 启动业务模块
java -jar ruoyi-modules-example.jar
```

### 2. 前端部署

#### 2.1 构建
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build:prod
```

#### 2.2 部署
将 `dist` 目录下的文件部署到 Web 服务器（如 Nginx）。

### 3. Docker 部署

项目提供了 Docker 部署方案，在 `docker` 目录下有完整的 Docker 配置文件。

## 常见问题

### 1. 服务注册失败
- 检查 Nacos 服务是否正常启动
- 检查网络连接是否正常
- 检查配置文件中的 Nacos 地址是否正确

### 2. 数据库连接失败
- 检查数据库服务是否正常
- 检查数据库连接配置是否正确
- 检查数据库用户权限

### 3. 前端页面无法访问
- 检查后端服务是否正常启动
- 检查网关配置是否正确
- 检查前端 API 地址配置

### 4. 权限验证失败
- 检查用户是否已登录
- 检查用户是否有对应权限
- 检查权限标识是否正确

### 5. 文件上传失败
- 检查文件服务是否正常启动
- 检查文件上传路径配置
- 检查文件大小限制

## 开发建议

### 1. 代码规范
- 遵循阿里巴巴 Java 开发手册
- 使用统一的代码格式化工具
- 编写完整的注释和文档

### 2. 测试
- 编写单元测试
- 进行接口测试
- 进行集成测试

### 3. 性能优化
- 合理使用缓存
- 优化数据库查询
- 使用分页查询

### 4. 安全考虑
- 输入参数验证
- SQL 注入防护
- XSS 攻击防护
- CSRF 攻击防护

### 5. 日志记录
- 记录关键操作日志
- 记录异常信息
- 使用统一的日志格式

---

**参考文档**: [RuoYi-Cloud 官方文档](https://doc.ruoyi.vip/ruoyi-cloud/)

**项目地址**: [RuoYi-Cloud GitHub](https://gitee.com/y_project/RuoYi-Cloud)

**在线演示**: [RuoYi-Cloud 演示地址](http://cloud.ruoyi.vip) 