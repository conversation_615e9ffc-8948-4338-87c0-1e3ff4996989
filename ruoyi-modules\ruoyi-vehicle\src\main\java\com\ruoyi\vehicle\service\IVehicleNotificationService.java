package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleNotification;

/**
 * 消息通知Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleNotificationService 
{
    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    public VehicleNotification selectVehicleNotificationByNotificationId(Long notificationId);

    /**
     * 查询消息通知列表
     * 
     * @param vehicleNotification 消息通知
     * @return 消息通知集合
     */
    public List<VehicleNotification> selectVehicleNotificationList(VehicleNotification vehicleNotification);

    /**
     * 新增消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    public int insertVehicleNotification(VehicleNotification vehicleNotification);

    /**
     * 修改消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    public int updateVehicleNotification(VehicleNotification vehicleNotification);

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的消息通知主键集合
     * @return 结果
     */
    public int deleteVehicleNotificationByNotificationIds(Long[] notificationIds);

    /**
     * 删除消息通知信息
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    public int deleteVehicleNotificationByNotificationId(Long notificationId);

    /**
     * 发送需求计划申请通知
     * 
     * @param planId 需求计划ID
     * @param notifyType 通知类型（submit、approve、reject）
     * @param operName 操作人
     * @return 结果
     */
    public int sendDemandPlanNotification(Long planId, String notifyType, String operName);

    /**
     * 发送用车申请通知
     * 
     * @param applicationId 申请ID
     * @param notifyType 通知类型（submit、approve、reject、assign）
     * @param operName 操作人
     * @return 结果
     */
    public int sendApplicationNotification(Long applicationId, String notifyType, String operName);

    /**
     * 发送订单确认通知
     * 
     * @param orderId 订单ID
     * @param notifyType 通知类型（start、finish、team_confirm、dispatch_confirm、manager_confirm、reject）
     * @param operName 操作人
     * @return 结果
     */
    public int sendOrderNotification(Long orderId, String notifyType, String operName);

    /**
     * 发送钉钉消息
     * 
     * @param notification 通知信息
     * @return 结果
     */
    public boolean sendDingtalkMessage(VehicleNotification notification);

    /**
     * 标记消息为已读
     * 
     * @param notificationId 通知ID
     * @param operName 操作人
     * @return 结果
     */
    public int markAsRead(Long notificationId, String operName);

    /**
     * 根据接收人查询通知列表
     * 
     * @param recipient 接收人
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationByRecipient(String recipient);

    /**
     * 根据业务ID和类型查询通知列表
     * 
     * @param businessId 业务ID
     * @param notificationType 通知类型
     * @return 通知集合
     */
    public List<VehicleNotification> selectNotificationByBusinessIdAndType(Long businessId, String notificationType);

    /**
     * 查询未读通知数量
     * 
     * @param recipient 接收人
     * @return 未读数量
     */
    public int countUnreadNotifications(String recipient);

    /**
     * 重新发送失败的通知
     * 
     * @param notificationId 通知ID
     * @return 结果
     */
    public int resendFailedNotification(Long notificationId);

    /**
     * 批量发送通知
     * 
     * @param notifications 通知列表
     * @return 结果
     */
    public int batchSendNotifications(List<VehicleNotification> notifications);
}
