package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.TeamInfo;

/**
 * 队伍信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface ITeamInfoService 
{
    /**
     * 查询队伍信息
     * 
     * @param teamId 队伍信息主键
     * @return 队伍信息
     */
    public TeamInfo selectTeamInfoByTeamId(Long teamId);

    /**
     * 查询队伍信息列表
     * 
     * @param teamInfo 队伍信息
     * @return 队伍信息集合
     */
    public List<TeamInfo> selectTeamInfoList(TeamInfo teamInfo);

    /**
     * 新增队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    public int insertTeamInfo(TeamInfo teamInfo);

    /**
     * 修改队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    public int updateTeamInfo(TeamInfo teamInfo);

    /**
     * 批量删除队伍信息
     * 
     * @param teamIds 需要删除的队伍信息主键集合
     * @return 结果
     */
    public int deleteTeamInfoByTeamIds(Long[] teamIds);

    /**
     * 删除队伍信息信息
     * 
     * @param teamId 队伍信息主键
     * @return 结果
     */
    public int deleteTeamInfoByTeamId(Long teamId);

    /**
     * 根据队伍类型查询队伍列表
     * 
     * @param teamType 队伍类型
     * @return 队伍集合
     */
    public List<TeamInfo> selectTeamInfoByType(String teamType);

    /**
     * 根据状态查询队伍列表
     * 
     * @param status 状态
     * @return 队伍集合
     */
    public List<TeamInfo> selectTeamInfoByStatus(String status);

    /**
     * 查询活跃队伍列表
     * 
     * @return 队伍集合
     */
    public List<TeamInfo> selectActiveTeamList();

    /**
     * 更新队伍状态
     * 
     * @param teamId 队伍ID
     * @param status 状态
     * @return 结果
     */
    public int updateTeamStatus(Long teamId, String status);
}
