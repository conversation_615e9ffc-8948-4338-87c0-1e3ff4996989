package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 车辆维修记录对象 vehicle_maintenance
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleMaintenance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 维修记录ID */
    private Long maintenanceId;

    /** 车辆ID */
    @Excel(name = "车辆ID")
    private Long vehicleId;

    /** 车辆信息 */
    private VehicleInfo vehicleInfo;

    /** 维修日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "维修日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date maintenanceDate;

    /** 维修类型 */
    @Excel(name = "维修类型")
    private String maintenanceType;

    /** 故障描述 */
    @Excel(name = "故障描述")
    private String faultDescription;

    /** 维修内容 */
    @Excel(name = "维修内容")
    private String maintenanceContent;

    /** 维修费用 */
    @Excel(name = "维修费用")
    private BigDecimal maintenanceCost;

    /** 维修人员 */
    @Excel(name = "维修人员")
    private String maintenancePerson;

    /** 维修公司 */
    @Excel(name = "维修公司")
    private String maintenanceCompany;

    /** 维修状态 */
    @Excel(name = "维修状态", readConverterExp = "维修中=0,已完成=1")
    private String status;

    public void setMaintenanceId(Long maintenanceId) 
    {
        this.maintenanceId = maintenanceId;
    }

    public Long getMaintenanceId() 
    {
        return maintenanceId;
    }

    public void setVehicleId(Long vehicleId) 
    {
        this.vehicleId = vehicleId;
    }

    @NotNull(message = "车辆ID不能为空")
    public Long getVehicleId() 
    {
        return vehicleId;
    }

    public void setVehicleInfo(VehicleInfo vehicleInfo) 
    {
        this.vehicleInfo = vehicleInfo;
    }

    public VehicleInfo getVehicleInfo() 
    {
        return vehicleInfo;
    }

    public void setMaintenanceDate(Date maintenanceDate) 
    {
        this.maintenanceDate = maintenanceDate;
    }

    @NotNull(message = "维修日期不能为空")
    public Date getMaintenanceDate() 
    {
        return maintenanceDate;
    }

    public void setMaintenanceType(String maintenanceType) 
    {
        this.maintenanceType = maintenanceType;
    }

    @NotBlank(message = "维修类型不能为空")
    @Size(min = 0, max = 50, message = "维修类型长度不能超过50个字符")
    public String getMaintenanceType() 
    {
        return maintenanceType;
    }

    public void setFaultDescription(String faultDescription) 
    {
        this.faultDescription = faultDescription;
    }

    public String getFaultDescription() 
    {
        return faultDescription;
    }

    public void setMaintenanceContent(String maintenanceContent) 
    {
        this.maintenanceContent = maintenanceContent;
    }

    public String getMaintenanceContent() 
    {
        return maintenanceContent;
    }

    public void setMaintenanceCost(BigDecimal maintenanceCost) 
    {
        this.maintenanceCost = maintenanceCost;
    }

    public BigDecimal getMaintenanceCost() 
    {
        return maintenanceCost;
    }

    public void setMaintenancePerson(String maintenancePerson) 
    {
        this.maintenancePerson = maintenancePerson;
    }

    @Size(min = 0, max = 50, message = "维修人员长度不能超过50个字符")
    public String getMaintenancePerson() 
    {
        return maintenancePerson;
    }

    public void setMaintenanceCompany(String maintenanceCompany) 
    {
        this.maintenanceCompany = maintenanceCompany;
    }

    @Size(min = 0, max = 200, message = "维修公司长度不能超过200个字符")
    public String getMaintenanceCompany() 
    {
        return maintenanceCompany;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    @Size(min = 0, max = 20, message = "维修状态长度不能超过20个字符")
    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("maintenanceId", getMaintenanceId())
            .append("vehicleId", getVehicleId())
            .append("maintenanceDate", getMaintenanceDate())
            .append("maintenanceType", getMaintenanceType())
            .append("faultDescription", getFaultDescription())
            .append("maintenanceContent", getMaintenanceContent())
            .append("maintenanceCost", getMaintenanceCost())
            .append("maintenancePerson", getMaintenancePerson())
            .append("maintenanceCompany", getMaintenanceCompany())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
