-- =============================================================================
-- RuoYi-Cloud 多数据库初始化脚本
-- =============================================================================
-- 创建两个数据库：ry-cloud 和 nacos
-- 并授权用户访问权限

-- 创建第一个数据库
CREATE DATABASE IF NOT EXISTS `ry-cloud` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建第二个数据库
CREATE DATABASE IF NOT EXISTS `nacos` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权root用户访问两个数据库
GRANT ALL PRIVILEGES ON `ry-cloud`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `nacos`.* TO 'root'@'%';

-- 授权ruoyi用户访问两个数据库
GRANT ALL PRIVILEGES ON `ry-cloud`.* TO 'ruoyi'@'%';
GRANT ALL PRIVILEGES ON `nacos`.* TO 'ruoyi'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 输出创建成功信息
SELECT '数据库 ry-cloud 和 nacos 已成功创建' AS status;