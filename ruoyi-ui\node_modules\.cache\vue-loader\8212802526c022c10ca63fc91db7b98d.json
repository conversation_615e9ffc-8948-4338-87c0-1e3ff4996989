{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\info\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\info\\index.vue", "mtime": 1754138584234}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RWZWhpY2xlSW5mbywgZ2V0VmVoaWNsZUluZm8sIGRlbFZlaGljbGVJbmZvLCBhZGRWZWhpY2xlSW5mbywgdXBkYXRlVmVoaWNsZUluZm8gfSBmcm9tICJAL2FwaS92ZWhpY2xlL2luZm8iOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlZlaGljbGVJbmZvIiwKICBkaWN0czogWyd2ZWhpY2xlX3R5cGUnLCAndmVoaWNsZV9zdGF0dXMnXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOacuuaisOi9pui+huS/oeaBr+ihqOagvOaVsOaNrgogICAgICB2ZWhpY2xlSW5mb0xpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHZlaGljbGVUeXBlOiBudWxsLAogICAgICAgIHZlaGljbGVNb2RlbDogbnVsbCwKICAgICAgICB1bml0TmFtZTogbnVsbCwKICAgICAgICBsaWNlbnNlUGxhdGU6IG51bGwsCiAgICAgICAgdmVoaWNsZVN0YXR1czogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHZlaGljbGVUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L2m6L6G57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgdmVoaWNsZU1vZGVsOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L2m6L6G5Z6L5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHVuaXROYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y2V5L2N5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOi9pui+huS/oeaBr+WvvOWFpeWPguaVsAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjovabovobkv6Hmga/lr7zlhaXvvIkKICAgICAgICBvcGVuOiBmYWxzZSwKICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopjvvIjovabovobkv6Hmga/lr7zlhaXvvIkKICAgICAgICB0aXRsZTogIiIsCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICAgIC8vIOaYr+WQpuabtOaWsOW3sue7j+WtmOWcqOeahOi9pui+huS/oeaBr+aVsOaNrgogICAgICAgIHVwZGF0ZVN1cHBvcnQ6IDAsCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sCiAgICAgICAgLy8g5LiK5Lyg55qE5Zyw5Z2ACiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi92ZWhpY2xlL2luZm8vaW1wb3J0RGF0YSIKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LmnLrmorDovabovobkv6Hmga/liJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RWZWhpY2xlSW5mbyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnZlaGljbGVJbmZvTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdmVoaWNsZUlkOiBudWxsLAogICAgICAgIHZlaGljbGVUeXBlOiBudWxsLAogICAgICAgIHZlaGljbGVNb2RlbDogbnVsbCwKICAgICAgICB1bml0TmFtZTogbnVsbCwKICAgICAgICBsaWNlbnNlUGxhdGU6IG51bGwsCiAgICAgICAgZHJpdmVyTmFtZTogbnVsbCwKICAgICAgICBkcml2ZXJQaG9uZTogbnVsbCwKICAgICAgICBjb21tYW5kZXJOYW1lOiBudWxsLAogICAgICAgIGNvbW1hbmRlclBob25lOiBudWxsLAogICAgICAgIGVudHJ5VGltZTogbnVsbCwKICAgICAgICBwcm9qZWN0UGhhc2U6IG51bGwsCiAgICAgICAgdmVoaWNsZVN0YXR1czogIuWPr+eUqCIsCiAgICAgICAgc2hpZnRDb25maXJtZXI6IG51bGwsCiAgICAgICAgY29zdFVuaXQ6ICLlpKkiLAogICAgICAgIHJlbWFyazogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS52ZWhpY2xlSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5py65qKw6L2m6L6G5L+h5oGvIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgdmVoaWNsZUlkID0gcm93LnZlaGljbGVJZCB8fCB0aGlzLmlkcwogICAgICBnZXRWZWhpY2xlSW5mbyh2ZWhpY2xlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueacuuaisOi9pui+huS/oeaBryI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLnZlaGljbGVJZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVZlaGljbGVJbmZvKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkVmVoaWNsZUluZm8odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IHZlaGljbGVJZHMgPSByb3cudmVoaWNsZUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmnLrmorDovabovobkv6Hmga/nvJblj7fkuLoiJyArIHZlaGljbGVJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbFZlaGljbGVJbmZvKHZlaGljbGVJZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgndmVoaWNsZS9pbmZvL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGB2ZWhpY2xlX2luZm9fJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLovabovobkv6Hmga/lr7zlhaUiOwogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5LiL6L295qih5p2/5pON5L2cICovCiAgICBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgndmVoaWNsZS9pbmZvL2ltcG9ydFRlbXBsYXRlJywge30sIGB2ZWhpY2xlX2luZm9fdGVtcGxhdGVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5Lit5aSE55CGCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgdGhpcy4kYWxlcnQoIjxkaXYgc3R5bGU9J292ZXJmbG93OiBhdXRvO292ZXJmbG93LXg6IGhpZGRlbjttYXgtaGVpZ2h0OiA3MHZoO3BhZGRpbmc6IDEwcHggMjBweCAwOyc+IiArIHJlc3BvbnNlLm1zZyArICI8L2Rpdj4iLCAi5a+85YWl57uT5p6cIiwgeyBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUgfSk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8vIOaPkOS6pOS4iuS8oOaWh+S7tgogICAgc3VibWl0RmlsZUZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgfSwKICAgIC8qKiDmm7TlpJrmk43kvZwgKi8KICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgInZpb2xhdGlvbiI6CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3ZlaGljbGUvdmlvbGF0aW9uP3ZlaGljbGVJZD0iICsgcm93LnZlaGljbGVJZCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJtYWludGVuYW5jZSI6CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3ZlaGljbGUvbWFpbnRlbmFuY2U/dmVoaWNsZUlkPSIgKyByb3cudmVoaWNsZUlkKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgInN0YXR1cyI6CiAgICAgICAgICB0aGlzLmhhbmRsZVN0YXR1c0NoYW5nZShyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvKiog54q25oCB566h55CGICovCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnZlaGljbGVTdGF0dXMgPT09ICLlj6/nlKgiID8gIuWBnOeUqCIgOiAi5ZCv55SoIjsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJyIiJyArIHJvdy52ZWhpY2xlTW9kZWwgKyAnIui9pui+huWQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIHVwZGF0ZVZlaGljbGVTdGF0dXMocm93LnZlaGljbGVJZCwgcm93LnZlaGljbGVTdGF0dXMgPT09ICLlj6/nlKgiID8gIuaVhemanCIgOiAi5Y+v55SoIik7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/info", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n        <el-input\n          v-model=\"queryParams.vehicleModel\"\n          placeholder=\"请输入车辆型号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"单位名称\" prop=\"unitName\">\n        <el-input\n          v-model=\"queryParams.unitName\"\n          placeholder=\"请输入单位名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车牌号码\" prop=\"licensePlate\">\n        <el-input\n          v-model=\"queryParams.licensePlate\"\n          placeholder=\"请输入车牌号码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆状态\" prop=\"vehicleStatus\">\n        <el-select v-model=\"queryParams.vehicleStatus\" placeholder=\"请选择车辆状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:info:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:info:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:info:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:info:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['vehicle:info:import']\"\n        >导入</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 车辆信息表格 -->\n    <el-table v-loading=\"loading\" :data=\"vehicleInfoList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"车辆ID\" align=\"center\" prop=\"vehicleId\" />\n      <el-table-column label=\"车辆类型\" align=\"center\" prop=\"vehicleType\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.vehicle_type\" :value=\"scope.row.vehicleType\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"车辆型号\" align=\"center\" prop=\"vehicleModel\" />\n      <el-table-column label=\"单位名称\" align=\"center\" prop=\"unitName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"licensePlate\" />\n      <el-table-column label=\"司机姓名\" align=\"center\" prop=\"driverName\" />\n      <el-table-column label=\"司机电话\" align=\"center\" prop=\"driverPhone\" />\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"vehicleStatus\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.vehicle_status\" :value=\"scope.row.vehicleStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"入场时间\" align=\"center\" prop=\"entryTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.entryTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:info:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:info:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['vehicle:info:edit']\">\n            <span class=\"el-dropdown-link\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"violation\" icon=\"el-icon-warning\">违章记录</el-dropdown-item>\n              <el-dropdown-item command=\"maintenance\" icon=\"el-icon-setting\">维修记录</el-dropdown-item>\n              <el-dropdown-item command=\"status\" icon=\"el-icon-refresh\">状态管理</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改机械车辆信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\">\n                <el-option\n                  v-for=\"dict in dict.type.vehicle_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-input v-model=\"form.vehicleModel\" placeholder=\"请输入车辆型号\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"单位名称\" prop=\"unitName\">\n              <el-input v-model=\"form.unitName\" placeholder=\"请输入单位名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车牌号码\" prop=\"licensePlate\">\n              <el-input v-model=\"form.licensePlate\" placeholder=\"请输入车牌号码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"司机姓名\" prop=\"driverName\">\n              <el-input v-model=\"form.driverName\" placeholder=\"请输入司机姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"司机电话\" prop=\"driverPhone\">\n              <el-input v-model=\"form.driverPhone\" placeholder=\"请输入司机电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"指挥姓名\" prop=\"commanderName\">\n              <el-input v-model=\"form.commanderName\" placeholder=\"请输入指挥姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"指挥电话\" prop=\"commanderPhone\">\n              <el-input v-model=\"form.commanderPhone\" placeholder=\"请输入指挥电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"入场时间\" prop=\"entryTime\">\n              <el-date-picker clearable\n                v-model=\"form.entryTime\"\n                type=\"datetime\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"请选择入场时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"项目期\" prop=\"projectPhase\">\n              <el-select v-model=\"form.projectPhase\" placeholder=\"请选择项目期\">\n                <el-option label=\"一期\" value=\"一期\"></el-option>\n                <el-option label=\"二期\" value=\"二期\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆状态\" prop=\"vehicleStatus\">\n              <el-select v-model=\"form.vehicleStatus\" placeholder=\"请选择车辆状态\">\n                <el-option\n                  v-for=\"dict in dict.type.vehicle_status\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"费用计量单位\" prop=\"costUnit\">\n              <el-select v-model=\"form.costUnit\" placeholder=\"请选择费用计量单位\">\n                <el-option label=\"天\" value=\"天\"></el-option>\n                <el-option label=\"小时\" value=\"小时\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"台班确认人\" prop=\"shiftConfirmer\">\n          <el-input v-model=\"form.shiftConfirmer\" type=\"textarea\" placeholder=\"请输入台班确认人，多个用逗号分隔\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 车辆信息导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\n          <div class=\"el-upload__tip\" slot=\"tip\">\n            仅允许导入xls、xlsx格式文件。\n            <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\n          </div>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listVehicleInfo, getVehicleInfo, delVehicleInfo, addVehicleInfo, updateVehicleInfo } from \"@/api/vehicle/info\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"VehicleInfo\",\n  dicts: ['vehicle_type', 'vehicle_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 机械车辆信息表格数据\n      vehicleInfoList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleType: null,\n        vehicleModel: null,\n        unitName: null,\n        licensePlate: null,\n        vehicleStatus: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        vehicleType: [\n          { required: true, message: \"车辆类型不能为空\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"车辆型号不能为空\", trigger: \"blur\" }\n        ],\n        unitName: [\n          { required: true, message: \"单位名称不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 车辆信息导入参数\n      upload: {\n        // 是否显示弹出层（车辆信息导入）\n        open: false,\n        // 弹出层标题（车辆信息导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的车辆信息数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/vehicle/info/importData\"\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询机械车辆信息列表 */\n    getList() {\n      this.loading = true;\n      listVehicleInfo(this.queryParams).then(response => {\n        this.vehicleInfoList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        vehicleId: null,\n        vehicleType: null,\n        vehicleModel: null,\n        unitName: null,\n        licensePlate: null,\n        driverName: null,\n        driverPhone: null,\n        commanderName: null,\n        commanderPhone: null,\n        entryTime: null,\n        projectPhase: null,\n        vehicleStatus: \"可用\",\n        shiftConfirmer: null,\n        costUnit: \"天\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.vehicleId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加机械车辆信息\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const vehicleId = row.vehicleId || this.ids\n      getVehicleInfo(vehicleId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改机械车辆信息\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.vehicleId != null) {\n            updateVehicleInfo(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addVehicleInfo(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const vehicleIds = row.vehicleId || this.ids;\n      this.$modal.confirm('是否确认删除机械车辆信息编号为\"' + vehicleIds + '\"的数据项？').then(function() {\n        return delVehicleInfo(vehicleIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/info/export', {\n        ...this.queryParams\n      }, `vehicle_info_${new Date().getTime()}.xlsx`)\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"车辆信息导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.download('vehicle/info/importTemplate', {}, `vehicle_info_template_${new Date().getTime()}.xlsx`)\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    /** 更多操作 */\n    handleCommand(command, row) {\n      switch (command) {\n        case \"violation\":\n          this.$router.push(\"/vehicle/violation?vehicleId=\" + row.vehicleId);\n          break;\n        case \"maintenance\":\n          this.$router.push(\"/vehicle/maintenance?vehicleId=\" + row.vehicleId);\n          break;\n        case \"status\":\n          this.handleStatusChange(row);\n          break;\n      }\n    },\n    /** 状态管理 */\n    handleStatusChange(row) {\n      let text = row.vehicleStatus === \"可用\" ? \"停用\" : \"启用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.vehicleModel + '\"车辆吗？').then(function() {\n        return updateVehicleStatus(row.vehicleId, row.vehicleStatus === \"可用\" ? \"故障\" : \"可用\");\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"]}]}