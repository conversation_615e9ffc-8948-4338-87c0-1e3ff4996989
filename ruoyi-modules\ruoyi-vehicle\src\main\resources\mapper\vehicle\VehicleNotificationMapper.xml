<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleNotificationMapper">
    
    <resultMap type="VehicleNotification" id="VehicleNotificationResult">
        <result property="notificationId"    column="notification_id"    />
        <result property="notificationType"  column="notification_type"  />
        <result property="businessId"        column="business_id"        />
        <result property="title"             column="title"             />
        <result property="content"           column="content"           />
        <result property="recipient"         column="recipient"         />
        <result property="recipientPhone"    column="recipient_phone"    />
        <result property="sendStatus"        column="send_status"        />
        <result property="sendTime"          column="send_time"          />
        <result property="readStatus"        column="read_status"        />
        <result property="readTime"          column="read_time"          />
        <result property="dingtalkMsgId"     column="dingtalk_msg_id"     />
        <result property="createBy"          column="create_by"          />
        <result property="createTime"        column="create_time"        />
        <result property="updateBy"          column="update_by"          />
        <result property="updateTime"        column="update_time"        />
        <result property="remark"            column="remark"            />
    </resultMap>

    <sql id="selectVehicleNotificationVo">
        select notification_id, notification_type, business_id, title, content, recipient, recipient_phone, send_status, send_time, read_status, read_time, dingtalk_msg_id, create_by, create_time, update_by, update_time, remark from vehicle_notification
    </sql>

    <select id="selectVehicleNotificationList" parameterType="VehicleNotification" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        <where>  
            <if test="notificationType != null  and notificationType != ''"> and notification_type = #{notificationType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="recipient != null  and recipient != ''"> and recipient like concat('%', #{recipient}, '%')</if>
            <if test="sendStatus != null  and sendStatus != ''"> and send_status = #{sendStatus}</if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleNotificationByNotificationId" parameterType="Long" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        where notification_id = #{notificationId}
    </select>

    <select id="selectNotificationByRecipient" parameterType="String" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        where recipient = #{recipient}
        order by create_time desc
    </select>

    <select id="selectNotificationByBusinessIdAndType" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        where business_id = #{businessId} and notification_type = #{notificationType}
        order by create_time desc
    </select>

    <select id="countUnreadNotifications" parameterType="String" resultType="int">
        select count(*) from vehicle_notification 
        where recipient = #{recipient} and read_status = 'unread'
    </select>

    <select id="selectNotificationBySendStatus" parameterType="String" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        where send_status = #{sendStatus}
        order by create_time desc
    </select>

    <select id="selectNotificationByReadStatus" resultMap="VehicleNotificationResult">
        <include refid="selectVehicleNotificationVo"/>
        where read_status = #{readStatus}
        <if test="recipient != null and recipient != ''">
            and recipient = #{recipient}
        </if>
        order by create_time desc
    </select>
        
    <insert id="insertVehicleNotification" parameterType="VehicleNotification" useGeneratedKeys="true" keyProperty="notificationId">
        insert into vehicle_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">notification_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="recipient != null and recipient != ''">recipient,</if>
            <if test="recipientPhone != null">recipient_phone,</if>
            <if test="sendStatus != null">send_status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="readTime != null">read_time,</if>
            <if test="dingtalkMsgId != null">dingtalk_msg_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">#{notificationType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="recipient != null and recipient != ''">#{recipient},</if>
            <if test="recipientPhone != null">#{recipientPhone},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="dingtalkMsgId != null">#{dingtalkMsgId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleNotification" parameterType="VehicleNotification">
        update vehicle_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">notification_type = #{notificationType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="recipient != null and recipient != ''">recipient = #{recipient},</if>
            <if test="recipientPhone != null">recipient_phone = #{recipientPhone},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="dingtalkMsgId != null">dingtalk_msg_id = #{dingtalkMsgId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notification_id = #{notificationId}
    </update>

    <delete id="deleteVehicleNotificationByNotificationId" parameterType="Long">
        delete from vehicle_notification where notification_id = #{notificationId}
    </delete>

    <delete id="deleteVehicleNotificationByNotificationIds" parameterType="String">
        delete from vehicle_notification where notification_id in 
        <foreach item="notificationId" collection="array" open="(" separator="," close=")">
            #{notificationId}
        </foreach>
    </delete>
</mapper>
