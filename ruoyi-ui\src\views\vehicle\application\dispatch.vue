<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">车辆调度安排</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
      </div>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待调度申请</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.availableCount }}</div>
              <div class="stat-label">可用车辆</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.busyCount }}</div>
              <div class="stat-label">使用中车辆</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.todayDispatchCount }}</div>
              <div class="stat-label">今日调度</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="车辆类型" prop="vehicleType">
          <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable @change="getList">
            <el-option
              v-for="dict in dict.type.vehicle_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态" prop="approvalStatus">
          <el-select v-model="queryParams.approvalStatus" placeholder="请选择状态" clearable @change="getList">
            <el-option label="待审批" value="pending"></el-option>
            <el-option label="已审批" value="approved"></el-option>
            <el-option label="待调度" value="approved"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度" prop="urgency">
          <el-select v-model="queryParams.urgency" placeholder="请选择紧急程度" clearable @change="getList">
            <el-option label="紧急" value="urgent"></el-option>
            <el-option label="普通" value="normal"></el-option>
            <el-option label="不急" value="low"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-s-operation"
            size="mini"
            :disabled="multiple"
            @click="handleBatchDispatch"
            v-hasPermi="['vehicle:application:dispatch']"
          >批量调度</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchApprove"
            v-hasPermi="['vehicle:application:approve']"
          >批量审批</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-view"
            size="mini"
            @click="showVehicleStatus"
          >车辆状态</el-button>
        </el-col>
      </el-row>

      <!-- 申请列表 -->
      <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请ID" align="center" prop="applicationId" width="80" />
        <el-table-column label="申请标题" align="center" prop="applicationTitle" :show-overflow-tooltip="true" />
        <el-table-column label="车辆需求" align="center" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.vehicleType }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.vehicleModel }}</div>
          </template>
        </el-table-column>
        <el-table-column label="用车时间" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ parseTime(scope.row.startTime, '{m}-{d} {h}:{i}') }}</div>
            <div style="color: #909399; font-size: 12px;">
              至 {{ parseTime(scope.row.endTime, '{m}-{d} {h}:{i}') }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请人" align="center" prop="applicant" width="100" />
        <el-table-column label="用车地点" align="center" prop="usageLocation" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="approvalStatus" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.approvalStatus)" size="mini">
              {{ getStatusText(scope.row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分配情况" align="center" width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.assignedVehicleId">
              <div style="color: #67C23A;">已分配车辆</div>
              <div style="color: #909399; font-size: 12px;">{{ scope.row.assignedDriver }}</div>
            </div>
            <div v-else style="color: #E6A23C;">待分配</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              v-if="scope.row.approvalStatus === 'approved' && !scope.row.assignedVehicleId"
              size="mini"
              type="text"
              icon="el-icon-s-operation"
              @click="handleDispatch(scope.row)"
              v-hasPermi="['vehicle:application:dispatch']"
            >调度</el-button>
            <el-button
              v-if="scope.row.approvalStatus === 'pending'"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleApprove(scope.row)"
              v-hasPermi="['vehicle:application:approve']"
            >审批</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 调度对话框 -->
    <el-dialog title="车辆调度" :visible.sync="dispatchDialogVisible" width="800px" append-to-body>
      <el-form ref="dispatchForm" :model="dispatchForm" :rules="dispatchRules" label-width="100px">
        <el-form-item label="申请信息">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="申请标题">{{ currentApplication.applicationTitle }}</el-descriptions-item>
            <el-descriptions-item label="车辆需求">{{ currentApplication.vehicleType }} - {{ currentApplication.vehicleModel }}</el-descriptions-item>
            <el-descriptions-item label="用车时间">{{ parseTime(currentApplication.startTime) }} 至 {{ parseTime(currentApplication.endTime) }}</el-descriptions-item>
            <el-descriptions-item label="用车地点">{{ currentApplication.usageLocation }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        
        <el-form-item label="分配车辆" prop="assignedVehicleId">
          <el-select v-model="dispatchForm.assignedVehicleId" placeholder="请选择车辆" @change="handleVehicleChange">
            <el-option
              v-for="vehicle in availableVehicles"
              :key="vehicle.vehicleId"
              :label="`${vehicle.vehicleModel} (${vehicle.licensePlate})`"
              :value="vehicle.vehicleId">
              <span style="float: left">{{ vehicle.vehicleModel }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ vehicle.licensePlate }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="分配司机" prop="assignedDriver">
          <el-select v-model="dispatchForm.assignedDriver" placeholder="请选择司机">
            <el-option
              v-for="driver in availableDrivers"
              :key="driver.driverName"
              :label="`${driver.driverName} (${driver.driverPhone})`"
              :value="driver.driverName">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="调度备注" prop="dispatchRemark">
          <el-input
            v-model="dispatchForm.dispatchRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入调度备注"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dispatchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDispatch" :loading="dispatchLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 车辆状态对话框 -->
    <el-dialog title="车辆状态总览" :visible.sync="statusDialogVisible" width="1000px">
      <el-table :data="vehicleStatusList" v-loading="statusLoading">
        <el-table-column label="车辆类型" prop="vehicleType" />
        <el-table-column label="车辆型号" prop="vehicleModel" />
        <el-table-column label="车牌号" prop="licensePlate" />
        <el-table-column label="司机" prop="driverName" />
        <el-table-column label="状态" prop="vehicleStatus">
          <template slot-scope="scope">
            <el-tag :type="getVehicleStatusType(scope.row.vehicleStatus)">
              {{ getVehicleStatusText(scope.row.vehicleStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="当前位置" prop="currentLocation" />
        <el-table-column label="下次可用时间" prop="nextAvailableTime">
          <template slot-scope="scope">
            <span v-if="scope.row.nextAvailableTime">{{ parseTime(scope.row.nextAvailableTime) }}</span>
            <span v-else style="color: #67C23A;">立即可用</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { listApplication, assignVehicle, batchAssignVehicle, getAvailableVehicles } from "@/api/vehicle/application";
import { listInfo } from "@/api/vehicle/info";

export default {
  name: "ApplicationDispatch",
  dicts: ['vehicle_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 申请列表
      applicationList: [],
      // 统计信息
      statistics: {
        pendingCount: 0,
        availableCount: 0,
        busyCount: 0,
        todayDispatchCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleType: null,
        approvalStatus: 'approved'
      },
      // 调度对话框
      dispatchDialogVisible: false,
      dispatchLoading: false,
      currentApplication: {},
      dispatchForm: {
        assignedVehicleId: null,
        assignedDriver: '',
        dispatchRemark: ''
      },
      availableVehicles: [],
      availableDrivers: [],
      // 车辆状态对话框
      statusDialogVisible: false,
      statusLoading: false,
      vehicleStatusList: [],
      // 调度表单验证规则
      dispatchRules: {
        assignedVehicleId: [
          { required: true, message: "请选择分配车辆", trigger: "change" }
        ],
        assignedDriver: [
          { required: true, message: "请选择分配司机", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询申请列表 */
    getList() {
      this.loading = true;
      listApplication(this.queryParams).then(response => {
        this.applicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 加载统计信息 */
    loadStatistics() {
      // TODO: 调用统计接口
      this.statistics = {
        pendingCount: 12,
        availableCount: 8,
        busyCount: 15,
        todayDispatchCount: 6
      };
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.multiple = !selection.length
    },
    
    /** 查看申请详情 */
    handleView(row) {
      this.$router.push('/vehicle/application/detail/' + row.applicationId);
    },
    
    /** 调度按钮操作 */
    async handleDispatch(row) {
      this.currentApplication = row;
      this.dispatchDialogVisible = true;
      
      // 加载可用车辆和司机
      try {
        const vehicleRes = await getAvailableVehicles({
          vehicleType: row.vehicleType,
          vehicleModel: row.vehicleModel,
          startTime: row.startTime,
          endTime: row.endTime
        });
        this.availableVehicles = vehicleRes.data;
        
        // 加载司机列表
        const driverRes = await listInfo({ vehicleType: row.vehicleType });
        this.availableDrivers = driverRes.rows.filter(v => v.driverName).map(v => ({
          driverName: v.driverName,
          driverPhone: v.driverPhone
        }));
      } catch (error) {
        this.$modal.msgError("加载可用资源失败");
      }
    },
    
    /** 车辆变化处理 */
    handleVehicleChange(vehicleId) {
      const selectedVehicle = this.availableVehicles.find(v => v.vehicleId === vehicleId);
      if (selectedVehicle && selectedVehicle.driverName) {
        this.dispatchForm.assignedDriver = selectedVehicle.driverName;
      }
    },
    
    /** 提交调度 */
    submitDispatch() {
      this.$refs["dispatchForm"].validate(valid => {
        if (valid) {
          this.dispatchLoading = true;
          const data = {
            ...this.dispatchForm,
            applicationId: this.currentApplication.applicationId
          };
          
          assignVehicle(data).then(response => {
            this.$modal.msgSuccess("调度成功");
            this.dispatchDialogVisible = false;
            this.getList();
          }).catch(() => {
            this.dispatchLoading = false;
          });
        }
      });
    },
    
    /** 批量调度 */
    handleBatchDispatch() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要调度的申请");
        return;
      }
      this.$modal.msgInfo("批量调度功能开发中...");
    },
    
    /** 批量审批 */
    handleBatchApprove() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审批的申请");
        return;
      }
      this.$modal.msgInfo("批量审批功能开发中...");
    },
    
    /** 显示车辆状态 */
    async showVehicleStatus() {
      this.statusDialogVisible = true;
      this.statusLoading = true;
      
      try {
        const response = await listInfo();
        this.vehicleStatusList = response.rows;
      } catch (error) {
        this.$modal.msgError("获取车辆状态失败");
      } finally {
        this.statusLoading = false;
      }
    },
    
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'dispatched': 'info'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已审批',
        'rejected': '已拒绝',
        'dispatched': '已调度'
      };
      return statusMap[status] || '未知';
    },
    
    /** 获取车辆状态类型 */
    getVehicleStatusType(status) {
      const statusMap = {
        'available': 'success',
        'busy': 'warning',
        'maintenance': 'info',
        'fault': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    /** 获取车辆状态文本 */
    getVehicleStatusText(status) {
      const statusMap = {
        'available': '可用',
        'busy': '使用中',
        'maintenance': '维护中',
        'fault': '故障'
      };
      return statusMap[status] || '未知';
    },
    
    /** 返回列表 */
    goBack() {
      this.$router.push('/vehicle/application');
    }
  }
};
</script>

<style scoped>
.box-card {
  margin: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
