package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.TeamInfo;
import com.ruoyi.vehicle.service.ITeamInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 队伍信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/team")
public class TeamInfoController extends BaseController
{
    @Autowired
    private ITeamInfoService teamInfoService;

    /**
     * 查询队伍信息列表
     */
    @RequiresPermissions("vehicle:team:list")
    @GetMapping("/list")
    public TableDataInfo list(TeamInfo teamInfo)
    {
        startPage();
        List<TeamInfo> list = teamInfoService.selectTeamInfoList(teamInfo);
        return getDataTable(list);
    }

    /**
     * 导出队伍信息列表
     */
    @RequiresPermissions("vehicle:team:export")
    @Log(title = "队伍信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TeamInfo teamInfo)
    {
        List<TeamInfo> list = teamInfoService.selectTeamInfoList(teamInfo);
        ExcelUtil<TeamInfo> util = new ExcelUtil<TeamInfo>(TeamInfo.class);
        util.exportExcel(response, list, "队伍信息数据");
    }

    /**
     * 获取队伍信息详细信息
     */
    @RequiresPermissions("vehicle:team:query")
    @GetMapping(value = "/{teamId}")
    public AjaxResult getInfo(@PathVariable("teamId") Long teamId)
    {
        return success(teamInfoService.selectTeamInfoByTeamId(teamId));
    }

    /**
     * 新增队伍信息
     */
    @RequiresPermissions("vehicle:team:add")
    @Log(title = "队伍信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeamInfo teamInfo)
    {
        teamInfo.setCreateBy(SecurityUtils.getUsername());
        return toAjax(teamInfoService.insertTeamInfo(teamInfo));
    }

    /**
     * 修改队伍信息
     */
    @RequiresPermissions("vehicle:team:edit")
    @Log(title = "队伍信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeamInfo teamInfo)
    {
        teamInfo.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(teamInfoService.updateTeamInfo(teamInfo));
    }

    /**
     * 删除队伍信息
     */
    @RequiresPermissions("vehicle:team:remove")
    @Log(title = "队伍信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{teamIds}")
    public AjaxResult remove(@PathVariable Long[] teamIds)
    {
        return toAjax(teamInfoService.deleteTeamInfoByTeamIds(teamIds));
    }

    /**
     * 根据队伍类型查询队伍列表
     */
    @RequiresPermissions("vehicle:team:list")
    @GetMapping("/type/{teamType}")
    public AjaxResult getByType(@PathVariable String teamType)
    {
        List<TeamInfo> list = teamInfoService.selectTeamInfoByType(teamType);
        return success(list);
    }

    /**
     * 根据状态查询队伍列表
     */
    @RequiresPermissions("vehicle:team:list")
    @GetMapping("/status/{status}")
    public AjaxResult getByStatus(@PathVariable String status)
    {
        List<TeamInfo> list = teamInfoService.selectTeamInfoByStatus(status);
        return success(list);
    }

    /**
     * 查询活跃队伍列表
     */
    @RequiresPermissions("vehicle:team:list")
    @GetMapping("/active")
    public AjaxResult getActiveTeams()
    {
        List<TeamInfo> list = teamInfoService.selectActiveTeamList();
        return success(list);
    }

    /**
     * 更新队伍状态
     */
    @RequiresPermissions("vehicle:team:edit")
    @Log(title = "更新队伍状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{teamId}/{status}")
    public AjaxResult updateStatus(@PathVariable Long teamId, @PathVariable String status)
    {
        return toAjax(teamInfoService.updateTeamStatus(teamId, status));
    }

    /**
     * 获取队伍下拉选项
     */
    @RequiresPermissions("vehicle:team:list")
    @GetMapping("/options")
    public AjaxResult getTeamOptions()
    {
        List<TeamInfo> list = teamInfoService.selectActiveTeamList();
        return success(list);
    }
}
