<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleStatisticsMapper">
    
    <resultMap type="VehicleStatistics" id="VehicleStatisticsResult">
        <result property="statisticsId"    column="statistics_id"    />
        <result property="statisticsType"    column="statistics_type"    />
        <result property="statisticsDate"    column="statistics_date"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="teamId"    column="team_id"    />
        <result property="usageCount"    column="usage_count"    />
        <result property="usageHours"    column="usage_hours"    />
        <result property="totalCost"    column="total_cost"    />
        <result property="averageCost"    column="average_cost"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleStatisticsVo">
        select statistics_id, statistics_type, statistics_date, vehicle_type, team_id, 
               usage_count, usage_hours, total_cost, average_cost, 
               create_by, create_time, update_by, update_time, remark 
        from vehicle_statistics
    </sql>

    <select id="selectVehicleStatisticsByStatisticsId" parameterType="Long" resultMap="VehicleStatisticsResult">
        <include refid="selectVehicleStatisticsVo"/>
        where statistics_id = #{statisticsId}
    </select>

    <select id="selectVehicleStatisticsList" parameterType="VehicleStatistics" resultMap="VehicleStatisticsResult">
        <include refid="selectVehicleStatisticsVo"/>
        <where>
            <if test="statisticsType != null  and statisticsType != ''"> and statistics_type = #{statisticsType}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="params.beginStatisticsDate != null and params.beginStatisticsDate != ''"><!-- 开始时间检索 -->
                and date_format(statistics_date,'%y%m%d') &gt;= date_format(#{params.beginStatisticsDate},'%y%m%d')
            </if>
            <if test="params.endStatisticsDate != null and params.endStatisticsDate != ''"><!-- 结束时间检索 -->
                and date_format(statistics_date,'%y%m%d') &lt;= date_format(#{params.endStatisticsDate},'%y%m%d')
            </if>
        </where>
        order by statistics_date desc
    </select>

    <!-- 车辆使用情况统计 -->
    <select id="getVehicleUsageStatistics" resultType="map">
        SELECT 
            vi.vehicle_type,
            vi.vehicle_model,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            AVG(vo.actual_duration) as avg_hours,
            SUM(vo.total_cost) as total_cost,
            AVG(vo.total_cost) as avg_cost
        FROM vehicle_info vi
        LEFT JOIN vehicle_order vo ON vi.vehicle_id = vo.vehicle_id
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        <if test="vehicleType != null and vehicleType != ''">
            AND vi.vehicle_type = #{vehicleType}
        </if>
        GROUP BY vi.vehicle_type, vi.vehicle_model
        ORDER BY usage_count DESC
    </select>

    <!-- 队伍维度统计 -->
    <select id="getTeamUsageStatistics" resultType="map">
        SELECT 
            ti.team_name,
            ti.team_leader,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            SUM(vo.total_cost) as total_cost,
            DATE_FORMAT(vo.actual_start_time, '%Y-%m') as usage_month
        FROM team_info ti
        LEFT JOIN vehicle_order vo ON ti.team_id = vo.team_id
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        <if test="teamId != null">
            AND ti.team_id = #{teamId}
        </if>
        GROUP BY ti.team_id, ti.team_name, ti.team_leader, DATE_FORMAT(vo.actual_start_time, '%Y-%m')
        ORDER BY usage_month DESC, usage_count DESC
    </select>

    <!-- 出租单位统计 -->
    <select id="getRentalUnitStatistics" resultType="map">
        SELECT 
            vi.unit_name,
            COUNT(DISTINCT vi.vehicle_id) as vehicle_count,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            SUM(vo.total_cost) as total_cost,
            AVG(vo.total_cost) as avg_cost
        FROM vehicle_info vi
        LEFT JOIN vehicle_order vo ON vi.vehicle_id = vo.vehicle_id
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        GROUP BY vi.unit_name
        ORDER BY total_cost DESC
    </select>

    <!-- 作业区域统计 -->
    <select id="getWorkAreaStatistics" resultType="map">
        SELECT 
            vo.usage_location as work_area,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            SUM(vo.total_cost) as total_cost,
            COUNT(DISTINCT vo.vehicle_id) as vehicle_count,
            COUNT(DISTINCT vo.team_id) as team_count
        FROM vehicle_order vo
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        GROUP BY vo.usage_location
        ORDER BY usage_count DESC
    </select>

    <!-- 费用单位分析 -->
    <select id="getCostUnitAnalysis" resultType="map">
        SELECT 
            vo.cost_unit,
            vo.cost_bearer,
            COUNT(vo.order_id) as order_count,
            SUM(vo.total_cost) as total_cost,
            AVG(vo.total_cost) as avg_cost,
            AVG(vo.unit_price) as avg_unit_price,
            SUM(vo.actual_duration) as total_duration
        FROM vehicle_order vo
        WHERE vo.order_status = 'completed'
        AND vo.total_cost IS NOT NULL
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        <if test="costBearer != null and costBearer != ''">
            AND vo.cost_bearer = #{costBearer}
        </if>
        GROUP BY vo.cost_unit, vo.cost_bearer
        ORDER BY total_cost DESC
    </select>

    <!-- 月度趋势统计 -->
    <select id="getMonthlyTrendStatistics" resultType="map">
        SELECT 
            DATE_FORMAT(vo.actual_start_time, '%Y-%m') as month,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            SUM(vo.total_cost) as total_cost,
            COUNT(DISTINCT vo.vehicle_id) as vehicle_count,
            COUNT(DISTINCT vo.team_id) as team_count
        FROM vehicle_order vo
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        GROUP BY DATE_FORMAT(vo.actual_start_time, '%Y-%m')
        ORDER BY month DESC
    </select>

    <!-- 车辆效率分析 -->
    <select id="getVehicleEfficiencyAnalysis" resultType="map">
        SELECT 
            vi.vehicle_id,
            vi.vehicle_model,
            vi.license_plate,
            COUNT(vo.order_id) as usage_count,
            SUM(vo.actual_duration) as total_hours,
            SUM(vo.total_cost) as total_cost,
            AVG(vo.actual_duration) as avg_duration,
            (SUM(vo.actual_duration) / COUNT(vo.order_id)) as efficiency_ratio
        FROM vehicle_info vi
        LEFT JOIN vehicle_order vo ON vi.vehicle_id = vo.vehicle_id
        WHERE vo.order_status = 'completed'
        <if test="startDate != null and startDate != ''">
            AND vo.actual_start_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND vo.actual_end_time &lt;= #{endDate}
        </if>
        <if test="vehicleType != null and vehicleType != ''">
            AND vi.vehicle_type = #{vehicleType}
        </if>
        GROUP BY vi.vehicle_id, vi.vehicle_model, vi.license_plate
        HAVING usage_count > 0
        ORDER BY efficiency_ratio DESC
    </select>

    <insert id="insertVehicleStatistics" parameterType="VehicleStatistics" useGeneratedKeys="true" keyProperty="statisticsId">
        insert into vehicle_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statisticsType != null and statisticsType != ''">statistics_type,</if>
            <if test="statisticsDate != null">statistics_date,</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="teamId != null">team_id,</if>
            <if test="usageCount != null">usage_count,</if>
            <if test="usageHours != null">usage_hours,</if>
            <if test="totalCost != null">total_cost,</if>
            <if test="averageCost != null">average_cost,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statisticsType != null and statisticsType != ''">#{statisticsType},</if>
            <if test="statisticsDate != null">#{statisticsDate},</if>
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="usageCount != null">#{usageCount},</if>
            <if test="usageHours != null">#{usageHours},</if>
            <if test="totalCost != null">#{totalCost},</if>
            <if test="averageCost != null">#{averageCost},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleStatistics" parameterType="VehicleStatistics">
        update vehicle_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statisticsType != null and statisticsType != ''">statistics_type = #{statisticsType},</if>
            <if test="statisticsDate != null">statistics_date = #{statisticsDate},</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="usageCount != null">usage_count = #{usageCount},</if>
            <if test="usageHours != null">usage_hours = #{usageHours},</if>
            <if test="totalCost != null">total_cost = #{totalCost},</if>
            <if test="averageCost != null">average_cost = #{averageCost},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where statistics_id = #{statisticsId}
    </update>

    <delete id="deleteVehicleStatisticsByStatisticsId" parameterType="Long">
        delete from vehicle_statistics where statistics_id = #{statisticsId}
    </delete>

    <delete id="deleteVehicleStatisticsByStatisticsIds" parameterType="String">
        delete from vehicle_statistics where statistics_id in
        <foreach item="statisticsId" collection="array" open="(" separator="," close=")">
            #{statisticsId}
        </foreach>
    </delete>
</mapper>
