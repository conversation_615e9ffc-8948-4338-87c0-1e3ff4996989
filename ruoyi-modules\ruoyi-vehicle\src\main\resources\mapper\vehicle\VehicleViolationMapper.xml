<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleViolationMapper">
    
    <resultMap type="VehicleViolation" id="VehicleViolationResult">
        <result property="violationId"    column="violation_id"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="violationType"    column="violation_type"    />
        <result property="violationTime"    column="violation_time"    />
        <result property="violationLocation"    column="violation_location"    />
        <result property="violationDescription"    column="violation_description"    />
        <result property="penaltyAmount"    column="penalty_amount"    />
        <result property="status"    column="status"    />
        <result property="processTime"    column="process_time"    />
        <result property="processPerson"    column="process_person"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleViolationVo">
        select violation_id, vehicle_id, violation_type, violation_time, violation_location, 
               violation_description, penalty_amount, status, process_time, process_person, 
               create_by, create_time, update_by, update_time, remark 
        from vehicle_violation
    </sql>

    <select id="selectVehicleViolationList" parameterType="VehicleViolation" resultMap="VehicleViolationResult">
        <include refid="selectVehicleViolationVo"/>
        <where>  
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="violationType != null  and violationType != ''"> and violation_type = #{violationType}</if>
            <if test="violationLocation != null  and violationLocation != ''"> and violation_location like concat('%', #{violationLocation}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginViolationTime != null and params.beginViolationTime != ''"><!-- 开始时间检索 -->
                and date_format(violation_time,'%y%m%d') &gt;= date_format(#{params.beginViolationTime},'%y%m%d')
            </if>
            <if test="params.endViolationTime != null and params.endViolationTime != ''"><!-- 结束时间检索 -->
                and date_format(violation_time,'%y%m%d') &lt;= date_format(#{params.endViolationTime},'%y%m%d')
            </if>
        </where>
        order by violation_time desc
    </select>
    
    <select id="selectVehicleViolationByViolationId" parameterType="Long" resultMap="VehicleViolationResult">
        <include refid="selectVehicleViolationVo"/>
        where violation_id = #{violationId}
    </select>

    <select id="selectVehicleViolationByVehicleId" parameterType="Long" resultMap="VehicleViolationResult">
        <include refid="selectVehicleViolationVo"/>
        where vehicle_id = #{vehicleId}
        order by violation_time desc
    </select>

    <select id="selectVehicleViolationByStatus" parameterType="String" resultMap="VehicleViolationResult">
        <include refid="selectVehicleViolationVo"/>
        where status = #{status}
        order by violation_time desc
    </select>
        
    <insert id="insertVehicleViolation" parameterType="VehicleViolation" useGeneratedKeys="true" keyProperty="violationId">
        insert into vehicle_violation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="violationType != null and violationType != ''">violation_type,</if>
            <if test="violationTime != null">violation_time,</if>
            <if test="violationLocation != null and violationLocation != ''">violation_location,</if>
            <if test="violationDescription != null">violation_description,</if>
            <if test="penaltyAmount != null">penalty_amount,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="processTime != null">process_time,</if>
            <if test="processPerson != null">process_person,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="violationType != null and violationType != ''">#{violationType},</if>
            <if test="violationTime != null">#{violationTime},</if>
            <if test="violationLocation != null and violationLocation != ''">#{violationLocation},</if>
            <if test="violationDescription != null">#{violationDescription},</if>
            <if test="penaltyAmount != null">#{penaltyAmount},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="processPerson != null">#{processPerson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleViolation" parameterType="VehicleViolation">
        update vehicle_violation
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="violationType != null and violationType != ''">violation_type = #{violationType},</if>
            <if test="violationTime != null">violation_time = #{violationTime},</if>
            <if test="violationLocation != null and violationLocation != ''">violation_location = #{violationLocation},</if>
            <if test="violationDescription != null">violation_description = #{violationDescription},</if>
            <if test="penaltyAmount != null">penalty_amount = #{penaltyAmount},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="processPerson != null">process_person = #{processPerson},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where violation_id = #{violationId}
    </update>

    <delete id="deleteVehicleViolationByViolationId" parameterType="Long">
        delete from vehicle_violation where violation_id = #{violationId}
    </delete>

    <delete id="deleteVehicleViolationByViolationIds" parameterType="String">
        delete from vehicle_violation where violation_id in 
        <foreach item="violationId" collection="array" open="(" separator="," close=")">
            #{violationId}
        </foreach>
    </delete>
</mapper>
