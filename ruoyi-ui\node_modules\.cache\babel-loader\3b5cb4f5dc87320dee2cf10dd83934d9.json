{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\manager-approve.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\manager-approve.vue", "mtime": 1754144140594}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_order", "require", "name", "data", "loading", "ids", "multiple", "total", "orderList", "statistics", "pendingCount", "approvedCount", "totalCost", "todayCount", "queryParams", "pageNum", "pageSize", "orderStatus", "date<PERSON><PERSON><PERSON>", "detailDialogVisible", "detailOrder", "approveDialogVisible", "approveLoading", "currentOrder", "approveForm", "approveResult", "approveComment", "approveRules", "required", "message", "trigger", "min", "max", "created", "getList", "loadStatistics", "methods", "_this", "params", "length", "getPendingConfirmOrders", "then", "response", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "refreshList", "$modal", "msgSuccess", "handleSelectionChange", "selection", "map", "item", "orderId", "handleView", "row", "_this2", "getOrder", "handleApprove", "handleReject", "submitApproval", "_this3", "$refs", "validate", "valid", "managerApproveOrder", "catch", "handleBatchApprove", "_this4", "msgError", "confirm", "concat", "showCostAnalysis", "msgInfo", "handleExport", "getApprovalStep", "status", "stepMap", "calculateDuration", "startTime", "endTime", "diff", "Date", "getTime", "hours", "Math", "floor", "minutes", "getCostBearerText", "costBearer", "bearerMap", "getStatusTagType", "statusMap", "getStatusText"], "sources": ["src/views/vehicle/order/manager-approve.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">机械主管 - 最终审批</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待审批订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.approvedCount }}</div>\n              <div class=\"stat-label\">已审批订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.totalCost }}</div>\n              <div class=\"stat-label\">总费用(万元)</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日审批</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"待主管审批\" value=\"pending_manager\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"费用范围\" prop=\"costRange\">\n          <el-select v-model=\"queryParams.costRange\" placeholder=\"请选择费用范围\" clearable @change=\"getList\">\n            <el-option label=\"1000元以下\" value=\"under_1000\"></el-option>\n            <el-option label=\"1000-5000元\" value=\"1000_5000\"></el-option>\n            <el-option label=\"5000-10000元\" value=\"5000_10000\"></el-option>\n            <el-option label=\"10000元以上\" value=\"over_10000\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆重量\" prop=\"vehicleWeight\">\n          <el-select v-model=\"queryParams.vehicleWeight\" placeholder=\"请选择重量范围\" clearable @change=\"getList\">\n            <el-option label=\"50吨及以上\" value=\"over_50\"></el-option>\n            <el-option label=\"50吨以下\" value=\"under_50\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchApprove\"\n            v-hasPermi=\"['vehicle:order:manager-approve']\"\n          >批量审批</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-pie-chart\"\n            size=\"mini\"\n            @click=\"showCostAnalysis\"\n          >费用分析</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-download\"\n            size=\"mini\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['vehicle:order:export']\"\n          >导出报表</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n            <div style=\"color: #E6A23C; font-size: 12px; font-weight: bold;\">\n              重量：{{ scope.row.vehicleWeight }}吨\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"队伍信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.driverName }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"费用信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>\n              <div style=\"color: #67C23A; font-weight: bold; font-size: 16px;\">￥{{ scope.row.totalCost }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ getCostBearerText(scope.row.costBearer) }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.costUnit === 'hour' ? '按小时' : '按天' }}计费</div>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'pending_manager'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleApprove(scope.row)\"\n              v-hasPermi=\"['vehicle:order:manager-approve']\"\n            >审批通过</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'pending_manager'\"\n              size=\"mini\"\n              type=\"warning\"\n              @click=\"handleReject(scope.row)\"\n              v-hasPermi=\"['vehicle:order:manager-reject']\"\n            >退回</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"1000px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"车辆重量\">\n          <span style=\"color: #E6A23C; font-weight: bold;\">{{ detailOrder.vehicleWeight }}吨</span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用详情\">\n          <div>\n            <div>总费用：<span style=\"color: #67C23A; font-weight: bold; font-size: 18px;\">￥{{ detailOrder.totalCost }}</span></div>\n            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>\n            <div>计量单位：{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>\n            <div>单价：￥{{ detailOrder.unitPrice }}/{{ detailOrder.costUnit === 'hour' ? '小时' : '天' }}</div>\n            <div>计算时间：{{ parseTime(detailOrder.costCalculateTime) }}</div>\n          </div>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 审批流程 -->\n      <div style=\"margin-top: 20px;\">\n        <h4>审批流程</h4>\n        <el-steps :active=\"getApprovalStep(detailOrder.orderStatus)\" finish-status=\"success\">\n          <el-step title=\"司机完成\" :description=\"parseTime(detailOrder.actualEndTime)\"></el-step>\n          <el-step title=\"队伍确认\" :description=\"parseTime(detailOrder.teamConfirmTime)\"></el-step>\n          <el-step title=\"调度确认\" :description=\"parseTime(detailOrder.dispatchConfirmTime)\"></el-step>\n          <el-step title=\"主管审批\" :description=\"parseTime(detailOrder.managerConfirmTime)\"></el-step>\n        </el-steps>\n      </div>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 审批操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'pending_manager'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">主管审批</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleApprove(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 审批通过\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"warning\" size=\"medium\" @click=\"handleReject(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-close\"></i> 退回修改\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 审批对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"approveDialogVisible\" width=\"500px\" append-to-body>\n      <el-form ref=\"approveForm\" :model=\"approveForm\" :rules=\"approveRules\" label-width=\"100px\">\n        <el-form-item label=\"审批结果\" prop=\"approveResult\">\n          <el-radio-group v-model=\"approveForm.approveResult\">\n            <el-radio label=\"approve\">审批通过</el-radio>\n            <el-radio label=\"reject\">退回修改</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"审批意见\" prop=\"approveComment\">\n          <el-input\n            v-model=\"approveForm.approveComment\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入审批意见\"\n            maxlength=\"500\"\n            show-word-limit />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"approveDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitApproval\" :loading=\"approveLoading\">确认审批</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, managerApproveOrder } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"ManagerApprove\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        approvedCount: 0,\n        totalCost: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'pending_manager'\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 审批对话框\n      approveDialogVisible: false,\n      approveLoading: false,\n      currentOrder: {},\n      approveForm: {\n        approveResult: 'approve',\n        approveComment: ''\n      },\n      // 审批表单验证规则\n      approveRules: {\n        approveComment: [\n          { required: true, message: \"请输入审批意见\", trigger: \"blur\" },\n          { min: 5, max: 500, message: \"长度在 5 到 500 个字符\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('manager').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 3,\n        approvedCount: 25,\n        totalCost: 15.6,\n        todayCount: 2\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 审批通过 */\n    handleApprove(row) {\n      this.currentOrder = row;\n      this.approveDialogVisible = true;\n      this.approveForm = {\n        approveResult: 'approve',\n        approveComment: ''\n      };\n    },\n    \n    /** 退回修改 */\n    handleReject(row) {\n      this.currentOrder = row;\n      this.approveDialogVisible = true;\n      this.approveForm = {\n        approveResult: 'reject',\n        approveComment: ''\n      };\n    },\n    \n    /** 提交审批 */\n    submitApproval() {\n      this.$refs[\"approveForm\"].validate(valid => {\n        if (valid) {\n          this.approveLoading = true;\n          const data = {\n            orderId: this.currentOrder.orderId,\n            approveResult: this.approveForm.approveResult,\n            approveComment: this.approveForm.approveComment\n          };\n          \n          managerApproveOrder(data).then(response => {\n            this.$modal.msgSuccess(\"审批成功\");\n            this.approveDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.approveLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 批量审批 */\n    handleBatchApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审批的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认批量审批选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量审批接口\n        this.$modal.msgSuccess(\"批量审批成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 显示费用分析 */\n    showCostAnalysis() {\n      this.$modal.msgInfo(\"费用分析功能开发中...\");\n    },\n    \n    /** 导出报表 */\n    handleExport() {\n      this.$modal.msgInfo(\"导出功能开发中...\");\n    },\n    \n    /** 获取审批步骤 */\n    getApprovalStep(status) {\n      const stepMap = {\n        'driver_finished': 1,\n        'team_confirmed': 2,\n        'dispatch_confirmed': 3,\n        'pending_manager': 3,\n        'completed': 4\n      };\n      return stepMap[status] || 0;\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取费用承担方文本 */\n    getCostBearerText(costBearer) {\n      const bearerMap = {\n        'project': '项目承担',\n        'team': '队伍承担'\n      };\n      return bearerMap[costBearer] || '未知';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending_manager': 'warning',\n        'completed': 'success'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending_manager': '待主管审批',\n        'completed': '已完成'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;AA+UA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,mBAAA;MACAC,WAAA;MACA;MACAC,oBAAA;MACAC,cAAA;MACAC,YAAA;MACAC,WAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACAC,YAAA;QACAD,cAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAjC,OAAA;MACA,KAAAU,WAAA,CAAAwB,MAAA;MACA,SAAApB,SAAA,SAAAA,SAAA,CAAAqB,MAAA;QACA,KAAAzB,WAAA,CAAAwB,MAAA,8BAAApB,SAAA;QACA,KAAAJ,WAAA,CAAAwB,MAAA,4BAAApB,SAAA;MACA;MAEA,IAAAsB,8BAAA,aAAAC,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAA7B,SAAA,GAAAkC,QAAA,CAAAvC,IAAA;QACAkC,KAAA,CAAA9B,KAAA,GAAAmC,QAAA,CAAAvC,IAAA,CAAAoC,MAAA;QACAF,KAAA,CAAAjC,OAAA;MACA;IACA;IAEA,aACA+B,cAAA,WAAAA,eAAA;MACA;MACA,KAAA1B,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,UAAA;MACA;IACA;IAEA,aACA8B,WAAA,WAAAA,YAAA;MACA,KAAA7B,WAAA,CAAAC,OAAA;MACA,KAAAmB,OAAA;IACA;IAEA,aACAU,UAAA,WAAAA,WAAA;MACA,KAAA1B,SAAA;MACA,KAAA2B,SAAA;MACA,KAAAF,WAAA;IACA;IAEA,WACAG,WAAA,WAAAA,YAAA;MACA,KAAAZ,OAAA;MACA,KAAAC,cAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA;MAAA;MACA,KAAA/C,QAAA,IAAA4C,SAAA,CAAAX,MAAA;IACA;IAEA,WACAe,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,eAAA,EAAAF,GAAA,CAAAF,OAAA,EAAAZ,IAAA,WAAAC,QAAA;QACAc,MAAA,CAAApC,WAAA,GAAAsB,QAAA,CAAAvC,IAAA;QACAqD,MAAA,CAAArC,mBAAA;MACA;IACA;IAEA,WACAuC,aAAA,WAAAA,cAAAH,GAAA;MACA,KAAAhC,YAAA,GAAAgC,GAAA;MACA,KAAAlC,oBAAA;MACA,KAAAG,WAAA;QACAC,aAAA;QACAC,cAAA;MACA;IACA;IAEA,WACAiC,YAAA,WAAAA,aAAAJ,GAAA;MACA,KAAAhC,YAAA,GAAAgC,GAAA;MACA,KAAAlC,oBAAA;MACA,KAAAG,WAAA;QACAC,aAAA;QACAC,cAAA;MACA;IACA;IAEA,WACAkC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,gBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAvC,cAAA;UACA,IAAAnB,IAAA;YACAkD,OAAA,EAAAQ,MAAA,CAAAtC,YAAA,CAAA8B,OAAA;YACA5B,aAAA,EAAAoC,MAAA,CAAArC,WAAA,CAAAC,aAAA;YACAC,cAAA,EAAAmC,MAAA,CAAArC,WAAA,CAAAE;UACA;UAEA,IAAAuC,0BAAA,EAAA9D,IAAA,EAAAsC,IAAA,WAAAC,QAAA;YACAmB,MAAA,CAAAd,MAAA,CAAAC,UAAA;YACAa,MAAA,CAAAxC,oBAAA;YACAwC,MAAA,CAAA1C,mBAAA;YACA0C,MAAA,CAAA3B,OAAA;UACA,GAAAgC,KAAA;YACAL,MAAA,CAAAvC,cAAA;UACA;QACA;MACA;IACA;IAEA,WACA6C,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/D,GAAA,CAAAkC,MAAA;QACA,KAAAQ,MAAA,CAAAsB,QAAA;QACA;MACA;MAEA,KAAAtB,MAAA,CAAAuB,OAAA,2DAAAC,MAAA,MAAAlE,GAAA,CAAAkC,MAAA,gCAAAE,IAAA;QACA;QACA2B,MAAA,CAAArB,MAAA,CAAAC,UAAA;QACAoB,MAAA,CAAAlC,OAAA;MACA,GAAAgC,KAAA;IACA;IAEA,aACAM,gBAAA,WAAAA,iBAAA;MACA,KAAAzB,MAAA,CAAA0B,OAAA;IACA;IAEA,WACAC,YAAA,WAAAA,aAAA;MACA,KAAA3B,MAAA,CAAA0B,OAAA;IACA;IAEA,aACAE,eAAA,WAAAA,gBAAAC,MAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,MAAA;IACA;IAEA,aACAE,iBAAA,WAAAA,kBAAAC,SAAA,EAAAC,OAAA;MACA,KAAAD,SAAA,KAAAC,OAAA;MAEA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,OAAA,EAAAG,OAAA,SAAAD,IAAA,CAAAH,SAAA,EAAAI,OAAA;MACA,IAAAC,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,IAAA;MACA,IAAAM,OAAA,GAAAF,IAAA,CAAAC,KAAA,CAAAL,IAAA;MAEA,UAAAV,MAAA,CAAAa,KAAA,kBAAAb,MAAA,CAAAgB,OAAA;IACA;IAEA,gBACAC,iBAAA,WAAAA,kBAAAC,UAAA;MACA,IAAAC,SAAA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,UAAA;IACA;IAEA,eACAE,gBAAA,WAAAA,iBAAAf,MAAA;MACA,IAAAgB,SAAA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAhB,MAAA;IACA;IAEA,aACAiB,aAAA,WAAAA,cAAAjB,MAAA;MACA,IAAAgB,SAAA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAhB,MAAA;IACA;EACA;AACA", "ignoreList": []}]}