package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.TeamInfoMapper;
import com.ruoyi.vehicle.domain.TeamInfo;
import com.ruoyi.vehicle.service.ITeamInfoService;

/**
 * 队伍信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class TeamInfoServiceImpl implements ITeamInfoService 
{
    @Autowired
    private TeamInfoMapper teamInfoMapper;

    /**
     * 查询队伍信息
     * 
     * @param teamId 队伍信息主键
     * @return 队伍信息
     */
    @Override
    public TeamInfo selectTeamInfoByTeamId(Long teamId)
    {
        return teamInfoMapper.selectTeamInfoByTeamId(teamId);
    }

    /**
     * 查询队伍信息列表
     * 
     * @param teamInfo 队伍信息
     * @return 队伍信息
     */
    @Override
    public List<TeamInfo> selectTeamInfoList(TeamInfo teamInfo)
    {
        return teamInfoMapper.selectTeamInfoList(teamInfo);
    }

    /**
     * 新增队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    @Override
    public int insertTeamInfo(TeamInfo teamInfo)
    {
        teamInfo.setCreateTime(DateUtils.getNowDate());
        teamInfo.setStatus("active"); // 默认活跃状态
        return teamInfoMapper.insertTeamInfo(teamInfo);
    }

    /**
     * 修改队伍信息
     * 
     * @param teamInfo 队伍信息
     * @return 结果
     */
    @Override
    public int updateTeamInfo(TeamInfo teamInfo)
    {
        teamInfo.setUpdateTime(DateUtils.getNowDate());
        return teamInfoMapper.updateTeamInfo(teamInfo);
    }

    /**
     * 批量删除队伍信息
     * 
     * @param teamIds 需要删除的队伍信息主键
     * @return 结果
     */
    @Override
    public int deleteTeamInfoByTeamIds(Long[] teamIds)
    {
        return teamInfoMapper.deleteTeamInfoByTeamIds(teamIds);
    }

    /**
     * 删除队伍信息信息
     * 
     * @param teamId 队伍信息主键
     * @return 结果
     */
    @Override
    public int deleteTeamInfoByTeamId(Long teamId)
    {
        return teamInfoMapper.deleteTeamInfoByTeamId(teamId);
    }

    /**
     * 根据队伍类型查询队伍列表
     * 
     * @param teamType 队伍类型
     * @return 队伍集合
     */
    @Override
    public List<TeamInfo> selectTeamInfoByType(String teamType)
    {
        return teamInfoMapper.selectTeamInfoByType(teamType);
    }

    /**
     * 根据状态查询队伍列表
     * 
     * @param status 状态
     * @return 队伍集合
     */
    @Override
    public List<TeamInfo> selectTeamInfoByStatus(String status)
    {
        return teamInfoMapper.selectTeamInfoByStatus(status);
    }

    /**
     * 查询活跃队伍列表
     * 
     * @return 队伍集合
     */
    @Override
    public List<TeamInfo> selectActiveTeamList()
    {
        return selectTeamInfoByStatus("active");
    }

    /**
     * 更新队伍状态
     * 
     * @param teamId 队伍ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateTeamStatus(Long teamId, String status)
    {
        TeamInfo teamInfo = selectTeamInfoByTeamId(teamId);
        if (teamInfo == null) {
            throw new ServiceException("队伍不存在");
        }
        
        teamInfo.setStatus(status);
        teamInfo.setUpdateTime(DateUtils.getNowDate());
        
        return updateTeamInfo(teamInfo);
    }
}
