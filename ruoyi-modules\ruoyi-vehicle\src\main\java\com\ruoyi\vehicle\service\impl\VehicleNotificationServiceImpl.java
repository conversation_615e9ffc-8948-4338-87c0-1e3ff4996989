package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.vehicle.mapper.VehicleNotificationMapper;
import com.ruoyi.vehicle.mapper.VehicleDemandPlanMapper;
import com.ruoyi.vehicle.mapper.VehicleApplicationMapper;
import com.ruoyi.vehicle.mapper.VehicleOrderMapper;
import com.ruoyi.vehicle.domain.VehicleNotification;
import com.ruoyi.vehicle.domain.VehicleDemandPlan;
import com.ruoyi.vehicle.domain.VehicleApplication;
import com.ruoyi.vehicle.domain.VehicleOrder;
import com.ruoyi.vehicle.service.IVehicleNotificationService;

/**
 * 消息通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleNotificationServiceImpl implements IVehicleNotificationService 
{
    @Autowired
    private VehicleNotificationMapper vehicleNotificationMapper;

    @Autowired
    private VehicleDemandPlanMapper demandPlanMapper;

    @Autowired
    private VehicleApplicationMapper applicationMapper;

    @Autowired
    private VehicleOrderMapper orderMapper;

    /**
     * 查询消息通知
     * 
     * @param notificationId 消息通知主键
     * @return 消息通知
     */
    @Override
    public VehicleNotification selectVehicleNotificationByNotificationId(Long notificationId)
    {
        return vehicleNotificationMapper.selectVehicleNotificationByNotificationId(notificationId);
    }

    /**
     * 查询消息通知列表
     * 
     * @param vehicleNotification 消息通知
     * @return 消息通知
     */
    @Override
    public List<VehicleNotification> selectVehicleNotificationList(VehicleNotification vehicleNotification)
    {
        return vehicleNotificationMapper.selectVehicleNotificationList(vehicleNotification);
    }

    /**
     * 新增消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    @Override
    public int insertVehicleNotification(VehicleNotification vehicleNotification)
    {
        vehicleNotification.setCreateTime(DateUtils.getNowDate());
        vehicleNotification.setSendStatus("pending");
        vehicleNotification.setReadStatus("unread");
        return vehicleNotificationMapper.insertVehicleNotification(vehicleNotification);
    }

    /**
     * 修改消息通知
     * 
     * @param vehicleNotification 消息通知
     * @return 结果
     */
    @Override
    public int updateVehicleNotification(VehicleNotification vehicleNotification)
    {
        vehicleNotification.setUpdateTime(DateUtils.getNowDate());
        return vehicleNotificationMapper.updateVehicleNotification(vehicleNotification);
    }

    /**
     * 批量删除消息通知
     * 
     * @param notificationIds 需要删除的消息通知主键
     * @return 结果
     */
    @Override
    public int deleteVehicleNotificationByNotificationIds(Long[] notificationIds)
    {
        return vehicleNotificationMapper.deleteVehicleNotificationByNotificationIds(notificationIds);
    }

    /**
     * 删除消息通知信息
     * 
     * @param notificationId 消息通知主键
     * @return 结果
     */
    @Override
    public int deleteVehicleNotificationByNotificationId(Long notificationId)
    {
        return vehicleNotificationMapper.deleteVehicleNotificationByNotificationId(notificationId);
    }

    /**
     * 发送需求计划申请通知
     *
     * @param planId 需求计划ID
     * @param notifyType 通知类型（submit、approve、reject）
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int sendDemandPlanNotification(Long planId, String notifyType, String operName)
    {
        try {
            // 1. 获取需求计划信息
            VehicleDemandPlan plan = demandPlanMapper.selectVehicleDemandPlanByPlanId(planId);
            if (plan == null) {
                throw new ServiceException("需求计划不存在");
            }

            // 2. 根据通知类型确定接收人和内容
            String recipient = determineRecipientForDemandPlan(plan, notifyType);
            String title = buildDemandPlanTitle(notifyType);
            String content = buildDemandPlanContent(plan, notifyType, operName);

            // 3. 构建通知内容
            VehicleNotification notification = new VehicleNotification();
            notification.setNotificationType("demand_plan");
            notification.setBusinessId(planId);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setRecipient(recipient);
            notification.setRecipientPhone(getRecipientPhone(recipient));
            notification.setCreateBy(operName);

            int result = insertVehicleNotification(notification);

            // 4. 发送钉钉消息
            if (result > 0) {
                sendDingtalkMessage(notification);
            }

            return result;
        } catch (Exception e) {
            throw new ServiceException("发送需求计划通知失败：" + e.getMessage());
        }
    }

    /**
     * 发送用车申请通知
     *
     * @param applicationId 申请ID
     * @param notifyType 通知类型（submit、approve、reject、assign）
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int sendApplicationNotification(Long applicationId, String notifyType, String operName)
    {
        try {
            // 1. 获取用车申请信息
            VehicleApplication application = applicationMapper.selectVehicleApplicationByApplicationId(applicationId);
            if (application == null) {
                throw new ServiceException("用车申请不存在");
            }

            // 2. 根据通知类型确定接收人和内容
            String recipient = determineRecipientForApplication(application, notifyType);
            String title = buildApplicationTitle(notifyType);
            String content = buildApplicationContent(application, notifyType, operName);

            // 3. 构建通知内容
            VehicleNotification notification = new VehicleNotification();
            notification.setNotificationType("application");
            notification.setBusinessId(applicationId);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setRecipient(recipient);
            notification.setRecipientPhone(getRecipientPhone(recipient));
            notification.setCreateBy(operName);

            int result = insertVehicleNotification(notification);

            if (result > 0) {
                sendDingtalkMessage(notification);
            }

            return result;
        } catch (Exception e) {
            throw new ServiceException("发送用车申请通知失败：" + e.getMessage());
        }
    }

    /**
     * 发送订单确认通知
     *
     * @param orderId 订单ID
     * @param notifyType 通知类型（start、finish、team_confirm、dispatch_confirm、manager_confirm、reject）
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int sendOrderNotification(Long orderId, String notifyType, String operName)
    {
        try {
            // 1. 获取订单信息
            VehicleOrder order = orderMapper.selectVehicleOrderByOrderId(orderId);
            if (order == null) {
                throw new ServiceException("用车订单不存在");
            }

            // 2. 根据通知类型确定接收人和内容
            String recipient = determineRecipientForOrder(order, notifyType);
            String title = buildOrderTitle(notifyType);
            String content = buildOrderContent(order, notifyType, operName);

            // 3. 构建通知内容
            VehicleNotification notification = new VehicleNotification();
            notification.setNotificationType("order_confirm");
            notification.setBusinessId(orderId);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setRecipient(recipient);
            notification.setRecipientPhone(getRecipientPhone(recipient));
            notification.setCreateBy(operName);

            int result = insertVehicleNotification(notification);

            if (result > 0) {
                sendDingtalkMessage(notification);
            }

            return result;
        } catch (Exception e) {
            throw new ServiceException("发送订单通知失败：" + e.getMessage());
        }
    }

    /**
     * 发送钉钉消息
     * 
     * @param notification 通知信息
     * @return 结果
     */
    @Override
    public boolean sendDingtalkMessage(VehicleNotification notification)
    {
        try {
            // TODO: 集成钉钉API发送消息
            // 1. 构建钉钉消息格式
            // 2. 调用钉钉API发送消息
            // 3. 获取消息ID并更新通知记录
            
            // 模拟发送成功
            notification.setSendStatus("sent");
            notification.setSendTime(DateUtils.getNowDate());
            notification.setDingtalkMsgId("MOCK_MSG_ID_" + System.currentTimeMillis());
            
            updateVehicleNotification(notification);
            
            return true;
        } catch (Exception e) {
            // 发送失败，更新状态
            notification.setSendStatus("failed");
            notification.setSendTime(DateUtils.getNowDate());
            updateVehicleNotification(notification);
            
            return false;
        }
    }

    /**
     * 标记消息为已读
     * 
     * @param notificationId 通知ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int markAsRead(Long notificationId, String operName)
    {
        VehicleNotification notification = selectVehicleNotificationByNotificationId(notificationId);
        if (notification == null) {
            throw new ServiceException("通知不存在");
        }
        
        notification.setReadStatus("read");
        notification.setReadTime(DateUtils.getNowDate());
        notification.setUpdateBy(operName);
        
        return updateVehicleNotification(notification);
    }

    /**
     * 根据接收人查询通知列表
     * 
     * @param recipient 接收人
     * @return 通知集合
     */
    @Override
    public List<VehicleNotification> selectNotificationByRecipient(String recipient)
    {
        return vehicleNotificationMapper.selectNotificationByRecipient(recipient);
    }

    /**
     * 根据业务ID和类型查询通知列表
     * 
     * @param businessId 业务ID
     * @param notificationType 通知类型
     * @return 通知集合
     */
    @Override
    public List<VehicleNotification> selectNotificationByBusinessIdAndType(Long businessId, String notificationType)
    {
        return vehicleNotificationMapper.selectNotificationByBusinessIdAndType(businessId, notificationType);
    }

    /**
     * 查询未读通知数量
     * 
     * @param recipient 接收人
     * @return 未读数量
     */
    @Override
    public int countUnreadNotifications(String recipient)
    {
        return vehicleNotificationMapper.countUnreadNotifications(recipient);
    }

    /**
     * 重新发送失败的通知
     * 
     * @param notificationId 通知ID
     * @return 结果
     */
    @Override
    public int resendFailedNotification(Long notificationId)
    {
        VehicleNotification notification = selectVehicleNotificationByNotificationId(notificationId);
        if (notification == null) {
            throw new ServiceException("通知不存在");
        }
        
        if (!"failed".equals(notification.getSendStatus())) {
            throw new ServiceException("只能重新发送失败的通知");
        }
        
        boolean success = sendDingtalkMessage(notification);
        return success ? 1 : 0;
    }

    /**
     * 批量发送通知
     * 
     * @param notifications 通知列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSendNotifications(List<VehicleNotification> notifications)
    {
        int successCount = 0;
        for (VehicleNotification notification : notifications) {
            try {
                int result = insertVehicleNotification(notification);
                if (result > 0) {
                    sendDingtalkMessage(notification);
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他通知
                continue;
            }
        }
        return successCount;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 确定需求计划通知接收人
     */
    private String determineRecipientForDemandPlan(VehicleDemandPlan plan, String notifyType) {
        switch (notifyType) {
            case "submit":
                // 提交时通知项目调度室
                return "项目调度室"; // TODO: 从配置或用户表获取具体人员
            case "approve":
                // 审批通过时通知申请人
                return plan.getApplicant();
            case "reject":
                // 审批拒绝时通知申请人
                return plan.getApplicant();
            default:
                return plan.getApplicant();
        }
    }

    /**
     * 确定用车申请通知接收人
     */
    private String determineRecipientForApplication(VehicleApplication application, String notifyType) {
        switch (notifyType) {
            case "submit":
                // 提交时通知调度室
                return "调度室"; // TODO: 从配置获取
            case "approve":
                // 审批通过时通知申请人
                return application.getApplicant();
            case "reject":
                // 审批拒绝时通知申请人
                return application.getApplicant();
            case "assign":
                // 分配车辆时通知司机和申请队伍
                return application.getAssignedDriver() != null ? application.getAssignedDriver() : application.getApplicant();
            default:
                return application.getApplicant();
        }
    }

    /**
     * 确定订单通知接收人
     */
    private String determineRecipientForOrder(VehicleOrder order, String notifyType) {
        switch (notifyType) {
            case "start":
                // 开始时通知队伍
                return order.getTeamInfo() != null ? order.getTeamInfo().getTeamLeader() : "队伍负责人";
            case "finish":
                // 结束时通知队伍确认
                return order.getTeamInfo() != null ? order.getTeamInfo().getTeamLeader() : "队伍负责人";
            case "team_confirm":
                // 队伍确认后通知调度室
                return "调度室"; // TODO: 从配置获取
            case "dispatch_confirm":
                // 调度室确认后通知主管
                return "机械主管"; // TODO: 从配置获取
            case "manager_confirm":
                // 主管确认后通知相关人员
                return order.getDriverName();
            case "reject":
                // 退回时通知上一级
                return order.getDriverName();
            default:
                return order.getDriverName();
        }
    }

    /**
     * 构建需求计划通知标题
     */
    private String buildDemandPlanTitle(String notifyType) {
        switch (notifyType) {
            case "submit":
                return "【需求计划】新的车辆需求计划待审批";
            case "approve":
                return "【需求计划】您的车辆需求计划已通过审批";
            case "reject":
                return "【需求计划】您的车辆需求计划审批被拒绝";
            default:
                return "【需求计划】车辆需求计划状态变更";
        }
    }

    /**
     * 构建用车申请通知标题
     */
    private String buildApplicationTitle(String notifyType) {
        switch (notifyType) {
            case "submit":
                return "【用车申请】新的用车申请待处理";
            case "approve":
                return "【用车申请】您的用车申请已通过审批";
            case "reject":
                return "【用车申请】您的用车申请审批被拒绝";
            case "assign":
                return "【用车申请】车辆已分配，请准备用车";
            default:
                return "【用车申请】用车申请状态变更";
        }
    }

    /**
     * 构建订单通知标题
     */
    private String buildOrderTitle(String notifyType) {
        switch (notifyType) {
            case "start":
                return "【用车订单】司机已开始用车";
            case "finish":
                return "【用车订单】司机已结束用车，请确认";
            case "team_confirm":
                return "【用车订单】队伍已确认，请调度室确认";
            case "dispatch_confirm":
                return "【用车订单】调度室已确认，请主管确认";
            case "manager_confirm":
                return "【用车订单】主管已确认，订单完成";
            case "reject":
                return "【用车订单】订单被退回，请重新处理";
            default:
                return "【用车订单】订单状态变更";
        }
    }

    /**
     * 构建需求计划通知内容
     */
    private String buildDemandPlanContent(VehicleDemandPlan plan, String notifyType, String operName) {
        StringBuilder content = new StringBuilder();
        content.append("计划标题：").append(plan.getPlanTitle()).append("\n");
        content.append("车辆类型：").append(plan.getVehicleType()).append("\n");
        content.append("车辆型号：").append(plan.getVehicleModel()).append("\n");
        content.append("需求单位：").append(plan.getDemandUnit()).append("\n");
        content.append("申请人：").append(plan.getApplicant()).append("\n");

        switch (notifyType) {
            case "submit":
                content.append("状态：已提交，等待审批\n");
                content.append("请及时处理审批。");
                break;
            case "approve":
                content.append("状态：审批通过\n");
                content.append("审批人：").append(operName);
                break;
            case "reject":
                content.append("状态：审批拒绝\n");
                content.append("审批人：").append(operName).append("\n");
                content.append("拒绝原因：").append(plan.getApprovalComments() != null ? plan.getApprovalComments() : "无");
                break;
        }

        return content.toString();
    }

    /**
     * 构建用车申请通知内容
     */
    private String buildApplicationContent(VehicleApplication application, String notifyType, String operName) {
        StringBuilder content = new StringBuilder();
        content.append("申请标题：").append(application.getApplicationTitle()).append("\n");
        content.append("车辆类型：").append(application.getVehicleType()).append("\n");
        content.append("车辆型号：").append(application.getVehicleModel()).append("\n");
        content.append("用车地点：").append(application.getUsageLocation()).append("\n");
        content.append("申请人：").append(application.getApplicant()).append("\n");

        switch (notifyType) {
            case "submit":
                content.append("状态：已提交，等待审批\n");
                content.append("请及时处理。");
                break;
            case "approve":
                content.append("状态：审批通过\n");
                content.append("审批人：").append(operName);
                break;
            case "reject":
                content.append("状态：审批拒绝\n");
                content.append("审批人：").append(operName);
                break;
            case "assign":
                content.append("状态：车辆已分配\n");
                content.append("分配车辆：").append(application.getAssignedVehicleInfo() != null ?
                    application.getAssignedVehicleInfo().getVehicleModel() : "待确定").append("\n");
                content.append("分配司机：").append(application.getAssignedDriver() != null ?
                    application.getAssignedDriver() : "待确定").append("\n");
                content.append("调度人：").append(operName);
                break;
        }

        return content.toString();
    }

    /**
     * 构建订单通知内容
     */
    private String buildOrderContent(VehicleOrder order, String notifyType, String operName) {
        StringBuilder content = new StringBuilder();
        content.append("订单编号：").append(order.getOrderId()).append("\n");
        content.append("车辆信息：").append(order.getVehicleInfo() != null ?
            order.getVehicleInfo().getVehicleModel() : "未知").append("\n");
        content.append("司机姓名：").append(order.getDriverName()).append("\n");
        content.append("用车地点：").append(order.getUsageLocation()).append("\n");
        content.append("队伍信息：").append(order.getTeamInfo() != null ?
            order.getTeamInfo().getTeamName() : "未知").append("\n");

        switch (notifyType) {
            case "start":
                content.append("状态：司机已开始用车\n");
                content.append("开始时间：").append(order.getActualStartTime() != null ?
                    order.getActualStartTime() : "刚刚").append("\n");
                content.append("操作人：").append(operName);
                break;
            case "finish":
                content.append("状态：司机已结束用车\n");
                content.append("结束时间：").append(order.getActualEndTime() != null ?
                    order.getActualEndTime() : "刚刚").append("\n");
                content.append("请队伍及时确认。");
                break;
            case "team_confirm":
                content.append("状态：队伍已确认\n");
                content.append("确认人：").append(order.getTeamConfirmPerson()).append("\n");
                content.append("请调度室确认。");
                break;
            case "dispatch_confirm":
                content.append("状态：调度室已确认\n");
                content.append("确认人：").append(order.getDispatchConfirmPerson()).append("\n");
                content.append("请主管确认。");
                break;
            case "manager_confirm":
                content.append("状态：主管已确认，订单完成\n");
                content.append("确认人：").append(order.getManagerConfirmPerson());
                break;
            case "reject":
                content.append("状态：订单被退回\n");
                content.append("退回原因：").append(order.getRejectReason() != null ?
                    order.getRejectReason() : "无").append("\n");
                content.append("操作人：").append(operName);
                break;
        }

        return content.toString();
    }

    /**
     * 获取接收人电话号码
     */
    private String getRecipientPhone(String recipient) {
        // TODO: 从用户表或配置中获取接收人的电话号码
        // 这里可以调用用户服务获取用户信息
        return null; // 暂时返回null，实际应该查询用户表
    }
}
