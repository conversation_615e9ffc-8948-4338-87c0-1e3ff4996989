package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleViolation;

/**
 * 车辆违章记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleViolationService 
{
    /**
     * 查询车辆违章记录
     * 
     * @param violationId 车辆违章记录主键
     * @return 车辆违章记录
     */
    public VehicleViolation selectVehicleViolationByViolationId(Long violationId);

    /**
     * 查询车辆违章记录列表
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 车辆违章记录集合
     */
    public List<VehicleViolation> selectVehicleViolationList(VehicleViolation vehicleViolation);

    /**
     * 新增车辆违章记录
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 结果
     */
    public int insertVehicleViolation(VehicleViolation vehicleViolation);

    /**
     * 修改车辆违章记录
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 结果
     */
    public int updateVehicleViolation(VehicleViolation vehicleViolation);

    /**
     * 批量删除车辆违章记录
     * 
     * @param violationIds 需要删除的车辆违章记录主键集合
     * @return 结果
     */
    public int deleteVehicleViolationByViolationIds(Long[] violationIds);

    /**
     * 删除车辆违章记录信息
     * 
     * @param violationId 车辆违章记录主键
     * @return 结果
     */
    public int deleteVehicleViolationByViolationId(Long violationId);

    /**
     * 根据车辆ID查询违章记录列表
     * 
     * @param vehicleId 车辆ID
     * @return 违章记录集合
     */
    public List<VehicleViolation> selectVehicleViolationByVehicleId(Long vehicleId);

    /**
     * 根据处理状态查询违章记录列表
     * 
     * @param status 处理状态
     * @return 违章记录集合
     */
    public List<VehicleViolation> selectVehicleViolationByStatus(String status);

    /**
     * 处理违章记录
     * 
     * @param violationId 违章记录ID
     * @param operName 操作人
     * @return 结果
     */
    public int processViolation(Long violationId, String operName);

    /**
     * 批量处理违章记录
     * 
     * @param violationIds 违章记录ID数组
     * @param operName 操作人
     * @return 结果
     */
    public int batchProcessViolation(Long[] violationIds, String operName);
}
