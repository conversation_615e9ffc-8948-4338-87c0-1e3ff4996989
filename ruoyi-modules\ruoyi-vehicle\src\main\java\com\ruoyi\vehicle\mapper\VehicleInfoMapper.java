package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleInfo;

/**
 * 机械车辆信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleInfoMapper 
{
    /**
     * 查询机械车辆信息
     * 
     * @param vehicleId 机械车辆信息主键
     * @return 机械车辆信息
     */
    public VehicleInfo selectVehicleInfoByVehicleId(Long vehicleId);

    /**
     * 查询机械车辆信息列表
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 机械车辆信息集合
     */
    public List<VehicleInfo> selectVehicleInfoList(VehicleInfo vehicleInfo);

    /**
     * 新增机械车辆信息
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 结果
     */
    public int insertVehicleInfo(VehicleInfo vehicleInfo);

    /**
     * 修改机械车辆信息
     * 
     * @param vehicleInfo 机械车辆信息
     * @return 结果
     */
    public int updateVehicleInfo(VehicleInfo vehicleInfo);

    /**
     * 删除机械车辆信息
     * 
     * @param vehicleId 机械车辆信息主键
     * @return 结果
     */
    public int deleteVehicleInfoByVehicleId(Long vehicleId);

    /**
     * 批量删除机械车辆信息
     * 
     * @param vehicleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleInfoByVehicleIds(Long[] vehicleIds);

    /**
     * 根据车辆状态查询车辆列表
     * 
     * @param vehicleStatus 车辆状态
     * @return 车辆信息集合
     */
    public List<VehicleInfo> selectVehicleInfoByStatus(String vehicleStatus);

    /**
     * 根据车辆类型查询车辆列表
     * 
     * @param vehicleType 车辆类型
     * @return 车辆信息集合
     */
    public List<VehicleInfo> selectVehicleInfoByType(String vehicleType);

    /**
     * 查询可用车辆列表
     * 
     * @return 车辆信息集合
     */
    public List<VehicleInfo> selectAvailableVehicleList();
}
