{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\violation.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\api\\vehicle\\violation.js", "mtime": 1754141776955}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listViolation", "query", "request", "url", "method", "params", "getViolation", "violationId", "addViolation", "data", "updateViolation", "delViolation", "getViolationByVehicleId", "vehicleId", "getViolationByStatus", "status", "processViolation", "batchProcessViolation", "violationIds"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/api/vehicle/violation.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询车辆违章记录列表\nexport function listViolation(query) {\n  return request({\n    url: '/vehicle/violation/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询车辆违章记录详细\nexport function getViolation(violationId) {\n  return request({\n    url: '/vehicle/violation/' + violationId,\n    method: 'get'\n  })\n}\n\n// 新增车辆违章记录\nexport function addViolation(data) {\n  return request({\n    url: '/vehicle/violation',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改车辆违章记录\nexport function updateViolation(data) {\n  return request({\n    url: '/vehicle/violation',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除车辆违章记录\nexport function delViolation(violationId) {\n  return request({\n    url: '/vehicle/violation/' + violationId,\n    method: 'delete'\n  })\n}\n\n// 根据车辆ID查询违章记录\nexport function getViolationByVehicleId(vehicleId) {\n  return request({\n    url: '/vehicle/violation/vehicle/' + vehicleId,\n    method: 'get'\n  })\n}\n\n// 根据处理状态查询违章记录\nexport function getViolationByStatus(status) {\n  return request({\n    url: '/vehicle/violation/status/' + status,\n    method: 'get'\n  })\n}\n\n// 处理违章记录\nexport function processViolation(violationId) {\n  return request({\n    url: '/vehicle/violation/process/' + violationId,\n    method: 'put'\n  })\n}\n\n// 批量处理违章记录\nexport function batchProcessViolation(violationIds) {\n  return request({\n    url: '/vehicle/violation/batch-process',\n    method: 'put',\n    data: violationIds\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,WAAW;IACxCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,WAAW;IACxCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,uBAAuBA,CAACC,SAAS,EAAE;EACjD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGU,SAAS;IAC9CT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGY,MAAM;IAC1CX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,gBAAgBA,CAACT,WAAW,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGI,WAAW;IAChDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,qBAAqBA,CAACC,YAAY,EAAE;EAClD,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAES;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}