<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vehicle.mapper.VehicleDemandPlanMapper">
    
    <resultMap type="VehicleDemandPlan" id="VehicleDemandPlanResult">
        <result property="planId"    column="plan_id"    />
        <result property="planTitle"    column="plan_title"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="vehicleModel"    column="vehicle_model"    />
        <result property="demandUnit"    column="demand_unit"    />
        <result property="demandQuantity"    column="demand_quantity"    />
        <result property="demandStartTime"    column="demand_start_time"    />
        <result property="demandEndTime"    column="demand_end_time"    />
        <result property="usagePurpose"    column="usage_purpose"    />
        <result property="teamId"    column="team_id"    />
        <result property="applicant"    column="applicant"    />
        <result property="applicantPhone"    column="applicant_phone"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approvalComments"    column="approval_comments"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVehicleDemandPlanVo">
        select plan_id, plan_title, vehicle_type, vehicle_model, demand_unit, demand_quantity, 
               demand_start_time, demand_end_time, usage_purpose, team_id, applicant, applicant_phone, 
               approval_status, approval_comments, create_by, create_time, update_by, update_time, remark 
        from vehicle_demand_plan
    </sql>

    <select id="selectVehicleDemandPlanList" parameterType="VehicleDemandPlan" resultMap="VehicleDemandPlanResult">
        <include refid="selectVehicleDemandPlanVo"/>
        <where>  
            <if test="planTitle != null  and planTitle != ''"> and plan_title like concat('%', #{planTitle}, '%')</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="vehicleModel != null  and vehicleModel != ''"> and vehicle_model = #{vehicleModel}</if>
            <if test="demandUnit != null  and demandUnit != ''"> and demand_unit like concat('%', #{demandUnit}, '%')</if>
            <if test="demandStartTime != null "> and demand_start_time &gt;= #{demandStartTime}</if>
            <if test="demandEndTime != null "> and demand_end_time &lt;= #{demandEndTime}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="applicant != null  and applicant != ''"> and applicant like concat('%', #{applicant}, '%')</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVehicleDemandPlanByPlanId" parameterType="Long" resultMap="VehicleDemandPlanResult">
        <include refid="selectVehicleDemandPlanVo"/>
        where plan_id = #{planId}
    </select>

    <select id="selectVehicleDemandPlanByStatus" parameterType="String" resultMap="VehicleDemandPlanResult">
        <include refid="selectVehicleDemandPlanVo"/>
        where approval_status = #{approvalStatus}
        order by create_time desc
    </select>

    <select id="selectVehicleDemandPlanByTeamId" parameterType="Long" resultMap="VehicleDemandPlanResult">
        <include refid="selectVehicleDemandPlanVo"/>
        where team_id = #{teamId}
        order by create_time desc
    </select>
        
    <insert id="insertVehicleDemandPlan" parameterType="VehicleDemandPlan" useGeneratedKeys="true" keyProperty="planId">
        insert into vehicle_demand_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planTitle != null and planTitle != ''">plan_title,</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model,</if>
            <if test="demandUnit != null and demandUnit != ''">demand_unit,</if>
            <if test="demandQuantity != null">demand_quantity,</if>
            <if test="demandStartTime != null">demand_start_time,</if>
            <if test="demandEndTime != null">demand_end_time,</if>
            <if test="usagePurpose != null and usagePurpose != ''">usage_purpose,</if>
            <if test="teamId != null">team_id,</if>
            <if test="applicant != null and applicant != ''">applicant,</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="approvalComments != null">approval_comments,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planTitle != null and planTitle != ''">#{planTitle},</if>
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">#{vehicleModel},</if>
            <if test="demandUnit != null and demandUnit != ''">#{demandUnit},</if>
            <if test="demandQuantity != null">#{demandQuantity},</if>
            <if test="demandStartTime != null">#{demandStartTime},</if>
            <if test="demandEndTime != null">#{demandEndTime},</if>
            <if test="usagePurpose != null and usagePurpose != ''">#{usagePurpose},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="applicant != null and applicant != ''">#{applicant},</if>
            <if test="applicantPhone != null and applicantPhone != ''">#{applicantPhone},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="approvalComments != null">#{approvalComments},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateVehicleDemandPlan" parameterType="VehicleDemandPlan">
        update vehicle_demand_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planTitle != null and planTitle != ''">plan_title = #{planTitle},</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="vehicleModel != null and vehicleModel != ''">vehicle_model = #{vehicleModel},</if>
            <if test="demandUnit != null and demandUnit != ''">demand_unit = #{demandUnit},</if>
            <if test="demandQuantity != null">demand_quantity = #{demandQuantity},</if>
            <if test="demandStartTime != null">demand_start_time = #{demandStartTime},</if>
            <if test="demandEndTime != null">demand_end_time = #{demandEndTime},</if>
            <if test="usagePurpose != null and usagePurpose != ''">usage_purpose = #{usagePurpose},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="applicant != null and applicant != ''">applicant = #{applicant},</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone = #{applicantPhone},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="approvalComments != null">approval_comments = #{approvalComments},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where plan_id = #{planId}
    </update>

    <delete id="deleteVehicleDemandPlanByPlanId" parameterType="Long">
        delete from vehicle_demand_plan where plan_id = #{planId}
    </delete>

    <delete id="deleteVehicleDemandPlanByPlanIds" parameterType="String">
        delete from vehicle_demand_plan where plan_id in 
        <foreach item="planId" collection="array" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>
</mapper>
