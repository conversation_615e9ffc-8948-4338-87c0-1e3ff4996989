package com.ruoyi.vehicle.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 车辆违章记录对象 vehicle_violation
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class VehicleViolation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 违章记录ID */
    private Long violationId;

    /** 车辆ID */
    @Excel(name = "车辆ID")
    private Long vehicleId;

    /** 车辆信息 */
    private VehicleInfo vehicleInfo;

    /** 违章时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "违章时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date violationTime;

    /** 违章地点 */
    @Excel(name = "违章地点")
    private String violationLocation;

    /** 违章类型 */
    @Excel(name = "违章类型")
    private String violationType;

    /** 违章描述 */
    @Excel(name = "违章描述")
    private String violationDescription;

    /** 罚款金额 */
    @Excel(name = "罚款金额")
    private BigDecimal penaltyAmount;

    /** 处理状态 */
    @Excel(name = "处理状态", readConverterExp = "未处理=0,已处理=1")
    private String status;

    public void setViolationId(Long violationId) 
    {
        this.violationId = violationId;
    }

    public Long getViolationId() 
    {
        return violationId;
    }

    public void setVehicleId(Long vehicleId) 
    {
        this.vehicleId = vehicleId;
    }

    @NotNull(message = "车辆ID不能为空")
    public Long getVehicleId() 
    {
        return vehicleId;
    }

    public void setVehicleInfo(VehicleInfo vehicleInfo) 
    {
        this.vehicleInfo = vehicleInfo;
    }

    public VehicleInfo getVehicleInfo() 
    {
        return vehicleInfo;
    }

    public void setViolationTime(Date violationTime) 
    {
        this.violationTime = violationTime;
    }

    @NotNull(message = "违章时间不能为空")
    public Date getViolationTime() 
    {
        return violationTime;
    }

    public void setViolationLocation(String violationLocation) 
    {
        this.violationLocation = violationLocation;
    }

    @Size(min = 0, max = 200, message = "违章地点长度不能超过200个字符")
    public String getViolationLocation() 
    {
        return violationLocation;
    }

    public void setViolationType(String violationType) 
    {
        this.violationType = violationType;
    }

    @NotBlank(message = "违章类型不能为空")
    @Size(min = 0, max = 100, message = "违章类型长度不能超过100个字符")
    public String getViolationType() 
    {
        return violationType;
    }

    public void setViolationDescription(String violationDescription) 
    {
        this.violationDescription = violationDescription;
    }

    public String getViolationDescription() 
    {
        return violationDescription;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) 
    {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyAmount() 
    {
        return penaltyAmount;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    @Size(min = 0, max = 20, message = "处理状态长度不能超过20个字符")
    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("violationId", getViolationId())
            .append("vehicleId", getVehicleId())
            .append("violationTime", getViolationTime())
            .append("violationLocation", getViolationLocation())
            .append("violationType", getViolationType())
            .append("violationDescription", getViolationDescription())
            .append("penaltyAmount", getPenaltyAmount())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
