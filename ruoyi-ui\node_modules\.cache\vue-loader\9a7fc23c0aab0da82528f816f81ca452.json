{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue?vue&type=style&index=0&id=bdac112a&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\order\\dispatch-confirm.vue", "mtime": 1754144052418}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJveC1jYXJkIHsKICBtYXJnaW46IDIwcHg7Cn0KCi5zdGF0LWNhcmQgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7Cn0KCi5zdGF0LWNhcmQ6aG92ZXIgewogIGJveC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDAsMCwwLDAuMTIpOwp9Cgouc3RhdC1pdGVtIHsKICBwYWRkaW5nOiAyMHB4Owp9Cgouc3RhdC12YWx1ZSB7CiAgZm9udC1zaXplOiAyOHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjNDA5RUZGOwogIG1hcmdpbi1ib3R0b206IDhweDsKfQoKLnN0YXQtbGFiZWwgewogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzYwNjI2NjsKfQoKLm1iMjAgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5waG90by1zZWN0aW9uIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5waG90by1zZWN0aW9uIGg1IHsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIGNvbG9yOiAjNjA2MjY2Owp9CgouY29zdC1pbmZvIHsKICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOwogIHBhZGRpbmc6IDE1cHg7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGxpbmUtaGVpZ2h0OiAxLjg7Cn0KCi5jb3N0LXByZXZpZXcgewogIGJhY2tncm91bmQ6ICNmMGY5ZmY7CiAgcGFkZGluZzogMTVweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7Cn0K"}, {"version": 3, "sources": ["dispatch-confirm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwlBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dispatch-confirm.vue", "sourceRoot": "src/views/vehicle/order", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">项目调度室 - 台班确认</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshList\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.confirmedCount }}</div>\n              <div class=\"stat-label\">已确认订单</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.over50TonCount }}</div>\n              <div class=\"stat-label\">超50吨待审</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayCount }}</div>\n              <div class=\"stat-label\">今日处理</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"订单状态\" prop=\"orderStatus\">\n          <el-select v-model=\"queryParams.orderStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"队伍已确认\" value=\"team_confirmed\"></el-option>\n            <el-option label=\"调度已确认\" value=\"dispatch_confirmed\"></el-option>\n            <el-option label=\"待主管审批\" value=\"pending_manager\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"车辆重量\" prop=\"vehicleWeight\">\n          <el-select v-model=\"queryParams.vehicleWeight\" placeholder=\"请选择重量范围\" clearable @change=\"getList\">\n            <el-option label=\"50吨以下\" value=\"under_50\"></el-option>\n            <el-option label=\"50吨及以上\" value=\"over_50\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"费用承担\" prop=\"costBearer\">\n          <el-select v-model=\"queryParams.costBearer\" placeholder=\"请选择费用承担方\" clearable @change=\"getList\">\n            <el-option label=\"项目承担\" value=\"project\"></el-option>\n            <el-option label=\"队伍承担\" value=\"team\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"完成日期\">\n          <el-date-picker\n            v-model=\"dateRange\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd\"\n            type=\"daterange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始日期\"\n            end-placeholder=\"结束日期\"\n            @change=\"getList\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchConfirm\"\n            v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n          >批量确认</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-money\"\n            size=\"mini\"\n            @click=\"showCostCalculation\"\n          >费用计算</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"warning\"\n            plain\n            icon=\"el-icon-download\"\n            size=\"mini\"\n            @click=\"handleExport\"\n            v-hasPermi=\"['vehicle:order:export']\"\n          >导出数据</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 订单列表 -->\n      <el-table v-loading=\"loading\" :data=\"orderList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"订单ID\" align=\"center\" prop=\"orderId\" width=\"80\" />\n        <el-table-column label=\"车辆信息\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleInfo ? scope.row.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              {{ scope.row.vehicleInfo ? scope.row.vehicleInfo.licensePlate : '' }}\n            </div>\n            <div style=\"color: #E6A23C; font-size: 12px;\" v-if=\"scope.row.vehicleWeight >= 50\">\n              重量：{{ scope.row.vehicleWeight }}吨\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"队伍信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.teamInfo ? scope.row.teamInfo.teamName : '未知' }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.driverName }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"实际用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.actualStartTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.actualEndTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时长\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #409EFF; font-weight: bold;\">\n              {{ calculateDuration(scope.row.actualStartTime, scope.row.actualEndTime) }}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"费用信息\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.totalCost\">\n              <div style=\"color: #67C23A; font-weight: bold;\">￥{{ scope.row.totalCost }}</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ getCostBearerText(scope.row.costBearer) }}</div>\n            </div>\n            <div v-else style=\"color: #E6A23C;\">待计算</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"订单状态\" align=\"center\" prop=\"orderStatus\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.orderStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.orderStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看详情</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"success\"\n              @click=\"handleConfirm(scope.row)\"\n              v-hasPermi=\"['vehicle:order:dispatch-confirm']\"\n            >确认</el-button>\n            <el-button\n              v-if=\"scope.row.orderStatus === 'team_confirmed'\"\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleCostCalculate(scope.row)\"\n              v-hasPermi=\"['vehicle:order:cost-calculate']\"\n            >计算费用</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailDialogVisible\" width=\"900px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"订单ID\">{{ detailOrder.orderId }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆信息\">\n          {{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.vehicleModel : '未知' }}\n          ({{ detailOrder.vehicleInfo ? detailOrder.vehicleInfo.licensePlate : '' }})\n        </el-descriptions-item>\n        <el-descriptions-item label=\"车辆重量\">{{ detailOrder.vehicleWeight }}吨</el-descriptions-item>\n        <el-descriptions-item label=\"司机\">{{ detailOrder.driverName }}</el-descriptions-item>\n        <el-descriptions-item label=\"队伍\">\n          {{ detailOrder.teamInfo ? detailOrder.teamInfo.teamName : '未知' }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailOrder.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际开始时间\">{{ parseTime(detailOrder.actualStartTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"实际结束时间\">{{ parseTime(detailOrder.actualEndTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车时长\">\n          <span style=\"color: #409EFF; font-weight: bold;\">\n            {{ calculateDuration(detailOrder.actualStartTime, detailOrder.actualEndTime) }}\n          </span>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"费用信息\">\n          <div v-if=\"detailOrder.totalCost\">\n            <div>总费用：<span style=\"color: #67C23A; font-weight: bold;\">￥{{ detailOrder.totalCost }}</span></div>\n            <div>承担方：{{ getCostBearerText(detailOrder.costBearer) }}</div>\n            <div>计量单位：{{ detailOrder.costUnit }}</div>\n            <div>单价：￥{{ detailOrder.unitPrice }}</div>\n          </div>\n          <div v-else style=\"color: #E6A23C;\">费用待计算</div>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"订单状态\">\n          <el-tag :type=\"getStatusTagType(detailOrder.orderStatus)\">\n            {{ getStatusText(detailOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n      </el-descriptions>\n      \n      <!-- 作业照片 -->\n      <div v-if=\"detailOrder.startPhotoUrl || detailOrder.endPhotoUrl\" style=\"margin-top: 20px;\">\n        <h4>作业照片</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" v-if=\"detailOrder.startPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>开始照片</h5>\n              <el-image\n                :src=\"detailOrder.startPhotoUrl\"\n                :preview-src-list=\"[detailOrder.startPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"detailOrder.endPhotoUrl\">\n            <div class=\"photo-section\">\n              <h5>结束照片</h5>\n              <el-image\n                :src=\"detailOrder.endPhotoUrl\"\n                :preview-src-list=\"[detailOrder.endPhotoUrl]\"\n                style=\"width: 100%; height: 200px;\"\n                fit=\"cover\">\n              </el-image>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 确认操作 -->\n      <div v-if=\"detailOrder.orderStatus === 'team_confirmed'\" style=\"margin-top: 20px;\">\n        <el-divider content-position=\"left\">调度室确认</el-divider>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleCostCalculate(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-money\"></i> 计算费用\n            </el-button>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-button type=\"success\" size=\"medium\" @click=\"handleConfirm(detailOrder)\" style=\"width: 100%;\">\n              <i class=\"el-icon-check\"></i> 确认订单\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n    </el-dialog>\n\n    <!-- 费用计算对话框 -->\n    <el-dialog title=\"费用计算\" :visible.sync=\"costDialogVisible\" width=\"600px\" append-to-body>\n      <el-form ref=\"costForm\" :model=\"costForm\" :rules=\"costRules\" label-width=\"120px\">\n        <el-form-item label=\"车辆信息\">\n          <div class=\"cost-info\">\n            <div>车辆型号：{{ currentOrder.vehicleInfo ? currentOrder.vehicleInfo.vehicleModel : '未知' }}</div>\n            <div>车辆重量：{{ currentOrder.vehicleWeight }}吨</div>\n            <div>用车时长：{{ calculateDuration(currentOrder.actualStartTime, currentOrder.actualEndTime) }}</div>\n          </div>\n        </el-form-item>\n        \n        <el-form-item label=\"计量单位\" prop=\"costUnit\">\n          <el-radio-group v-model=\"costForm.costUnit\">\n            <el-radio label=\"hour\">小时</el-radio>\n            <el-radio label=\"day\">天</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"单价\" prop=\"unitPrice\">\n          <el-input-number\n            v-model=\"costForm.unitPrice\"\n            :precision=\"2\"\n            :min=\"0\"\n            :max=\"99999\"\n            placeholder=\"请输入单价\"\n            style=\"width: 200px;\">\n          </el-input-number>\n          <span style=\"margin-left: 10px;\">元/{{ costForm.costUnit === 'hour' ? '小时' : '天' }}</span>\n        </el-form-item>\n        \n        <el-form-item label=\"费用承担方\" prop=\"costBearer\">\n          <el-radio-group v-model=\"costForm.costBearer\">\n            <el-radio label=\"project\">项目承担</el-radio>\n            <el-radio label=\"team\">队伍承担</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        \n        <el-form-item label=\"预计总费用\">\n          <div class=\"cost-preview\">\n            <span style=\"font-size: 18px; color: #67C23A; font-weight: bold;\">\n              ￥{{ calculateTotalCost() }}\n            </span>\n          </div>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"costDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitCostCalculation\" :loading=\"costLoading\">确认计算</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPendingConfirmOrders, getOrder, dispatchConfirmOrder, calculateOrderCost } from \"@/api/vehicle/order\";\n\nexport default {\n  name: \"DispatchConfirm\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 订单列表\n      orderList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        confirmedCount: 0,\n        over50TonCount: 0,\n        todayCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        orderStatus: 'team_confirmed'\n      },\n      // 日期范围\n      dateRange: [],\n      // 详情对话框\n      detailDialogVisible: false,\n      detailOrder: {},\n      // 费用计算对话框\n      costDialogVisible: false,\n      costLoading: false,\n      currentOrder: {},\n      costForm: {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: 'project'\n      },\n      // 费用计算表单验证规则\n      costRules: {\n        unitPrice: [\n          { required: true, message: \"请输入单价\", trigger: \"blur\" },\n          { type: 'number', min: 0.01, message: \"单价必须大于0\", trigger: \"blur\" }\n        ],\n        costBearer: [\n          { required: true, message: \"请选择费用承担方\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询订单列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (this.dateRange && this.dateRange.length === 2) {\n        this.queryParams.params[\"beginActualEndTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endActualEndTime\"] = this.dateRange[1];\n      }\n      \n      getPendingConfirmOrders('dispatch').then(response => {\n        this.orderList = response.data;\n        this.total = response.data.length;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 8,\n        confirmedCount: 15,\n        over50TonCount: 3,\n        todayCount: 5\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    /** 刷新列表 */\n    refreshList() {\n      this.getList();\n      this.loadStatistics();\n      this.$modal.msgSuccess(\"刷新成功\");\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.orderId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getOrder(row.orderId).then(response => {\n        this.detailOrder = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 确认订单 */\n    handleConfirm(row) {\n      this.$modal.confirm('确认该订单的费用和用车情况？').then(() => {\n        return dispatchConfirmOrder(row.orderId);\n      }).then(() => {\n        this.$modal.msgSuccess(\"确认成功\");\n        this.detailDialogVisible = false;\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 费用计算 */\n    handleCostCalculate(row) {\n      this.currentOrder = row;\n      this.costDialogVisible = true;\n      this.costForm = {\n        costUnit: 'hour',\n        unitPrice: 0,\n        costBearer: row.vehicleWeight >= 50 ? 'project' : 'team'\n      };\n    },\n    \n    /** 提交费用计算 */\n    submitCostCalculation() {\n      this.$refs[\"costForm\"].validate(valid => {\n        if (valid) {\n          this.costLoading = true;\n          const data = {\n            orderId: this.currentOrder.orderId,\n            costUnit: this.costForm.costUnit,\n            unitPrice: this.costForm.unitPrice,\n            costBearer: this.costForm.costBearer,\n            totalCost: this.calculateTotalCost()\n          };\n          \n          calculateOrderCost(data).then(response => {\n            this.$modal.msgSuccess(\"费用计算成功\");\n            this.costDialogVisible = false;\n            this.detailDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.costLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 计算总费用 */\n    calculateTotalCost() {\n      if (!this.currentOrder.actualStartTime || !this.currentOrder.actualEndTime || !this.costForm.unitPrice) {\n        return 0;\n      }\n      \n      const diff = new Date(this.currentOrder.actualEndTime).getTime() - new Date(this.currentOrder.actualStartTime).getTime();\n      let duration = 0;\n      \n      if (this.costForm.costUnit === 'hour') {\n        duration = Math.ceil(diff / (1000 * 60 * 60)); // 向上取整小时\n      } else {\n        duration = Math.ceil(diff / (1000 * 60 * 60 * 24)); // 向上取整天\n      }\n      \n      return (duration * this.costForm.unitPrice).toFixed(2);\n    },\n    \n    /** 批量确认 */\n    handleBatchConfirm() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要确认的订单\");\n        return;\n      }\n      \n      this.$modal.confirm(`确认选中的 ${this.ids.length} 个订单？`).then(() => {\n        // TODO: 调用批量确认接口\n        this.$modal.msgSuccess(\"批量确认成功\");\n        this.getList();\n      }).catch(() => {});\n    },\n    \n    /** 显示费用计算 */\n    showCostCalculation() {\n      this.$modal.msgInfo(\"费用计算功能开发中...\");\n    },\n    \n    /** 导出数据 */\n    handleExport() {\n      this.$modal.msgInfo(\"导出功能开发中...\");\n    },\n    \n    /** 计算用车时长 */\n    calculateDuration(startTime, endTime) {\n      if (!startTime || !endTime) return '0小时';\n      \n      const diff = new Date(endTime).getTime() - new Date(startTime).getTime();\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      \n      return `${hours}小时${minutes}分钟`;\n    },\n    \n    /** 获取费用承担方文本 */\n    getCostBearerText(costBearer) {\n      const bearerMap = {\n        'project': '项目承担',\n        'team': '队伍承担'\n      };\n      return bearerMap[costBearer] || '未知';\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'team_confirmed': 'warning',\n        'dispatch_confirmed': 'success',\n        'pending_manager': 'primary'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'team_confirmed': '队伍已确认',\n        'dispatch_confirmed': '调度已确认',\n        'pending_manager': '待主管审批'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n\n.photo-section {\n  text-align: center;\n}\n\n.photo-section h5 {\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.cost-info {\n  background: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  line-height: 1.8;\n}\n\n.cost-preview {\n  background: #f0f9ff;\n  padding: 15px;\n  border-radius: 4px;\n  text-align: center;\n  border: 1px solid #b3d8ff;\n}\n</style>\n"]}]}