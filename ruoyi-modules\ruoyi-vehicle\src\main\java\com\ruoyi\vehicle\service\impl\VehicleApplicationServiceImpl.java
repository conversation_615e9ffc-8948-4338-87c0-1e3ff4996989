package com.ruoyi.vehicle.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.vehicle.mapper.VehicleApplicationMapper;
import com.ruoyi.vehicle.mapper.VehicleInfoMapper;
import com.ruoyi.vehicle.domain.VehicleApplication;
import com.ruoyi.vehicle.domain.VehicleInfo;
import com.ruoyi.vehicle.service.IVehicleApplicationService;
import com.ruoyi.vehicle.service.IVehicleNotificationService;

/**
 * 机械用车申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleApplicationServiceImpl implements IVehicleApplicationService 
{
    @Autowired
    private VehicleApplicationMapper vehicleApplicationMapper;

    @Autowired
    private VehicleInfoMapper vehicleInfoMapper;

    @Autowired
    private IVehicleNotificationService notificationService;

    /**
     * 查询机械用车申请
     * 
     * @param applicationId 机械用车申请主键
     * @return 机械用车申请
     */
    @Override
    public VehicleApplication selectVehicleApplicationByApplicationId(Long applicationId)
    {
        return vehicleApplicationMapper.selectVehicleApplicationByApplicationId(applicationId);
    }

    /**
     * 查询机械用车申请列表
     * 
     * @param vehicleApplication 机械用车申请
     * @return 机械用车申请
     */
    @Override
    public List<VehicleApplication> selectVehicleApplicationList(VehicleApplication vehicleApplication)
    {
        return vehicleApplicationMapper.selectVehicleApplicationList(vehicleApplication);
    }

    /**
     * 新增机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    @Override
    public int insertVehicleApplication(VehicleApplication vehicleApplication)
    {
        vehicleApplication.setCreateTime(DateUtils.getNowDate());
        vehicleApplication.setApprovalStatus("pending"); // 默认待审批状态
        return vehicleApplicationMapper.insertVehicleApplication(vehicleApplication);
    }

    /**
     * 修改机械用车申请
     * 
     * @param vehicleApplication 机械用车申请
     * @return 结果
     */
    @Override
    public int updateVehicleApplication(VehicleApplication vehicleApplication)
    {
        vehicleApplication.setUpdateTime(DateUtils.getNowDate());
        return vehicleApplicationMapper.updateVehicleApplication(vehicleApplication);
    }

    /**
     * 批量删除机械用车申请
     * 
     * @param applicationIds 需要删除的机械用车申请主键
     * @return 结果
     */
    @Override
    public int deleteVehicleApplicationByApplicationIds(Long[] applicationIds)
    {
        return vehicleApplicationMapper.deleteVehicleApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除机械用车申请信息
     * 
     * @param applicationId 机械用车申请主键
     * @return 结果
     */
    @Override
    public int deleteVehicleApplicationByApplicationId(Long applicationId)
    {
        return vehicleApplicationMapper.deleteVehicleApplicationByApplicationId(applicationId);
    }

    /**
     * 提交用车申请
     * 
     * @param vehicleApplication 用车申请信息
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int submitApplication(VehicleApplication vehicleApplication, String operName)
    {
        vehicleApplication.setCreateBy(operName);
        vehicleApplication.setApprovalStatus("pending");
        int result = insertVehicleApplication(vehicleApplication);
        
        if (result > 0) {
            // TODO: 发送消息通知调度室审批
            notificationService.sendApplicationNotification(vehicleApplication.getApplicationId(), "submit", operName);
        }
        
        return result;
    }

    /**
     * 审批用车申请
     * 
     * @param applicationId 申请ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int approveApplication(Long applicationId, String approvalStatus, String approvalComments, String operName)
    {
        VehicleApplication application = selectVehicleApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }
        
        application.setApprovalStatus(approvalStatus);
        application.setUpdateBy(operName);
        application.setUpdateTime(DateUtils.getNowDate());
        application.setRemark(approvalComments);
        
        int result = updateVehicleApplication(application);
        
        if (result > 0) {
            // TODO: 发送审批结果通知给申请人
            notificationService.sendApplicationNotification(applicationId, "approve", operName);
        }
        
        return result;
    }

    /**
     * 分配车辆和司机
     * 
     * @param applicationId 申请ID
     * @param vehicleId 车辆ID
     * @param driverName 司机姓名
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int assignVehicleAndDriver(Long applicationId, Long vehicleId, String driverName, String operName)
    {
        VehicleApplication application = selectVehicleApplicationByApplicationId(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }
        
        // 检查车辆是否可用
        VehicleInfo vehicle = vehicleInfoMapper.selectVehicleInfoByVehicleId(vehicleId);
        if (vehicle == null || !"可用".equals(vehicle.getVehicleStatus())) {
            throw new ServiceException("车辆不可用");
        }
        
        application.setAssignedVehicleId(vehicleId);
        application.setAssignedDriver(driverName);
        application.setScheduler(operName);
        application.setScheduleTime(DateUtils.getNowDate());
        application.setUpdateBy(operName);
        application.setUpdateTime(DateUtils.getNowDate());
        
        int result = updateVehicleApplication(application);
        
        if (result > 0) {
            // TODO: 发送调度通知给司机和申请队伍
            notificationService.sendApplicationNotification(applicationId, "assign", operName);
        }
        
        return result;
    }

    /**
     * 根据审批状态查询申请列表
     * 
     * @param approvalStatus 审批状态
     * @return 申请集合
     */
    @Override
    public List<VehicleApplication> selectVehicleApplicationByStatus(String approvalStatus)
    {
        return vehicleApplicationMapper.selectVehicleApplicationByStatus(approvalStatus);
    }

    /**
     * 根据队伍ID查询申请列表
     * 
     * @param teamId 队伍ID
     * @return 申请集合
     */
    @Override
    public List<VehicleApplication> selectVehicleApplicationByTeamId(Long teamId)
    {
        return vehicleApplicationMapper.selectVehicleApplicationByTeamId(teamId);
    }

    /**
     * 查询待调度的申请列表
     * 
     * @return 申请集合
     */
    @Override
    public List<VehicleApplication> selectPendingScheduleApplications()
    {
        return vehicleApplicationMapper.selectPendingScheduleApplications();
    }

    /**
     * 批量审批申请
     * 
     * @param applicationIds 申请ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int batchApproveApplications(Long[] applicationIds, String approvalStatus, String approvalComments, String operName)
    {
        int successCount = 0;
        for (Long applicationId : applicationIds) {
            try {
                int result = approveApplication(applicationId, approvalStatus, approvalComments, operName);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他申请
                continue;
            }
        }
        return successCount;
    }
}
