{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\src\\router\\index.js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\router\\index.js", "mtime": 1754146964882}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_layout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "redirect", "name", "meta", "title", "icon", "affix", "dynamicRoutes", "permissions", "activeMenu", "routerPush", "prototype", "push", "routerReplace", "replace", "location", "call", "catch", "err", "_default", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes"], "sources": ["D:/Work/car/AA/ruoyi-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\n/* Layout */\r\nimport Layout from '@/layout'\r\n\r\n/**\r\n * Note: 路由配置项\r\n *\r\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\r\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\r\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\r\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\r\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\r\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\r\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\r\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\r\n * roles: ['admin', 'common']       // 访问路由的角色权限\r\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\r\n * meta : {\r\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\r\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\r\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\r\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\r\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\r\n  }\r\n */\r\n\r\n// 公共路由\r\nexport const constantRoutes = [\r\n  {\r\n    path: '/redirect',\r\n    component: Layout,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: '/redirect/:path(.*)',\r\n        component: () => import('@/views/redirect')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    component: () => import('@/views/login'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/register',\r\n    component: () => import('@/views/register'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/404',\r\n    component: () => import('@/views/error/404'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/401',\r\n    component: () => import('@/views/error/401'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '',\r\n    component: Layout,\r\n    redirect: 'index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/index'),\r\n        name: 'Index',\r\n        meta: { title: '首页', icon: 'dashboard', affix: true }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/user',\r\n    component: Layout,\r\n    hidden: true,\r\n    redirect: 'noredirect',\r\n    children: [\r\n      {\r\n        path: 'profile',\r\n        component: () => import('@/views/system/user/profile/index'),\r\n        name: 'Profile',\r\n        meta: { title: '个人中心', icon: 'user' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 动态路由，基于用户权限动态去加载\r\nexport const dynamicRoutes = [\r\n  {\r\n    path: '/system/user-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:user:edit'],\r\n    children: [\r\n      {\r\n        path: 'role/:userId(\\\\d+)',\r\n        component: () => import('@/views/system/user/authRole'),\r\n        name: 'AuthRole',\r\n        meta: { title: '分配角色', activeMenu: '/system/user' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/role-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:role:edit'],\r\n    children: [\r\n      {\r\n        path: 'user/:roleId(\\\\d+)',\r\n        component: () => import('@/views/system/role/authUser'),\r\n        name: 'AuthUser',\r\n        meta: { title: '分配用户', activeMenu: '/system/role' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/dict-data',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:dict:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:dictId(\\\\d+)',\r\n        component: () => import('@/views/system/dict/data'),\r\n        name: 'Data',\r\n        meta: { title: '字典数据', activeMenu: '/system/dict' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/monitor/job-log',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['monitor:job:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:jobId(\\\\d+)',\r\n        component: () => import('@/views/monitor/job/log'),\r\n        name: 'JobLog',\r\n        meta: { title: '调度日志', activeMenu: '/monitor/job' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/tool/gen-edit',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['tool:gen:edit'],\r\n    children: [\r\n      {\r\n        path: 'index/:tableId(\\\\d+)',\r\n        component: () => import('@/views/tool/gen/editTable'),\r\n        name: 'GenEdit',\r\n        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/vehicle/application/apply',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['vehicle:application:add'],\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/vehicle/application/apply'),\r\n        name: 'ApplicationApply',\r\n        meta: { title: '申请用车', activeMenu: '/vehicle/application' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/vehicle/application/dispatch',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['vehicle:application:dispatch'],\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/vehicle/application/dispatch'),\r\n        name: 'ApplicationDispatch',\r\n        meta: { title: '车辆调度', activeMenu: '/vehicle/application' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 防止连续点击多次路由报错\r\nlet routerPush = Router.prototype.push\r\nlet routerReplace = Router.prototype.replace\r\n// push\r\nRouter.prototype.push = function push(location) {\r\n  return routerPush.call(this, location).catch(err => err)\r\n}\r\n// replace\r\nRouter.prototype.replace = function push(location) {\r\n  return routerReplace.call(this, location).catch(err => err)\r\n}\r\n\r\nexport default new Router({\r\n  mode: 'history', // 去掉url中的#\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes: constantRoutes\r\n})\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAC5B;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;IAAA;EAAA,CAAC;EAC3CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,OAAO;EACjBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;MAAA;IAAA,CAAC;IACxCmB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK;EACtD,CAAC;AAEL,CAAC,EACD;EACEf,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZO,QAAQ,EAAE,YAAY;EACtBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,CACF;;AAED;AACO,IAAME,aAAa,GAAAjB,OAAA,CAAAiB,aAAA,GAAG,CAC3B;EACEhB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,eAAe,CAAC;EAC9Bb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEK,UAAU,EAAE;IAAY;EACnD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,4BAA4B;EAClCC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,yBAAyB,CAAC;EACxCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAuB;EAC5D,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,+BAA+B;EACrCC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,8BAA8B,CAAC;EAC7Cb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sCAAsC;MAAA;IAAA,CAAC;IAC/DmB,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAuB;EAC5D,CAAC;AAEL,CAAC,CACF;;AAED;AACA,IAAIC,UAAU,GAAGtB,kBAAM,CAACuB,SAAS,CAACC,IAAI;AACtC,IAAIC,aAAa,GAAGzB,kBAAM,CAACuB,SAAS,CAACG,OAAO;AAC5C;AACA1B,kBAAM,CAACuB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACG,QAAQ,EAAE;EAC9C,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC1D,CAAC;AACD;AACA9B,kBAAM,CAACuB,SAAS,CAACG,OAAO,GAAG,SAASF,IAAIA,CAACG,QAAQ,EAAE;EACjD,OAAOF,aAAa,CAACG,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AAAA,IAAAC,QAAA,GAAA7B,OAAA,CAAAU,OAAA,GAEc,IAAIZ,kBAAM,CAAC;EACxBgC,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAElC;AACV,CAAC,CAAC", "ignoreList": []}]}