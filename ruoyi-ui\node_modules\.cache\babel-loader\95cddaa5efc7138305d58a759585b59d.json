{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\dispatch.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\dispatch.vue", "mtime": 1754142892050}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_application", "require", "_info", "name", "dicts", "data", "loading", "ids", "multiple", "total", "applicationList", "statistics", "pendingCount", "availableCount", "busyCount", "todayDispatchCount", "queryParams", "pageNum", "pageSize", "vehicleType", "approvalStatus", "dispatchDialogVisible", "dispatchLoading", "currentApplication", "dispatchForm", "assignedVehicleId", "assignedDriver", "dispatchRemark", "availableVehicles", "availableDrivers", "statusDialogVisible", "statusLoading", "vehicleStatusList", "dispatchRules", "required", "message", "trigger", "created", "getList", "loadStatistics", "methods", "_this", "listApplication", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "applicationId", "length", "handleView", "row", "$router", "push", "handleDispatch", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "vehicleRes", "driverRes", "_t", "w", "_context", "p", "n", "getAvailableVehicles", "vehicleModel", "startTime", "endTime", "v", "listInfo", "filter", "<PERSON><PERSON><PERSON>", "driverPhone", "$modal", "msgError", "a", "handleVehicleChange", "vehicleId", "selectedVehicle", "find", "submitDispatch", "_this3", "$refs", "validate", "valid", "_objectSpread2", "assignVehicle", "msgSuccess", "catch", "handleBatchDispatch", "msgInfo", "handleBatchApprove", "showVehicleStatus", "_this4", "_callee2", "_t2", "_context2", "f", "getStatusTagType", "status", "statusMap", "getStatusText", "getVehicleStatusType", "getVehicleStatusText", "goBack"], "sources": ["src/views/vehicle/application/dispatch.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span style=\"font-size: 18px; font-weight: bold;\">车辆调度安排</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"goBack\">返回列表</el-button>\n      </div>\n\n      <!-- 统计信息 -->\n      <el-row :gutter=\"20\" class=\"mb20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.pendingCount }}</div>\n              <div class=\"stat-label\">待调度申请</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.availableCount }}</div>\n              <div class=\"stat-label\">可用车辆</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.busyCount }}</div>\n              <div class=\"stat-label\">使用中车辆</div>\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-item\">\n              <div class=\"stat-value\">{{ statistics.todayDispatchCount }}</div>\n              <div class=\"stat-label\">今日调度</div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 筛选条件 -->\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n        <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n          <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable @change=\"getList\">\n            <el-option\n              v-for=\"dict in dict.type.vehicle_type\"\n              :key=\"dict.value\"\n              :label=\"dict.label\"\n              :value=\"dict.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"申请状态\" prop=\"approvalStatus\">\n          <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择状态\" clearable @change=\"getList\">\n            <el-option label=\"待审批\" value=\"pending\"></el-option>\n            <el-option label=\"已审批\" value=\"approved\"></el-option>\n            <el-option label=\"待调度\" value=\"approved\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"紧急程度\" prop=\"urgency\">\n          <el-select v-model=\"queryParams.urgency\" placeholder=\"请选择紧急程度\" clearable @change=\"getList\">\n            <el-option label=\"紧急\" value=\"urgent\"></el-option>\n            <el-option label=\"普通\" value=\"normal\"></el-option>\n            <el-option label=\"不急\" value=\"low\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <!-- 操作按钮 -->\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"primary\"\n            plain\n            icon=\"el-icon-s-operation\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchDispatch\"\n            v-hasPermi=\"['vehicle:application:dispatch']\"\n          >批量调度</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"success\"\n            plain\n            icon=\"el-icon-check\"\n            size=\"mini\"\n            :disabled=\"multiple\"\n            @click=\"handleBatchApprove\"\n            v-hasPermi=\"['vehicle:application:approve']\"\n          >批量审批</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button\n            type=\"info\"\n            plain\n            icon=\"el-icon-view\"\n            size=\"mini\"\n            @click=\"showVehicleStatus\"\n          >车辆状态</el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 申请列表 -->\n      <el-table v-loading=\"loading\" :data=\"applicationList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"申请ID\" align=\"center\" prop=\"applicationId\" width=\"80\" />\n        <el-table-column label=\"申请标题\" align=\"center\" prop=\"applicationTitle\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"车辆需求\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div>{{ scope.row.vehicleType }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.vehicleModel }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"用车时间\" align=\"center\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <div>{{ parseTime(scope.row.startTime, '{m}-{d} {h}:{i}') }}</div>\n            <div style=\"color: #909399; font-size: 12px;\">\n              至 {{ parseTime(scope.row.endTime, '{m}-{d} {h}:{i}') }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"申请人\" align=\"center\" prop=\"applicant\" width=\"100\" />\n        <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\" size=\"mini\">\n              {{ getStatusText(scope.row.approvalStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"分配情况\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <div v-if=\"scope.row.assignedVehicleId\">\n              <div style=\"color: #67C23A;\">已分配车辆</div>\n              <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.assignedDriver }}</div>\n            </div>\n            <div v-else style=\"color: #E6A23C;\">待分配</div>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-view\"\n              @click=\"handleView(scope.row)\"\n            >查看</el-button>\n            <el-button\n              v-if=\"scope.row.approvalStatus === 'approved' && !scope.row.assignedVehicleId\"\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-s-operation\"\n              @click=\"handleDispatch(scope.row)\"\n              v-hasPermi=\"['vehicle:application:dispatch']\"\n            >调度</el-button>\n            <el-button\n              v-if=\"scope.row.approvalStatus === 'pending'\"\n              size=\"mini\"\n              type=\"text\"\n              icon=\"el-icon-check\"\n              @click=\"handleApprove(scope.row)\"\n              v-hasPermi=\"['vehicle:application:approve']\"\n            >审批</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n\n    <!-- 调度对话框 -->\n    <el-dialog title=\"车辆调度\" :visible.sync=\"dispatchDialogVisible\" width=\"800px\" append-to-body>\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\">\n        <el-form-item label=\"申请信息\">\n          <el-descriptions :column=\"2\" size=\"small\">\n            <el-descriptions-item label=\"申请标题\">{{ currentApplication.applicationTitle }}</el-descriptions-item>\n            <el-descriptions-item label=\"车辆需求\">{{ currentApplication.vehicleType }} - {{ currentApplication.vehicleModel }}</el-descriptions-item>\n            <el-descriptions-item label=\"用车时间\">{{ parseTime(currentApplication.startTime) }} 至 {{ parseTime(currentApplication.endTime) }}</el-descriptions-item>\n            <el-descriptions-item label=\"用车地点\">{{ currentApplication.usageLocation }}</el-descriptions-item>\n          </el-descriptions>\n        </el-form-item>\n        \n        <el-form-item label=\"分配车辆\" prop=\"assignedVehicleId\">\n          <el-select v-model=\"dispatchForm.assignedVehicleId\" placeholder=\"请选择车辆\" @change=\"handleVehicleChange\">\n            <el-option\n              v-for=\"vehicle in availableVehicles\"\n              :key=\"vehicle.vehicleId\"\n              :label=\"`${vehicle.vehicleModel} (${vehicle.licensePlate})`\"\n              :value=\"vehicle.vehicleId\">\n              <span style=\"float: left\">{{ vehicle.vehicleModel }}</span>\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ vehicle.licensePlate }}</span>\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"分配司机\" prop=\"assignedDriver\">\n          <el-select v-model=\"dispatchForm.assignedDriver\" placeholder=\"请选择司机\">\n            <el-option\n              v-for=\"driver in availableDrivers\"\n              :key=\"driver.driverName\"\n              :label=\"`${driver.driverName} (${driver.driverPhone})`\"\n              :value=\"driver.driverName\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"调度备注\" prop=\"dispatchRemark\">\n          <el-input\n            v-model=\"dispatchForm.dispatchRemark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入调度备注\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitDispatch\" :loading=\"dispatchLoading\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 车辆状态对话框 -->\n    <el-dialog title=\"车辆状态总览\" :visible.sync=\"statusDialogVisible\" width=\"1000px\">\n      <el-table :data=\"vehicleStatusList\" v-loading=\"statusLoading\">\n        <el-table-column label=\"车辆类型\" prop=\"vehicleType\" />\n        <el-table-column label=\"车辆型号\" prop=\"vehicleModel\" />\n        <el-table-column label=\"车牌号\" prop=\"licensePlate\" />\n        <el-table-column label=\"司机\" prop=\"driverName\" />\n        <el-table-column label=\"状态\" prop=\"vehicleStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getVehicleStatusType(scope.row.vehicleStatus)\">\n              {{ getVehicleStatusText(scope.row.vehicleStatus) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"当前位置\" prop=\"currentLocation\" />\n        <el-table-column label=\"下次可用时间\" prop=\"nextAvailableTime\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.nextAvailableTime\">{{ parseTime(scope.row.nextAvailableTime) }}</span>\n            <span v-else style=\"color: #67C23A;\">立即可用</span>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listApplication, assignVehicle, batchAssignVehicle, getAvailableVehicles } from \"@/api/vehicle/application\";\nimport { listInfo } from \"@/api/vehicle/info\";\n\nexport default {\n  name: \"ApplicationDispatch\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 总条数\n      total: 0,\n      // 申请列表\n      applicationList: [],\n      // 统计信息\n      statistics: {\n        pendingCount: 0,\n        availableCount: 0,\n        busyCount: 0,\n        todayDispatchCount: 0\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleType: null,\n        approvalStatus: 'approved'\n      },\n      // 调度对话框\n      dispatchDialogVisible: false,\n      dispatchLoading: false,\n      currentApplication: {},\n      dispatchForm: {\n        assignedVehicleId: null,\n        assignedDriver: '',\n        dispatchRemark: ''\n      },\n      availableVehicles: [],\n      availableDrivers: [],\n      // 车辆状态对话框\n      statusDialogVisible: false,\n      statusLoading: false,\n      vehicleStatusList: [],\n      // 调度表单验证规则\n      dispatchRules: {\n        assignedVehicleId: [\n          { required: true, message: \"请选择分配车辆\", trigger: \"change\" }\n        ],\n        assignedDriver: [\n          { required: true, message: \"请选择分配司机\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.loadStatistics();\n  },\n  methods: {\n    /** 查询申请列表 */\n    getList() {\n      this.loading = true;\n      listApplication(this.queryParams).then(response => {\n        this.applicationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    \n    /** 加载统计信息 */\n    loadStatistics() {\n      // TODO: 调用统计接口\n      this.statistics = {\n        pendingCount: 12,\n        availableCount: 8,\n        busyCount: 15,\n        todayDispatchCount: 6\n      };\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.applicationId)\n      this.multiple = !selection.length\n    },\n    \n    /** 查看申请详情 */\n    handleView(row) {\n      this.$router.push('/vehicle/application/detail/' + row.applicationId);\n    },\n    \n    /** 调度按钮操作 */\n    async handleDispatch(row) {\n      this.currentApplication = row;\n      this.dispatchDialogVisible = true;\n      \n      // 加载可用车辆和司机\n      try {\n        const vehicleRes = await getAvailableVehicles({\n          vehicleType: row.vehicleType,\n          vehicleModel: row.vehicleModel,\n          startTime: row.startTime,\n          endTime: row.endTime\n        });\n        this.availableVehicles = vehicleRes.data;\n        \n        // 加载司机列表\n        const driverRes = await listInfo({ vehicleType: row.vehicleType });\n        this.availableDrivers = driverRes.rows.filter(v => v.driverName).map(v => ({\n          driverName: v.driverName,\n          driverPhone: v.driverPhone\n        }));\n      } catch (error) {\n        this.$modal.msgError(\"加载可用资源失败\");\n      }\n    },\n    \n    /** 车辆变化处理 */\n    handleVehicleChange(vehicleId) {\n      const selectedVehicle = this.availableVehicles.find(v => v.vehicleId === vehicleId);\n      if (selectedVehicle && selectedVehicle.driverName) {\n        this.dispatchForm.assignedDriver = selectedVehicle.driverName;\n      }\n    },\n    \n    /** 提交调度 */\n    submitDispatch() {\n      this.$refs[\"dispatchForm\"].validate(valid => {\n        if (valid) {\n          this.dispatchLoading = true;\n          const data = {\n            ...this.dispatchForm,\n            applicationId: this.currentApplication.applicationId\n          };\n          \n          assignVehicle(data).then(response => {\n            this.$modal.msgSuccess(\"调度成功\");\n            this.dispatchDialogVisible = false;\n            this.getList();\n          }).catch(() => {\n            this.dispatchLoading = false;\n          });\n        }\n      });\n    },\n    \n    /** 批量调度 */\n    handleBatchDispatch() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要调度的申请\");\n        return;\n      }\n      this.$modal.msgInfo(\"批量调度功能开发中...\");\n    },\n    \n    /** 批量审批 */\n    handleBatchApprove() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要审批的申请\");\n        return;\n      }\n      this.$modal.msgInfo(\"批量审批功能开发中...\");\n    },\n    \n    /** 显示车辆状态 */\n    async showVehicleStatus() {\n      this.statusDialogVisible = true;\n      this.statusLoading = true;\n      \n      try {\n        const response = await listInfo();\n        this.vehicleStatusList = response.rows;\n      } catch (error) {\n        this.$modal.msgError(\"获取车辆状态失败\");\n      } finally {\n        this.statusLoading = false;\n      }\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending': 'warning',\n        'approved': 'success',\n        'rejected': 'danger',\n        'dispatched': 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待审批',\n        'approved': '已审批',\n        'rejected': '已拒绝',\n        'dispatched': '已调度'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 获取车辆状态类型 */\n    getVehicleStatusType(status) {\n      const statusMap = {\n        'available': 'success',\n        'busy': 'warning',\n        'maintenance': 'info',\n        'fault': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取车辆状态文本 */\n    getVehicleStatusText(status) {\n      const statusMap = {\n        'available': '可用',\n        'busy': '使用中',\n        'maintenance': '维护中',\n        'fault': '故障'\n      };\n      return statusMap[status] || '未知';\n    },\n    \n    /** 返回列表 */\n    goBack() {\n      this.$router.push('/vehicle/application');\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.stat-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.12);\n}\n\n.stat-item {\n  padding: 20px;\n}\n\n.stat-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #409EFF;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAwQA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,eAAA;MACA;MACAC,UAAA;QACAC,YAAA;QACAC,cAAA;QACAC,SAAA;QACAC,kBAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;MACA;MACA;MACAC,qBAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,YAAA;QACAC,iBAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACAC,iBAAA;MACAC,gBAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MACAC,iBAAA;MACA;MACAC,aAAA;QACAR,iBAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,cAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,4BAAA,OAAA1B,WAAA,EAAA2B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA/B,eAAA,GAAAkC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhC,KAAA,GAAAmC,QAAA,CAAAnC,KAAA;QACAgC,KAAA,CAAAnC,OAAA;MACA;IACA;IAEA,aACAiC,cAAA,WAAAA,eAAA;MACA;MACA,KAAA5B,UAAA;QACAC,YAAA;QACAC,cAAA;QACAC,SAAA;QACAC,kBAAA;MACA;IACA;IAEA,aACA+B,WAAA,WAAAA,YAAA;MACA,KAAA9B,WAAA,CAAAC,OAAA;MACA,KAAAqB,OAAA;IACA;IAEA,aACAS,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3C,GAAA,GAAA2C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,aAAA;MAAA;MACA,KAAA7C,QAAA,IAAA0C,SAAA,CAAAI,MAAA;IACA;IAEA,aACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,kCAAAF,GAAA,CAAAH,aAAA;IACA;IAEA,aACAM,cAAA,WAAAA,eAAAH,GAAA;MAAA,IAAAI,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,UAAA,EAAAC,SAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cACAZ,MAAA,CAAArC,kBAAA,GAAAiC,GAAA;cACAI,MAAA,CAAAvC,qBAAA;;cAEA;cAAAiD,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,iCAAA;gBACAtD,WAAA,EAAAqC,GAAA,CAAArC,WAAA;gBACAuD,YAAA,EAAAlB,GAAA,CAAAkB,YAAA;gBACAC,SAAA,EAAAnB,GAAA,CAAAmB,SAAA;gBACAC,OAAA,EAAApB,GAAA,CAAAoB;cACA;YAAA;cALAV,UAAA,GAAAI,QAAA,CAAAO,CAAA;cAMAjB,MAAA,CAAAhC,iBAAA,GAAAsC,UAAA,CAAA7D,IAAA;;cAEA;cAAAiE,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAM,cAAA;gBAAA3D,WAAA,EAAAqC,GAAA,CAAArC;cAAA;YAAA;cAAAgD,SAAA,GAAAG,QAAA,CAAAO,CAAA;cACAjB,MAAA,CAAA/B,gBAAA,GAAAsC,SAAA,CAAAtB,IAAA,CAAAkC,MAAA,WAAAF,CAAA;gBAAA,OAAAA,CAAA,CAAAG,UAAA;cAAA,GAAA7B,GAAA,WAAA0B,CAAA;gBAAA;kBACAG,UAAA,EAAAH,CAAA,CAAAG,UAAA;kBACAC,WAAA,EAAAJ,CAAA,CAAAI;gBACA;cAAA;cAAAX,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAEAjB,MAAA,CAAAsB,MAAA,CAAAC,QAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA,aACAoB,mBAAA,WAAAA,oBAAAC,SAAA;MACA,IAAAC,eAAA,QAAA3D,iBAAA,CAAA4D,IAAA,WAAAX,CAAA;QAAA,OAAAA,CAAA,CAAAS,SAAA,KAAAA,SAAA;MAAA;MACA,IAAAC,eAAA,IAAAA,eAAA,CAAAP,UAAA;QACA,KAAAxD,YAAA,CAAAE,cAAA,GAAA6D,eAAA,CAAAP,UAAA;MACA;IACA;IAEA,WACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,iBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAApE,eAAA;UACA,IAAAjB,IAAA,OAAAyF,cAAA,CAAAhC,OAAA,MAAAgC,cAAA,CAAAhC,OAAA,MACA4B,MAAA,CAAAlE,YAAA;YACA6B,aAAA,EAAAqC,MAAA,CAAAnE,kBAAA,CAAA8B;UAAA,EACA;UAEA,IAAA0C,0BAAA,EAAA1F,IAAA,EAAAsC,IAAA,WAAAC,QAAA;YACA8C,MAAA,CAAAR,MAAA,CAAAc,UAAA;YACAN,MAAA,CAAArE,qBAAA;YACAqE,MAAA,CAAApD,OAAA;UACA,GAAA2D,KAAA;YACAP,MAAA,CAAApE,eAAA;UACA;QACA;MACA;IACA;IAEA,WACA4E,mBAAA,WAAAA,oBAAA;MACA,SAAA3F,GAAA,CAAA+C,MAAA;QACA,KAAA4B,MAAA,CAAAC,QAAA;QACA;MACA;MACA,KAAAD,MAAA,CAAAiB,OAAA;IACA;IAEA,WACAC,kBAAA,WAAAA,mBAAA;MACA,SAAA7F,GAAA,CAAA+C,MAAA;QACA,KAAA4B,MAAA,CAAAC,QAAA;QACA;MACA;MACA,KAAAD,MAAA,CAAAiB,OAAA;IACA;IAEA,aACAE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAuC,SAAA;QAAA,IAAA3D,QAAA,EAAA4D,GAAA;QAAA,WAAAzC,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAoC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,CAAA,GAAAkC,SAAA,CAAAjC,CAAA;YAAA;cACA8B,MAAA,CAAAxE,mBAAA;cACAwE,MAAA,CAAAvE,aAAA;cAAA0E,SAAA,CAAAlC,CAAA;cAAAkC,SAAA,CAAAjC,CAAA;cAAA,OAGA,IAAAM,cAAA;YAAA;cAAAlC,QAAA,GAAA6D,SAAA,CAAA5B,CAAA;cACAyB,MAAA,CAAAtE,iBAAA,GAAAY,QAAA,CAAAC,IAAA;cAAA4D,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAlC,CAAA;cAAAiC,GAAA,GAAAC,SAAA,CAAA5B,CAAA;cAEAyB,MAAA,CAAApB,MAAA,CAAAC,QAAA;YAAA;cAAAsB,SAAA,CAAAlC,CAAA;cAEA+B,MAAA,CAAAvE,aAAA;cAAA,OAAA0E,SAAA,CAAAC,CAAA;YAAA;cAAA,OAAAD,SAAA,CAAArB,CAAA;UAAA;QAAA,GAAAmB,QAAA;MAAA;IAEA;IAEA,eACAI,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,aACAE,aAAA,WAAAA,cAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,eACAG,oBAAA,WAAAA,qBAAAH,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,eACAI,oBAAA,WAAAA,qBAAAJ,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,WACAK,MAAA,WAAAA,OAAA;MACA,KAAAxD,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}