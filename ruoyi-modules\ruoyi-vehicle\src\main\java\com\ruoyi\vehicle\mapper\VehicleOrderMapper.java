package com.ruoyi.vehicle.mapper;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleOrder;

/**
 * 用车订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface VehicleOrderMapper 
{
    /**
     * 查询用车订单
     * 
     * @param orderId 用车订单主键
     * @return 用车订单
     */
    public VehicleOrder selectVehicleOrderByOrderId(Long orderId);

    /**
     * 查询用车订单列表
     * 
     * @param vehicleOrder 用车订单
     * @return 用车订单集合
     */
    public List<VehicleOrder> selectVehicleOrderList(VehicleOrder vehicleOrder);

    /**
     * 新增用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    public int insertVehicleOrder(VehicleOrder vehicleOrder);

    /**
     * 修改用车订单
     * 
     * @param vehicleOrder 用车订单
     * @return 结果
     */
    public int updateVehicleOrder(VehicleOrder vehicleOrder);

    /**
     * 删除用车订单
     * 
     * @param orderId 用车订单主键
     * @return 结果
     */
    public int deleteVehicleOrderByOrderId(Long orderId);

    /**
     * 批量删除用车订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVehicleOrderByOrderIds(Long[] orderIds);

    /**
     * 根据订单状态查询订单列表
     * 
     * @param orderStatus 订单状态
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByStatus(String orderStatus);

    /**
     * 根据车辆ID查询订单列表
     * 
     * @param vehicleId 车辆ID
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByVehicleId(Long vehicleId);

    /**
     * 根据队伍ID查询订单列表
     * 
     * @param teamId 队伍ID
     * @return 订单集合
     */
    public List<VehicleOrder> selectVehicleOrderByTeamId(Long teamId);

    /**
     * 根据申请ID查询订单
     * 
     * @param applicationId 申请ID
     * @return 订单信息
     */
    public VehicleOrder selectVehicleOrderByApplicationId(Long applicationId);
}
