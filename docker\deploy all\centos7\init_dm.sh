#!/bin/bash

# 达梦数据库容器初始化脚本 - 完整修复版
# 解决laglangyue/dmdb8:latest镜像的初始化问题

set -e

# 日志函数 - 添加时间戳和级别
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1" | tee -a /opt/dmdbms/log/startup.log
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" | tee -a /opt/dmdbms/log/startup.log
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1" | tee -a /opt/dmdbms/log/startup.log
}

# 设置正确的环境变量
export DM_HOME=/opt/dmdbms
export PATH=$DM_HOME/bin:$PATH
export LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH

log_info "=== 达梦数据库容器启动 ==="
log_info "DM_HOME: $DM_HOME"
log_info "PATH: $PATH"
log_info "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

# 创建日志目录
mkdir -p /opt/dmdbms/log

# 检查并修复库文件路径
log_info "检查达梦数据库安装环境..."
if [ ! -d "$DM_HOME" ]; then
    log_error "DM_HOME目录不存在: $DM_HOME"
    exit 1
fi

# 检查关键可执行文件
if [ ! -f "$DM_HOME/bin/dmserver" ]; then
    log_error "dmserver程序不存在: $DM_HOME/bin/dmserver"
    exit 1
fi

if [ ! -f "$DM_HOME/bin/dminit" ]; then
    log_error "dminit程序不存在: $DM_HOME/bin/dminit"
    exit 1
fi

# 设置执行权限
chmod +x $DM_HOME/bin/dmserver $DM_HOME/bin/dminit 2>/dev/null || true

# 检查并修复库文件
log_info "检查库文件完整性..."
KEY_LIBS=("libdmnsort.so" "libdmdpi.so" "libdmclient.so" "libdmserver.so" "libdmcalc.so")
for lib in "${KEY_LIBS[@]}"; do
    if [ -f "$DM_HOME/bin/$lib" ]; then
        log_info "找到库文件: $lib"
    else
        log_warn "缺少库文件: $lib，尝试修复..."
        
        # 在多个位置查找库文件
        for search_path in "$DM_HOME/bin" "$DM_HOME/lib" "/usr/lib" "/usr/local/lib"; do
            if [ -f "$search_path/$lib" ]; then
                ln -sf "$search_path/$lib" "$DM_HOME/bin/$lib"
                log_info "创建库文件链接: $lib -> $search_path/$lib"
                break
            fi
        done
    fi
done

# 更新库缓存
if command -v ldconfig >/dev/null 2>&1; then
    echo "$DM_HOME/bin" > /etc/ld.so.conf.d/dm.conf 2>/dev/null || true
    ldconfig 2>/dev/null || true
    log_info "库路径已更新"
fi

# 验证库依赖
log_info "验证dminit依赖..."
if command -v ldd >/dev/null 2>&1; then
    missing_libs=$(ldd "$DM_HOME/bin/dminit" 2>/dev/null | grep "not found" || true)
    if [ -n "$missing_libs" ]; then
        log_warn "发现缺失的库: $missing_libs"
    else
        log_info "所有依赖库已满足"
    fi
fi

# 检查系统工具
log_info "检查系统工具..."
if ! command -v netstat >/dev/null 2>&1 && ! command -v ss >/dev/null 2>&1; then
    log_warn "网络工具缺失，跳过端口检查"
fi

# 创建必要的目录结构
log_info "创建数据目录结构..."
mkdir -p /opt/dmdbms/data/DAMENG
mkdir -p /opt/dmdbms/data/ctl_bak
mkdir -p /opt/dmdbms/log
mkdir -p /opt/dmdbms/log/arch
mkdir -p /opt/dmdbms/backup
mkdir -p /opt/dmdbms/temp

# 设置正确的权限
chmod -R 755 /opt/dmdbms/data /opt/dmdbms/log /opt/dmdbms/backup /opt/dmdbms/temp
chown -R $(id -u):$(id -g) /opt/dmdbms/data /opt/dmdbms/log /opt/dmdbms/backup /opt/dmdbms/temp 2>/dev/null || {
    log_warn "设置目录所有者失败，使用当前用户权限运行"
}

# 获取环境变量配置
DB_NAME="${DB_NAME:-DAMENG}"
INSTANCE_NAME="${INSTANCE_NAME:-dm8_test}"
PORT_NUM="${PORT_NUM:-5236}"
SYSDBA_PWD="${SYSDBA_PWD:-SYSDBA001}"

# 预创建必要的目录结构并设置权限
    log_info "预创建必要的目录结构..."
    mkdir -p /opt/dmdbms/data
    mkdir -p /opt/dmdbms/log
    mkdir -p /opt/dmdbms/data/$DB_NAME
    mkdir -p /opt/dmdbms/data/$DB_NAME/bak
    mkdir -p /opt/dmdbms/data/ctl_bak
    mkdir -p /opt/dmdbms/log/arch
    mkdir -p /opt/dmdbms/backup
    mkdir -p /opt/dmdbms/temp
    
    # 设置目录权限 - 使用最宽松的权限确保可写
    chmod -R 777 /opt/dmdbms/data /opt/dmdbms/log /opt/dmdbms/backup /opt/dmdbms/temp 2>/dev/null || true
    chmod -R 777 /opt/dmdbms/data/$DB_NAME 2>/dev/null || true

# 检查数据库初始化状态
DATA_DIR="/opt/dmdbms/data/$DB_NAME"
CONTROL_FILE="$DATA_DIR/dm.ctl"
SYSTEM_DBF="$DATA_DIR/SYSTEM.DBF"
CONFIG_FILE="$DATA_DIR/dm.ini"

log_info "检查数据库初始化状态..."
log_info "数据目录: $DATA_DIR"
log_info "控制文件: $CONTROL_FILE"
log_info "系统文件: $SYSTEM_DBF"

# 检查是否需要初始化
if [ ! -f "$CONTROL_FILE" ] || [ ! -f "$SYSTEM_DBF" ]; then
    log_info "数据库需要初始化，开始初始化流程..."
    
    # 清理可能存在的残留文件
    if [ -d "$DATA_DIR" ]; then
        log_info "清理残留数据..."
        rm -rf "$DATA_DIR"/*
    fi
    
    # 确保目录存在
    mkdir -p "$DATA_DIR"
    
    # 使用dminit初始化数据库
    log_info "使用dminit初始化数据库..."
    log_info "初始化参数:"
    log_info "  路径: /opt/dmdbms/data"
    log_info "  数据库名: $DB_NAME"
    log_info "  实例名: $INSTANCE_NAME"
    log_info "  端口: $PORT_NUM"
    
    # 执行dminit初始化
    if dminit \
        path="/opt/dmdbms/data" \
        db_name="$DB_NAME" \
        instance_name="$INSTANCE_NAME" \
        port_num="$PORT_NUM" \
        page_size=16 \
        extent_size=16 \
        case_sensitive=n \
        charset=1 \
        length_in_char=y \
        time_zone="+08:00" \
        sysdba_pwd="$SYSDBA_PWD"; then
        
        log_info "数据库初始化成功"
        
        # 验证关键文件创建
        if [ -f "$CONTROL_FILE" ] && [ -f "$SYSTEM_DBF" ]; then
            log_info "关键文件验证通过:"
            log_info "  ✓ 控制文件: $CONTROL_FILE"
            log_info "  ✓ 系统文件: $SYSTEM_DBF"
        else
            log_error "关键文件验证失败"
            [ -f "$CONTROL_FILE" ] || log_error "  ✗ 控制文件缺失"
            [ -f "$SYSTEM_DBF" ] || log_error "  ✗ 系统文件缺失"
            exit 1
        fi
    else
        log_error "dminit初始化失败"
        
        # 尝试诊断问题
        log_info "诊断信息:"
        log_info "  dminit路径: $(which dminit 2>/dev/null || echo '未找到')"
        log_info "  权限检查: $(ls -la "$DM_HOME/bin/dminit" 2>/dev/null || echo '文件不存在')"
        log_info "  库文件检查:"
        if command -v ldd >/dev/null 2>&1; then
            ldd "$DM_HOME/bin/dminit" 2>/dev/null || log_warn "无法检查库依赖"
        fi
        
        exit 1
    fi
else
    log_info "数据库已初始化，跳过初始化步骤"
fi

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    log_warn "配置文件不存在，创建默认配置..."
    cat > "$CONFIG_FILE" << EOF
INSTANCE_NAME = $INSTANCE_NAME
PORT_NUM = $PORT_NUM
DB_PATH = $DATA_DIR
CTL_PATH = $DATA_DIR
CTL_BAK_PATH = /opt/dmdbms/data/ctl_bak
LOG_PATH = /opt/dmdbms/log
ARCH_PATH = /opt/dmdbms/log/arch
BAK_PATH = /opt/dmdbms/backup
TEMP_PATH = /opt/dmdbms/temp
PAGE_SIZE = 16
EXTENT_SIZE = 16
CASE_SENSITIVE = N
CHARSET = 1
LENGTH_IN_CHAR = Y
MAX_SESSIONS = 1000
MEMORY_POOL = 500
BUFFER = 1000
LOG_BUFFER_SIZE = 256
SORT_BUF_SIZE = 50
TIME_ZONE = +08:00
EOF
    log_info "配置文件创建完成"
fi

# 最终验证
log_info "最终验证..."
if [ -f "$CONTROL_FILE" ] && [ -f "$SYSTEM_DBF" ] && [ -f "$CONFIG_FILE" ]; then
    log_info "所有验证通过，准备启动数据库"
else
    log_error "验证失败，缺少必要文件"
    exit 1
fi

# 启动数据库服务
log_info "启动达梦数据库服务..."
log_info "使用配置文件: $CONFIG_FILE"

# 检查端口是否被占用
if command -v netstat >/dev/null 2>&1; then
    if netstat -tuln | grep -q ":$PORT_NUM"; then
        log_warn "端口 $PORT_NUM 已被占用"
    fi
elif command -v ss >/dev/null 2>&1; then
    if ss -tuln | grep -q ":$PORT_NUM"; then
        log_warn "端口 $PORT_NUM 已被占用"
    fi
fi

# 启动数据库
cd $DM_HOME/bin
log_info "执行命令: ./dmserver $CONFIG_FILE -noconsole"
exec ./dmserver "$CONFIG_FILE" -noconsole