{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue?vue&type=style&index=0&id=fd59f1fa&scoped=true&lang=css", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\statistics\\index.vue", "mtime": 1754139905404}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754135853197}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754135854613}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754135853218}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zdGF0LWNhcmQgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4Owp9Cgouc3RhdC1pY29uIHsKICB3aWR0aDogNjBweDsKICBoZWlnaHQ6IDYwcHg7CiAgYm9yZGVyLXJhZGl1czogNTAlOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBtYXJnaW4tcmlnaHQ6IDIwcHg7Cn0KCi5zdGF0LWNvbnRlbnQgewogIGZsZXg6IDE7Cn0KCi5zdGF0LW51bWJlciB7CiAgZm9udC1zaXplOiAzMnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjMzAzMTMzOwogIGxpbmUtaGVpZ2h0OiAxOwp9Cgouc3RhdC1sYWJlbCB7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjOTA5Mzk5OwogIG1hcmdpbi10b3A6IDhweDsKfQoKLm1iMjAgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAubA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/statistics", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 查询条件 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"开始日期\" prop=\"startDate\">\n        <el-date-picker\n          v-model=\"queryParams.startDate\"\n          type=\"date\"\n          placeholder=\"选择开始日期\"\n          value-format=\"yyyy-MM-dd\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"结束日期\" prop=\"endDate\">\n        <el-date-picker\n          v-model=\"queryParams.endDate\"\n          type=\"date\"\n          placeholder=\"选择结束日期\"\n          value-format=\"yyyy-MM-dd\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"mb20\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #409EFF;\">\n              <i class=\"el-icon-truck\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalVehicles || 0 }}</div>\n              <div class=\"stat-label\">车辆总数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #67C23A;\">\n              <i class=\"el-icon-s-order\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalOrders || 0 }}</div>\n              <div class=\"stat-label\">订单总数</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #E6A23C;\">\n              <i class=\"el-icon-time\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.totalWorkHours || 0 }}</div>\n              <div class=\"stat-label\">总工作时长</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\" style=\"background: #F56C6C;\">\n              <i class=\"el-icon-warning\" style=\"color: white; font-size: 24px;\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <div class=\"stat-number\">{{ overallStats.utilizationRate || 0 }}%</div>\n              <div class=\"stat-label\">车辆利用率</div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 图表区域 -->\n    <el-row :gutter=\"20\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>车辆类型统计</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshVehicleTypeChart\">刷新</el-button>\n          </div>\n          <div id=\"vehicleTypeChart\" style=\"height: 300px;\"></div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>队伍用车统计</span>\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshTeamChart\">刷新</el-button>\n          </div>\n          <div id=\"teamChart\" style=\"height: 300px;\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>时间趋势统计</span>\n            <el-button-group style=\"float: right;\">\n              <el-button size=\"mini\" @click=\"changeTimeRange('day')\" :type=\"timeRange === 'day' ? 'primary' : ''\">按天</el-button>\n              <el-button size=\"mini\" @click=\"changeTimeRange('month')\" :type=\"timeRange === 'month' ? 'primary' : ''\">按月</el-button>\n            </el-button-group>\n          </div>\n          <div id=\"trendChart\" style=\"height: 400px;\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 排行榜 -->\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>车辆使用排行榜</span>\n          </div>\n          <el-table :data=\"vehicleRanking\" style=\"width: 100%\" size=\"small\">\n            <el-table-column prop=\"rank\" label=\"排名\" width=\"60\" align=\"center\" />\n            <el-table-column prop=\"vehicleModel\" label=\"车辆型号\" />\n            <el-table-column prop=\"usageCount\" label=\"使用次数\" align=\"center\" />\n            <el-table-column prop=\"workHours\" label=\"工作时长\" align=\"center\" />\n          </el-table>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>队伍用车排行榜</span>\n          </div>\n          <el-table :data=\"teamRanking\" style=\"width: 100%\" size=\"small\">\n            <el-table-column prop=\"rank\" label=\"排名\" width=\"60\" align=\"center\" />\n            <el-table-column prop=\"teamName\" label=\"队伍名称\" />\n            <el-table-column prop=\"orderCount\" label=\"订单数量\" align=\"center\" />\n            <el-table-column prop=\"workHours\" label=\"工作时长\" align=\"center\" />\n          </el-table>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 导出按钮 -->\n    <el-row style=\"margin-top: 20px;\">\n      <el-col :span=\"24\" style=\"text-align: center;\">\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"handleExport\">导出统计报表</el-button>\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleRefreshAll\">刷新所有数据</el-button>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: \"VehicleStatistics\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 显示搜索条件\n      showSearch: true,\n      // 查询参数\n      queryParams: {\n        startDate: null,\n        endDate: null,\n        vehicleType: null\n      },\n      // 总体统计数据\n      overallStats: {},\n      // 时间范围\n      timeRange: 'day',\n      // 车辆排行榜\n      vehicleRanking: [],\n      // 队伍排行榜\n      teamRanking: [],\n      // 图表实例\n      charts: {\n        vehicleType: null,\n        team: null,\n        trend: null\n      }\n    };\n  },\n  created() {\n    this.initDefaultDates();\n    this.getOverallStatistics();\n  },\n  mounted() {\n    this.initCharts();\n    this.loadAllData();\n  },\n  beforeDestroy() {\n    // 销毁图表实例\n    Object.values(this.charts).forEach(chart => {\n      if (chart) {\n        chart.dispose();\n      }\n    });\n  },\n  methods: {\n    /** 初始化默认日期 */\n    initDefaultDates() {\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前\n      \n      this.queryParams.startDate = this.formatDate(start);\n      this.queryParams.endDate = this.formatDate(end);\n    },\n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n    /** 初始化图表 */\n    initCharts() {\n      this.charts.vehicleType = echarts.init(document.getElementById('vehicleTypeChart'));\n      this.charts.team = echarts.init(document.getElementById('teamChart'));\n      this.charts.trend = echarts.init(document.getElementById('trendChart'));\n    },\n    /** 获取总体统计 */\n    getOverallStatistics() {\n      // TODO: 调用API获取总体统计数据\n      // 模拟数据\n      this.overallStats = {\n        totalVehicles: 156,\n        totalOrders: 1248,\n        totalWorkHours: 3456,\n        utilizationRate: 78.5\n      };\n    },\n    /** 加载所有数据 */\n    loadAllData() {\n      this.loadVehicleTypeData();\n      this.loadTeamData();\n      this.loadTrendData();\n      this.loadRankingData();\n    },\n    /** 加载车辆类型数据 */\n    loadVehicleTypeData() {\n      // TODO: 调用API获取车辆类型统计数据\n      // 模拟数据\n      const data = [\n        { name: '挖掘机', value: 45 },\n        { name: '推土机', value: 32 },\n        { name: '装载机', value: 28 },\n        { name: '起重机', value: 25 },\n        { name: '运输车', value: 26 }\n      ];\n      \n      const option = {\n        title: {\n          text: '车辆类型分布',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        series: [\n          {\n            name: '车辆数量',\n            type: 'pie',\n            radius: '50%',\n            data: data,\n            emphasis: {\n              itemStyle: {\n                shadowBlur: 10,\n                shadowOffsetX: 0,\n                shadowColor: 'rgba(0, 0, 0, 0.5)'\n              }\n            }\n          }\n        ]\n      };\n      \n      this.charts.vehicleType.setOption(option);\n    },\n    /** 加载队伍数据 */\n    loadTeamData() {\n      // TODO: 调用API获取队伍统计数据\n      // 模拟数据\n      const option = {\n        title: {\n          text: '队伍用车统计',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        xAxis: {\n          type: 'category',\n          data: ['第一施工队', '第二施工队', '第三施工队', '机械维修队', '安全监督队']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [\n          {\n            name: '用车次数',\n            type: 'bar',\n            data: [120, 98, 87, 45, 23],\n            itemStyle: {\n              color: '#409EFF'\n            }\n          }\n        ]\n      };\n      \n      this.charts.team.setOption(option);\n    },\n    /** 加载趋势数据 */\n    loadTrendData() {\n      // TODO: 调用API获取趋势统计数据\n      // 模拟数据\n      const option = {\n        title: {\n          text: '用车趋势统计',\n          left: 'center'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: ['订单数量', '工作时长'],\n          top: 30\n        },\n        xAxis: {\n          type: 'category',\n          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月']\n        },\n        yAxis: [\n          {\n            type: 'value',\n            name: '订单数量'\n          },\n          {\n            type: 'value',\n            name: '工作时长'\n          }\n        ],\n        series: [\n          {\n            name: '订单数量',\n            type: 'line',\n            data: [120, 132, 101, 134, 90, 230, 210, 156]\n          },\n          {\n            name: '工作时长',\n            type: 'bar',\n            yAxisIndex: 1,\n            data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2]\n          }\n        ]\n      };\n      \n      this.charts.trend.setOption(option);\n    },\n    /** 加载排行榜数据 */\n    loadRankingData() {\n      // TODO: 调用API获取排行榜数据\n      // 模拟数据\n      this.vehicleRanking = [\n        { rank: 1, vehicleModel: '卡特320D', usageCount: 45, workHours: 360 },\n        { rank: 2, vehicleModel: '小松PC200', usageCount: 38, workHours: 304 },\n        { rank: 3, vehicleModel: '三一SY215', usageCount: 32, workHours: 256 },\n        { rank: 4, vehicleModel: '徐工XE215', usageCount: 28, workHours: 224 },\n        { rank: 5, vehicleModel: '柳工CLG922', usageCount: 25, workHours: 200 }\n      ];\n      \n      this.teamRanking = [\n        { rank: 1, teamName: '第一施工队', orderCount: 120, workHours: 960 },\n        { rank: 2, teamName: '第二施工队', orderCount: 98, workHours: 784 },\n        { rank: 3, teamName: '第三施工队', orderCount: 87, workHours: 696 },\n        { rank: 4, teamName: '机械维修队', orderCount: 45, workHours: 360 },\n        { rank: 5, teamName: '安全监督队', orderCount: 23, workHours: 184 }\n      ];\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.loadAllData();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.initDefaultDates();\n      this.handleQuery();\n    },\n    /** 刷新车辆类型图表 */\n    refreshVehicleTypeChart() {\n      this.loadVehicleTypeData();\n    },\n    /** 刷新队伍图表 */\n    refreshTeamChart() {\n      this.loadTeamData();\n    },\n    /** 改变时间范围 */\n    changeTimeRange(range) {\n      this.timeRange = range;\n      this.loadTrendData();\n    },\n    /** 导出统计报表 */\n    handleExport() {\n      // TODO: 实现导出功能\n      this.$modal.msgSuccess(\"导出功能开发中...\");\n    },\n    /** 刷新所有数据 */\n    handleRefreshAll() {\n      this.getOverallStatistics();\n      this.loadAllData();\n      this.$modal.msgSuccess(\"数据刷新成功\");\n    }\n  }\n};\n</script>\n\n<style scoped>\n.stat-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 32px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #909399;\n  margin-top: 8px;\n}\n\n.mb20 {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}