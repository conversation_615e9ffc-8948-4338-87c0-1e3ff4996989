package com.ruoyi.vehicle.service;

import java.util.List;
import com.ruoyi.vehicle.domain.VehicleDemandPlan;

/**
 * 车辆需求计划Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IVehicleDemandPlanService 
{
    /**
     * 查询车辆需求计划
     * 
     * @param planId 车辆需求计划主键
     * @return 车辆需求计划
     */
    public VehicleDemandPlan selectVehicleDemandPlanByPlanId(Long planId);

    /**
     * 查询车辆需求计划列表
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 车辆需求计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanList(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 新增车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    public int insertVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 修改车辆需求计划
     * 
     * @param vehicleDemandPlan 车辆需求计划
     * @return 结果
     */
    public int updateVehicleDemandPlan(VehicleDemandPlan vehicleDemandPlan);

    /**
     * 批量删除车辆需求计划
     * 
     * @param planIds 需要删除的车辆需求计划主键集合
     * @return 结果
     */
    public int deleteVehicleDemandPlanByPlanIds(Long[] planIds);

    /**
     * 删除车辆需求计划信息
     * 
     * @param planId 车辆需求计划主键
     * @return 结果
     */
    public int deleteVehicleDemandPlanByPlanId(Long planId);

    /**
     * 提交需求计划
     * 
     * @param vehicleDemandPlan 需求计划信息
     * @param operName 操作人
     * @return 结果
     */
    public int submitDemandPlan(VehicleDemandPlan vehicleDemandPlan, String operName);

    /**
     * 审批需求计划
     * 
     * @param planId 计划ID
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int approveDemandPlan(Long planId, String approvalStatus, String approvalComments, String operName);

    /**
     * 根据审批状态查询计划列表
     * 
     * @param approvalStatus 审批状态
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanByStatus(String approvalStatus);

    /**
     * 根据队伍ID查询计划列表
     * 
     * @param teamId 队伍ID
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanByTeamId(Long teamId);

    /**
     * 根据申请人查询计划列表
     * 
     * @param applicant 申请人
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectVehicleDemandPlanByApplicant(String applicant);

    /**
     * 查询待审批的计划列表
     * 
     * @return 计划集合
     */
    public List<VehicleDemandPlan> selectPendingApprovalPlans();

    /**
     * 批量审批需求计划
     * 
     * @param planIds 计划ID数组
     * @param approvalStatus 审批状态
     * @param approvalComments 审批意见
     * @param operName 操作人
     * @return 结果
     */
    public int batchApproveDemandPlans(Long[] planIds, String approvalStatus, String approvalComments, String operName);
}
