{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\info\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\info\\index.vue", "mtime": 1754138584234}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\babel.config.js", "mtime": 1754122134026}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_auth", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "vehicleInfoList", "title", "open", "queryParams", "pageNum", "pageSize", "vehicleType", "vehicleModel", "unitName", "licensePlate", "vehicleStatus", "form", "rules", "required", "message", "trigger", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "created", "getList", "methods", "_this", "listVehicleInfo", "then", "response", "rows", "cancel", "reset", "vehicleId", "<PERSON><PERSON><PERSON>", "driverPhone", "<PERSON><PERSON><PERSON>", "commander<PERSON><PERSON>", "entryTime", "projectPhase", "shiftConfirmer", "costUnit", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getVehicleInfo", "submitForm", "_this3", "$refs", "validate", "valid", "updateVehicleInfo", "$modal", "msgSuccess", "addVehicleInfo", "handleDelete", "_this4", "vehicleIds", "confirm", "delVehicleInfo", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleImport", "importTemplate", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$alert", "msg", "dangerouslyUseHTMLString", "submitFileForm", "submit", "handleCommand", "command", "$router", "push", "handleStatusChange", "_this5", "text", "updateVehicleStatus"], "sources": ["src/views/vehicle/info/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n        <el-input\n          v-model=\"queryParams.vehicleModel\"\n          placeholder=\"请输入车辆型号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"单位名称\" prop=\"unitName\">\n        <el-input\n          v-model=\"queryParams.unitName\"\n          placeholder=\"请输入单位名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车牌号码\" prop=\"licensePlate\">\n        <el-input\n          v-model=\"queryParams.licensePlate\"\n          placeholder=\"请输入车牌号码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆状态\" prop=\"vehicleStatus\">\n        <el-select v-model=\"queryParams.vehicleStatus\" placeholder=\"请选择车辆状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:info:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:info:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:info:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:info:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['vehicle:info:import']\"\n        >导入</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <!-- 车辆信息表格 -->\n    <el-table v-loading=\"loading\" :data=\"vehicleInfoList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"车辆ID\" align=\"center\" prop=\"vehicleId\" />\n      <el-table-column label=\"车辆类型\" align=\"center\" prop=\"vehicleType\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.vehicle_type\" :value=\"scope.row.vehicleType\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"车辆型号\" align=\"center\" prop=\"vehicleModel\" />\n      <el-table-column label=\"单位名称\" align=\"center\" prop=\"unitName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"licensePlate\" />\n      <el-table-column label=\"司机姓名\" align=\"center\" prop=\"driverName\" />\n      <el-table-column label=\"司机电话\" align=\"center\" prop=\"driverPhone\" />\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"vehicleStatus\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.vehicle_status\" :value=\"scope.row.vehicleStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"入场时间\" align=\"center\" prop=\"entryTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.entryTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:info:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:info:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['vehicle:info:edit']\">\n            <span class=\"el-dropdown-link\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"violation\" icon=\"el-icon-warning\">违章记录</el-dropdown-item>\n              <el-dropdown-item command=\"maintenance\" icon=\"el-icon-setting\">维修记录</el-dropdown-item>\n              <el-dropdown-item command=\"status\" icon=\"el-icon-refresh\">状态管理</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改机械车辆信息对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n              <el-select v-model=\"form.vehicleType\" placeholder=\"请选择车辆类型\">\n                <el-option\n                  v-for=\"dict in dict.type.vehicle_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆型号\" prop=\"vehicleModel\">\n              <el-input v-model=\"form.vehicleModel\" placeholder=\"请输入车辆型号\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"单位名称\" prop=\"unitName\">\n              <el-input v-model=\"form.unitName\" placeholder=\"请输入单位名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车牌号码\" prop=\"licensePlate\">\n              <el-input v-model=\"form.licensePlate\" placeholder=\"请输入车牌号码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"司机姓名\" prop=\"driverName\">\n              <el-input v-model=\"form.driverName\" placeholder=\"请输入司机姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"司机电话\" prop=\"driverPhone\">\n              <el-input v-model=\"form.driverPhone\" placeholder=\"请输入司机电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"指挥姓名\" prop=\"commanderName\">\n              <el-input v-model=\"form.commanderName\" placeholder=\"请输入指挥姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"指挥电话\" prop=\"commanderPhone\">\n              <el-input v-model=\"form.commanderPhone\" placeholder=\"请输入指挥电话\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"入场时间\" prop=\"entryTime\">\n              <el-date-picker clearable\n                v-model=\"form.entryTime\"\n                type=\"datetime\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"请选择入场时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"项目期\" prop=\"projectPhase\">\n              <el-select v-model=\"form.projectPhase\" placeholder=\"请选择项目期\">\n                <el-option label=\"一期\" value=\"一期\"></el-option>\n                <el-option label=\"二期\" value=\"二期\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"车辆状态\" prop=\"vehicleStatus\">\n              <el-select v-model=\"form.vehicleStatus\" placeholder=\"请选择车辆状态\">\n                <el-option\n                  v-for=\"dict in dict.type.vehicle_status\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"费用计量单位\" prop=\"costUnit\">\n              <el-select v-model=\"form.costUnit\" placeholder=\"请选择费用计量单位\">\n                <el-option label=\"天\" value=\"天\"></el-option>\n                <el-option label=\"小时\" value=\"小时\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"台班确认人\" prop=\"shiftConfirmer\">\n          <el-input v-model=\"form.shiftConfirmer\" type=\"textarea\" placeholder=\"请输入台班确认人，多个用逗号分隔\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 车辆信息导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".xlsx, .xls\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\n          <div class=\"el-upload__tip\" slot=\"tip\">\n            仅允许导入xls、xlsx格式文件。\n            <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\n          </div>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listVehicleInfo, getVehicleInfo, delVehicleInfo, addVehicleInfo, updateVehicleInfo } from \"@/api/vehicle/info\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"VehicleInfo\",\n  dicts: ['vehicle_type', 'vehicle_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 机械车辆信息表格数据\n      vehicleInfoList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        vehicleType: null,\n        vehicleModel: null,\n        unitName: null,\n        licensePlate: null,\n        vehicleStatus: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        vehicleType: [\n          { required: true, message: \"车辆类型不能为空\", trigger: \"change\" }\n        ],\n        vehicleModel: [\n          { required: true, message: \"车辆型号不能为空\", trigger: \"blur\" }\n        ],\n        unitName: [\n          { required: true, message: \"单位名称不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 车辆信息导入参数\n      upload: {\n        // 是否显示弹出层（车辆信息导入）\n        open: false,\n        // 弹出层标题（车辆信息导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的车辆信息数据\n        updateSupport: 0,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/vehicle/info/importData\"\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询机械车辆信息列表 */\n    getList() {\n      this.loading = true;\n      listVehicleInfo(this.queryParams).then(response => {\n        this.vehicleInfoList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        vehicleId: null,\n        vehicleType: null,\n        vehicleModel: null,\n        unitName: null,\n        licensePlate: null,\n        driverName: null,\n        driverPhone: null,\n        commanderName: null,\n        commanderPhone: null,\n        entryTime: null,\n        projectPhase: null,\n        vehicleStatus: \"可用\",\n        shiftConfirmer: null,\n        costUnit: \"天\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.vehicleId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加机械车辆信息\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const vehicleId = row.vehicleId || this.ids\n      getVehicleInfo(vehicleId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改机械车辆信息\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.vehicleId != null) {\n            updateVehicleInfo(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addVehicleInfo(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const vehicleIds = row.vehicleId || this.ids;\n      this.$modal.confirm('是否确认删除机械车辆信息编号为\"' + vehicleIds + '\"的数据项？').then(function() {\n        return delVehicleInfo(vehicleIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/info/export', {\n        ...this.queryParams\n      }, `vehicle_info_${new Date().getTime()}.xlsx`)\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"车辆信息导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.download('vehicle/info/importTemplate', {}, `vehicle_info_template_${new Date().getTime()}.xlsx`)\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    /** 更多操作 */\n    handleCommand(command, row) {\n      switch (command) {\n        case \"violation\":\n          this.$router.push(\"/vehicle/violation?vehicleId=\" + row.vehicleId);\n          break;\n        case \"maintenance\":\n          this.$router.push(\"/vehicle/maintenance?vehicleId=\" + row.vehicleId);\n          break;\n        case \"status\":\n          this.handleStatusChange(row);\n          break;\n      }\n    },\n    /** 状态管理 */\n    handleStatusChange(row) {\n      let text = row.vehicleStatus === \"可用\" ? \"停用\" : \"启用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.vehicleModel + '\"车辆吗？').then(function() {\n        return updateVehicleStatus(row.vehicleId, row.vehicleStatus === \"可用\" ? \"故障\" : \"可用\");\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(() => {});\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;AA4TA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,YAAA;QACAC,QAAA;QACAC,YAAA;QACAC,aAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAN,WAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,MAAA;QACA;QACAd,IAAA;QACA;QACAD,KAAA;QACA;QACAgB,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,qBAAA,OAAA3B,WAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA7B,eAAA,GAAAgC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA9B,KAAA,GAAAiC,QAAA,CAAAjC,KAAA;QACA8B,KAAA,CAAAnC,OAAA;MACA;IACA;IACA;IACAwC,MAAA,WAAAA,OAAA;MACA,KAAAhC,IAAA;MACA,KAAAiC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAxB,IAAA;QACAyB,SAAA;QACA9B,WAAA;QACAC,YAAA;QACAC,QAAA;QACAC,YAAA;QACA4B,UAAA;QACAC,WAAA;QACAC,aAAA;QACAC,cAAA;QACAC,SAAA;QACAC,YAAA;QACAhC,aAAA;QACAiC,cAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5C,WAAA,CAAAC,OAAA;MACA,KAAAuB,OAAA;IACA;IACA,aACAqB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvD,GAAA,GAAAuD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,SAAA;MAAA;MACA,KAAAxC,MAAA,GAAAsD,SAAA,CAAAG,MAAA;MACA,KAAAxD,QAAA,IAAAqD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAnB,KAAA;MACA,KAAAjC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAtB,KAAA;MACA,IAAAC,SAAA,GAAAoB,GAAA,CAAApB,SAAA,SAAAzC,GAAA;MACA,IAAA+D,oBAAA,EAAAtB,SAAA,EAAAL,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAA9C,IAAA,GAAAqB,QAAA,CAAAvC,IAAA;QACAgE,MAAA,CAAAvD,IAAA;QACAuD,MAAA,CAAAxD,KAAA;MACA;IACA;IACA,WACA0D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAjD,IAAA,CAAAyB,SAAA;YACA,IAAA4B,uBAAA,EAAAJ,MAAA,CAAAjD,IAAA,EAAAoB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAAjC,OAAA;YACA;UACA;YACA,IAAAwC,oBAAA,EAAAP,MAAA,CAAAjD,IAAA,EAAAoB,IAAA,WAAAC,QAAA;cACA4B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAAjC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,UAAA,GAAAd,GAAA,CAAApB,SAAA,SAAAzC,GAAA;MACA,KAAAsE,MAAA,CAAAM,OAAA,sBAAAD,UAAA,aAAAvC,IAAA;QACA,WAAAyC,oBAAA,EAAAF,UAAA;MACA,GAAAvC,IAAA;QACAsC,MAAA,CAAA1C,OAAA;QACA0C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,4BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA1E,WAAA,mBAAA2E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAjE,MAAA,CAAAf,KAAA;MACA,KAAAe,MAAA,CAAAd,IAAA;IACA;IACA,aACAgF,cAAA,WAAAA,eAAA;MACA,KAAAP,QAAA,6DAAAG,MAAA,KAAAC,IAAA,GAAAC,OAAA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAtE,MAAA,CAAAC,WAAA;IACA;IACA;IACAsE,iBAAA,WAAAA,kBAAAvD,QAAA,EAAAqD,IAAA,EAAAC,QAAA;MACA,KAAAtE,MAAA,CAAAd,IAAA;MACA,KAAAc,MAAA,CAAAC,WAAA;MACA,KAAA4C,KAAA,CAAA7C,MAAA,CAAAwE,UAAA;MACA,KAAAC,MAAA,4FAAAzD,QAAA,CAAA0D,GAAA;QAAAC,wBAAA;MAAA;MACA,KAAAhE,OAAA;IACA;IACA;IACAiE,cAAA,WAAAA,eAAA;MACA,KAAA/B,KAAA,CAAA7C,MAAA,CAAA6E,MAAA;IACA;IACA,WACAC,aAAA,WAAAA,cAAAC,OAAA,EAAAvC,GAAA;MACA,QAAAuC,OAAA;QACA;UACA,KAAAC,OAAA,CAAAC,IAAA,mCAAAzC,GAAA,CAAApB,SAAA;UACA;QACA;UACA,KAAA4D,OAAA,CAAAC,IAAA,qCAAAzC,GAAA,CAAApB,SAAA;UACA;QACA;UACA,KAAA8D,kBAAA,CAAA1C,GAAA;UACA;MACA;IACA;IACA,WACA0C,kBAAA,WAAAA,mBAAA1C,GAAA;MAAA,IAAA2C,MAAA;MACA,IAAAC,IAAA,GAAA5C,GAAA,CAAA9C,aAAA;MACA,KAAAuD,MAAA,CAAAM,OAAA,UAAA6B,IAAA,UAAA5C,GAAA,CAAAjD,YAAA,YAAAwB,IAAA;QACA,OAAAsE,mBAAA,CAAA7C,GAAA,CAAApB,SAAA,EAAAoB,GAAA,CAAA9C,aAAA;MACA,GAAAqB,IAAA;QACAoE,MAAA,CAAAxE,OAAA;QACAwE,MAAA,CAAAlC,MAAA,CAAAC,UAAA,CAAAkC,IAAA;MACA,GAAA3B,KAAA;IACA;EACA;AACA", "ignoreList": []}]}