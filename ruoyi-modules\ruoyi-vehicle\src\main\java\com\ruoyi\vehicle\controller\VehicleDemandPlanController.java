package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleDemandPlan;
import com.ruoyi.vehicle.service.IVehicleDemandPlanService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 车辆需求计划Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/demand")
public class VehicleDemandPlanController extends BaseController
{
    @Autowired
    private IVehicleDemandPlanService vehicleDemandPlanService;

    /**
     * 查询车辆需求计划列表
     */
    @RequiresPermissions("vehicle:demand:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleDemandPlan vehicleDemandPlan)
    {
        startPage();
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectVehicleDemandPlanList(vehicleDemandPlan);
        return getDataTable(list);
    }

    /**
     * 导出车辆需求计划列表
     */
    @RequiresPermissions("vehicle:demand:export")
    @Log(title = "车辆需求计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleDemandPlan vehicleDemandPlan)
    {
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectVehicleDemandPlanList(vehicleDemandPlan);
        ExcelUtil<VehicleDemandPlan> util = new ExcelUtil<VehicleDemandPlan>(VehicleDemandPlan.class);
        util.exportExcel(response, list, "车辆需求计划数据");
    }

    /**
     * 获取车辆需求计划详细信息
     */
    @RequiresPermissions("vehicle:demand:query")
    @GetMapping(value = "/{planId}")
    public AjaxResult getInfo(@PathVariable("planId") Long planId)
    {
        return success(vehicleDemandPlanService.selectVehicleDemandPlanByPlanId(planId));
    }

    /**
     * 新增车辆需求计划
     */
    @RequiresPermissions("vehicle:demand:add")
    @Log(title = "车辆需求计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleDemandPlan vehicleDemandPlan)
    {
        vehicleDemandPlan.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleDemandPlanService.insertVehicleDemandPlan(vehicleDemandPlan));
    }

    /**
     * 修改车辆需求计划
     */
    @RequiresPermissions("vehicle:demand:edit")
    @Log(title = "车辆需求计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleDemandPlan vehicleDemandPlan)
    {
        vehicleDemandPlan.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleDemandPlanService.updateVehicleDemandPlan(vehicleDemandPlan));
    }

    /**
     * 删除车辆需求计划
     */
    @RequiresPermissions("vehicle:demand:remove")
    @Log(title = "车辆需求计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public AjaxResult remove(@PathVariable Long[] planIds)
    {
        return toAjax(vehicleDemandPlanService.deleteVehicleDemandPlanByPlanIds(planIds));
    }

    /**
     * 提交需求计划
     */
    @RequiresPermissions("vehicle:demand:submit")
    @Log(title = "提交需求计划", businessType = BusinessType.UPDATE)
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody VehicleDemandPlan vehicleDemandPlan)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleDemandPlanService.submitDemandPlan(vehicleDemandPlan, operName));
    }

    /**
     * 审批需求计划
     */
    @RequiresPermissions("vehicle:demand:approve")
    @Log(title = "审批需求计划", businessType = BusinessType.UPDATE)
    @PutMapping("/approve/{planId}")
    public AjaxResult approve(@PathVariable Long planId, @RequestBody VehicleDemandPlan vehicleDemandPlan)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleDemandPlanService.approveDemandPlan(
            planId, 
            vehicleDemandPlan.getApprovalStatus(), 
            vehicleDemandPlan.getApprovalComments(), 
            operName
        ));
    }

    /**
     * 根据审批状态查询计划列表
     */
    @RequiresPermissions("vehicle:demand:list")
    @GetMapping("/status/{approvalStatus}")
    public AjaxResult getByStatus(@PathVariable String approvalStatus)
    {
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectVehicleDemandPlanByStatus(approvalStatus);
        return success(list);
    }

    /**
     * 根据队伍ID查询计划列表
     */
    @RequiresPermissions("vehicle:demand:list")
    @GetMapping("/team/{teamId}")
    public AjaxResult getByTeamId(@PathVariable Long teamId)
    {
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectVehicleDemandPlanByTeamId(teamId);
        return success(list);
    }

    /**
     * 查询我的需求计划
     */
    @RequiresPermissions("vehicle:demand:list")
    @GetMapping("/my-plans")
    public AjaxResult getMyPlans()
    {
        String applicant = SecurityUtils.getUsername();
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectVehicleDemandPlanByApplicant(applicant);
        return success(list);
    }

    /**
     * 查询待审批的计划列表
     */
    @RequiresPermissions("vehicle:demand:approve")
    @GetMapping("/pending-approval")
    public AjaxResult getPendingApproval()
    {
        List<VehicleDemandPlan> list = vehicleDemandPlanService.selectPendingApprovalPlans();
        return success(list);
    }

    /**
     * 批量审批需求计划
     */
    @RequiresPermissions("vehicle:demand:approve")
    @Log(title = "批量审批需求计划", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-approve")
    public AjaxResult batchApprove(@RequestBody VehicleDemandPlan vehicleDemandPlan)
    {
        String operName = SecurityUtils.getUsername();
        Long[] planIds = vehicleDemandPlan.getPlanId() != null ? 
            new Long[]{vehicleDemandPlan.getPlanId()} : new Long[0];
        
        int result = vehicleDemandPlanService.batchApproveDemandPlans(
            planIds, 
            vehicleDemandPlan.getApprovalStatus(), 
            vehicleDemandPlan.getApprovalComments(), 
            operName
        );
        
        return success("批量审批完成，成功处理 " + result + " 条需求计划");
    }
}
