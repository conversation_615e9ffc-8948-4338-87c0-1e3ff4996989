package com.ruoyi.vehicle.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.vehicle.domain.VehicleNotification;
import com.ruoyi.vehicle.service.IVehicleNotificationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;

/**
 * 消息通知Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/notification")
public class VehicleNotificationController extends BaseController
{
    @Autowired
    private IVehicleNotificationService vehicleNotificationService;

    /**
     * 查询消息通知列表
     */
    @RequiresPermissions("vehicle:notification:list")
    @GetMapping("/list")
    public TableDataInfo list(VehicleNotification vehicleNotification)
    {
        startPage();
        List<VehicleNotification> list = vehicleNotificationService.selectVehicleNotificationList(vehicleNotification);
        return getDataTable(list);
    }

    /**
     * 导出消息通知列表
     */
    @RequiresPermissions("vehicle:notification:export")
    @Log(title = "消息通知", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VehicleNotification vehicleNotification)
    {
        List<VehicleNotification> list = vehicleNotificationService.selectVehicleNotificationList(vehicleNotification);
        ExcelUtil<VehicleNotification> util = new ExcelUtil<VehicleNotification>(VehicleNotification.class);
        util.exportExcel(response, list, "消息通知数据");
    }

    /**
     * 获取消息通知详细信息
     */
    @RequiresPermissions("vehicle:notification:query")
    @GetMapping(value = "/{notificationId}")
    public AjaxResult getInfo(@PathVariable("notificationId") Long notificationId)
    {
        return success(vehicleNotificationService.selectVehicleNotificationByNotificationId(notificationId));
    }

    /**
     * 新增消息通知
     */
    @RequiresPermissions("vehicle:notification:add")
    @Log(title = "消息通知", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VehicleNotification vehicleNotification)
    {
        vehicleNotification.setCreateBy(SecurityUtils.getUsername());
        return toAjax(vehicleNotificationService.insertVehicleNotification(vehicleNotification));
    }

    /**
     * 修改消息通知
     */
    @RequiresPermissions("vehicle:notification:edit")
    @Log(title = "消息通知", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VehicleNotification vehicleNotification)
    {
        vehicleNotification.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(vehicleNotificationService.updateVehicleNotification(vehicleNotification));
    }

    /**
     * 删除消息通知
     */
    @RequiresPermissions("vehicle:notification:remove")
    @Log(title = "消息通知", businessType = BusinessType.DELETE)
    @DeleteMapping("/{notificationIds}")
    public AjaxResult remove(@PathVariable Long[] notificationIds)
    {
        return toAjax(vehicleNotificationService.deleteVehicleNotificationByNotificationIds(notificationIds));
    }

    /**
     * 标记消息为已读
     */
    @RequiresPermissions("vehicle:notification:edit")
    @Log(title = "标记消息已读", businessType = BusinessType.UPDATE)
    @PutMapping("/mark-read/{notificationId}")
    public AjaxResult markAsRead(@PathVariable Long notificationId)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleNotificationService.markAsRead(notificationId, operName));
    }

    /**
     * 查询我的通知列表
     */
    @RequiresPermissions("vehicle:notification:list")
    @GetMapping("/my-notifications")
    public AjaxResult getMyNotifications()
    {
        String recipient = SecurityUtils.getUsername();
        List<VehicleNotification> list = vehicleNotificationService.selectNotificationByRecipient(recipient);
        return success(list);
    }

    /**
     * 查询未读通知数量
     */
    @RequiresPermissions("vehicle:notification:list")
    @GetMapping("/unread-count")
    public AjaxResult getUnreadCount()
    {
        String recipient = SecurityUtils.getUsername();
        int count = vehicleNotificationService.countUnreadNotifications(recipient);
        return success(count);
    }

    /**
     * 根据业务ID和类型查询通知
     */
    @RequiresPermissions("vehicle:notification:list")
    @GetMapping("/business/{businessType}/{businessId}")
    public AjaxResult getByBusinessIdAndType(@PathVariable String businessType, @PathVariable Long businessId)
    {
        List<VehicleNotification> list = vehicleNotificationService.selectNotificationByBusinessIdAndType(businessId, businessType);
        return success(list);
    }

    /**
     * 重新发送失败的通知
     */
    @RequiresPermissions("vehicle:notification:edit")
    @Log(title = "重新发送通知", businessType = BusinessType.UPDATE)
    @PutMapping("/resend/{notificationId}")
    public AjaxResult resend(@PathVariable Long notificationId)
    {
        return toAjax(vehicleNotificationService.resendFailedNotification(notificationId));
    }

    /**
     * 发送需求计划通知
     */
    @RequiresPermissions("vehicle:notification:send")
    @Log(title = "发送需求计划通知", businessType = BusinessType.INSERT)
    @PostMapping("/send-demand-plan/{planId}/{notifyType}")
    public AjaxResult sendDemandPlanNotification(@PathVariable Long planId, @PathVariable String notifyType)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleNotificationService.sendDemandPlanNotification(planId, notifyType, operName));
    }

    /**
     * 发送用车申请通知
     */
    @RequiresPermissions("vehicle:notification:send")
    @Log(title = "发送用车申请通知", businessType = BusinessType.INSERT)
    @PostMapping("/send-application/{applicationId}/{notifyType}")
    public AjaxResult sendApplicationNotification(@PathVariable Long applicationId, @PathVariable String notifyType)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleNotificationService.sendApplicationNotification(applicationId, notifyType, operName));
    }

    /**
     * 发送订单通知
     */
    @RequiresPermissions("vehicle:notification:send")
    @Log(title = "发送订单通知", businessType = BusinessType.INSERT)
    @PostMapping("/send-order/{orderId}/{notifyType}")
    public AjaxResult sendOrderNotification(@PathVariable Long orderId, @PathVariable String notifyType)
    {
        String operName = SecurityUtils.getUsername();
        return toAjax(vehicleNotificationService.sendOrderNotification(orderId, notifyType, operName));
    }

    /**
     * 批量发送通知
     */
    @RequiresPermissions("vehicle:notification:send")
    @Log(title = "批量发送通知", businessType = BusinessType.INSERT)
    @PostMapping("/batch-send")
    public AjaxResult batchSend(@RequestBody List<VehicleNotification> notifications)
    {
        int result = vehicleNotificationService.batchSendNotifications(notifications);
        return success("批量发送完成，成功发送 " + result + " 条通知");
    }
}
