{"remainingRequest": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\car\\AA\\ruoyi-ui\\src\\views\\vehicle\\application\\index.vue", "mtime": 1754144196211}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754135853223}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754135852981}, {"path": "D:\\Work\\car\\AA\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754135853905}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RBcHBsaWNhdGlvbiwgZ2V0QXBwbGljYXRpb24sIGRlbEFwcGxpY2F0aW9uLCBleHBvcnRBcHBsaWNhdGlvbiB9IGZyb20gIkAvYXBpL3ZlaGljbGUvYXBwbGljYXRpb24iOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJBcHBsaWNhdGlvbiIsCiAgZGljdHM6IFsndmVoaWNsZV90eXBlJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDnlKjovabnlLPor7fooajmoLzmlbDmja4KICAgICAgYXBwbGljYXRpb25MaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDor6bmg4Xlr7nor53moYYKICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGRldGFpbEFwcGxpY2F0aW9uOiB7fSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGFwcGxpY2F0aW9uVGl0bGU6IG51bGwsCiAgICAgICAgdmVoaWNsZVR5cGU6IG51bGwsCiAgICAgICAgYXBwcm92YWxTdGF0dXM6IG51bGwsCiAgICAgICAgYXBwbGljYW50OiBudWxsCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i55So6L2m55Sz6K+35YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcyA9IHt9OwogICAgICBpZiAobnVsbCAhPSB0aGlzLmRhdGVSYW5nZSAmJiAnJyAhPSB0aGlzLmRhdGVSYW5nZSkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJiZWdpblN0YXJ0VGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMF07CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXNbImVuZFN0YXJ0VGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgbGlzdEFwcGxpY2F0aW9uKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuYXBwbGljYXRpb25MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5hcHBsaWNhdGlvbklkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL3ZlaGljbGUvYXBwbGljYXRpb24vYXBwbHknKTsKICAgIH0sCiAgICAKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgY29uc3QgYXBwbGljYXRpb25JZCA9IHJvdy5hcHBsaWNhdGlvbklkIHx8IHRoaXMuaWRzCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvdmVoaWNsZS9hcHBsaWNhdGlvbi9lZGl0LycgKyBhcHBsaWNhdGlvbklkKTsKICAgIH0sCiAgICAKICAgIC8qKiDmn6XnnIvor6bmg4UgKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIGdldEFwcGxpY2F0aW9uKHJvdy5hcHBsaWNhdGlvbklkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmRldGFpbEFwcGxpY2F0aW9uID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICAKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgYXBwbGljYXRpb25JZHMgPSByb3cuYXBwbGljYXRpb25JZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55So6L2m55Sz6K+357yW5Y+35Li6IicgKyBhcHBsaWNhdGlvbklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsQXBwbGljYXRpb24oYXBwbGljYXRpb25JZHMpOwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIAogICAgLyoqIOiwg+W6puWuieaOkiAqLwogICAgaGFuZGxlRGlzcGF0Y2goKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvdmVoaWNsZS9hcHBsaWNhdGlvbi9kaXNwYXRjaCcpOwogICAgfSwKICAgIAogICAgLyoqIOWNleS4quiwg+W6piAqLwogICAgaGFuZGxlRGlzcGF0Y2hTaW5nbGUocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvdmVoaWNsZS9hcHBsaWNhdGlvbi9kaXNwYXRjaD9hcHBsaWNhdGlvbklkPScgKyByb3cuYXBwbGljYXRpb25JZCk7CiAgICB9LAogICAgCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ3ZlaGljbGUvYXBwbGljYXRpb24vZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGFwcGxpY2F0aW9uXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLwogICAgZ2V0U3RhdHVzVGFnVHlwZShzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICdwZW5kaW5nJzogJ3dhcm5pbmcnLAogICAgICAgICdhcHByb3ZlZCc6ICdzdWNjZXNzJywKICAgICAgICAncmVqZWN0ZWQnOiAnZGFuZ2VyJywKICAgICAgICAnZGlzcGF0Y2hlZCc6ICdpbmZvJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ2luZm8nOwogICAgfSwKICAgIAogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICdwZW5kaW5nJzogJ+W+heWuoeaJuScsCiAgICAgICAgJ2FwcHJvdmVkJzogJ+W3suWuoeaJuScsCiAgICAgICAgJ3JlamVjdGVkJzogJ+W3suaLkue7nScsCiAgICAgICAgJ2Rpc3BhdGNoZWQnOiAn5bey6LCD5bqmJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpSc7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6OA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vehicle/application", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"申请标题\" prop=\"applicationTitle\">\n        <el-input\n          v-model=\"queryParams.applicationTitle\"\n          placeholder=\"请输入申请标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"车辆类型\" prop=\"vehicleType\">\n        <el-select v-model=\"queryParams.vehicleType\" placeholder=\"请选择车辆类型\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.vehicle_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"审批状态\" prop=\"approvalStatus\">\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"请选择审批状态\" clearable>\n          <el-option label=\"待审批\" value=\"pending\"></el-option>\n          <el-option label=\"已审批\" value=\"approved\"></el-option>\n          <el-option label=\"已拒绝\" value=\"rejected\"></el-option>\n          <el-option label=\"已调度\" value=\"dispatched\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"申请人\" prop=\"applicant\">\n        <el-input\n          v-model=\"queryParams.applicant\"\n          placeholder=\"请输入申请人\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"申请时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['vehicle:application:add']\"\n        >新增申请</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['vehicle:application:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['vehicle:application:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-s-operation\"\n          size=\"mini\"\n          @click=\"handleDispatch\"\n        >调度安排</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['vehicle:application:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"applicationList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"申请ID\" align=\"center\" prop=\"applicationId\" width=\"80\" />\n      <el-table-column label=\"申请标题\" align=\"center\" prop=\"applicationTitle\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"车辆需求\" align=\"center\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.vehicleType }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.vehicleModel }}</div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"用车时间\" align=\"center\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <div>{{ parseTime(scope.row.startTime, '{m}-{d} {h}:{i}') }}</div>\n          <div style=\"color: #909399; font-size: 12px;\">\n            至 {{ parseTime(scope.row.endTime, '{m}-{d} {h}:{i}') }}\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicant\" width=\"100\" />\n      <el-table-column label=\"用车地点\" align=\"center\" prop=\"usageLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\" size=\"mini\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"分配情况\" align=\"center\" width=\"150\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.assignedVehicleId\">\n            <div style=\"color: #67C23A;\">已分配车辆</div>\n            <div style=\"color: #909399; font-size: 12px;\">{{ scope.row.assignedDriver }}</div>\n          </div>\n          <div v-else style=\"color: #E6A23C;\">待分配</div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['vehicle:application:edit']\"\n            v-if=\"scope.row.approvalStatus === 'pending'\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['vehicle:application:remove']\"\n            v-if=\"scope.row.approvalStatus === 'pending'\"\n          >删除</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-s-operation\"\n            @click=\"handleDispatchSingle(scope.row)\"\n            v-hasPermi=\"['vehicle:application:dispatch']\"\n            v-if=\"scope.row.approvalStatus === 'approved' && !scope.row.assignedVehicleId\"\n          >调度</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 申请详情对话框 -->\n    <el-dialog title=\"申请详情\" :visible.sync=\"detailDialogVisible\" width=\"800px\" append-to-body>\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item label=\"申请ID\">{{ detailApplication.applicationId }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请标题\">{{ detailApplication.applicationTitle }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆类型\">{{ detailApplication.vehicleType }}</el-descriptions-item>\n        <el-descriptions-item label=\"车辆型号\">{{ detailApplication.vehicleModel }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车地点\" :span=\"2\">{{ detailApplication.usageLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"施工作业说明\" :span=\"2\">{{ detailApplication.workDescription }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车开始时间\">{{ parseTime(detailApplication.startTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"用车结束时间\">{{ parseTime(detailApplication.endTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"申请人\">{{ detailApplication.applicant }}</el-descriptions-item>\n        <el-descriptions-item label=\"联系方式\">{{ detailApplication.applicantPhone }}</el-descriptions-item>\n        <el-descriptions-item label=\"审批状态\">\n          <el-tag :type=\"getStatusTagType(detailApplication.approvalStatus)\">\n            {{ getStatusText(detailApplication.approvalStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"申请时间\">{{ parseTime(detailApplication.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"分配车辆\" v-if=\"detailApplication.assignedVehicleId\">\n          {{ detailApplication.assignedVehicleId }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"分配司机\" v-if=\"detailApplication.assignedDriver\">\n          {{ detailApplication.assignedDriver }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"调度时间\" v-if=\"detailApplication.dispatchTime\">\n          {{ parseTime(detailApplication.dispatchTime) }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"调度人员\" v-if=\"detailApplication.dispatchPerson\">\n          {{ detailApplication.dispatchPerson }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"调度备注\" v-if=\"detailApplication.dispatchRemark\" :span=\"2\">\n          {{ detailApplication.dispatchRemark }}\n        </el-descriptions-item>\n        <el-descriptions-item label=\"备注\" v-if=\"detailApplication.remark\" :span=\"2\">\n          {{ detailApplication.remark }}\n        </el-descriptions-item>\n      </el-descriptions>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listApplication, getApplication, delApplication, exportApplication } from \"@/api/vehicle/application\";\n\nexport default {\n  name: \"Application\",\n  dicts: ['vehicle_type'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 用车申请表格数据\n      applicationList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 详情对话框\n      detailDialogVisible: false,\n      detailApplication: {},\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        applicationTitle: null,\n        vehicleType: null,\n        approvalStatus: null,\n        applicant: null\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询用车申请列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.params = {};\n      if (null != this.dateRange && '' != this.dateRange) {\n        this.queryParams.params[\"beginStartTime\"] = this.dateRange[0];\n        this.queryParams.params[\"endStartTime\"] = this.dateRange[1];\n      }\n      listApplication(this.queryParams).then(response => {\n        this.applicationList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    \n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    \n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.applicationId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    \n    /** 新增按钮操作 */\n    handleAdd() {\n      this.$router.push('/vehicle/application/apply');\n    },\n    \n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const applicationId = row.applicationId || this.ids\n      this.$router.push('/vehicle/application/edit/' + applicationId);\n    },\n    \n    /** 查看详情 */\n    handleView(row) {\n      getApplication(row.applicationId).then(response => {\n        this.detailApplication = response.data;\n        this.detailDialogVisible = true;\n      });\n    },\n    \n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const applicationIds = row.applicationId || this.ids;\n      this.$modal.confirm('是否确认删除用车申请编号为\"' + applicationIds + '\"的数据项？').then(function() {\n        return delApplication(applicationIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    \n    /** 调度安排 */\n    handleDispatch() {\n      this.$router.push('/vehicle/application/dispatch');\n    },\n    \n    /** 单个调度 */\n    handleDispatchSingle(row) {\n      this.$router.push('/vehicle/application/dispatch?applicationId=' + row.applicationId);\n    },\n    \n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('vehicle/application/export', {\n        ...this.queryParams\n      }, `application_${new Date().getTime()}.xlsx`)\n    },\n    \n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const statusMap = {\n        'pending': 'warning',\n        'approved': 'success',\n        'rejected': 'danger',\n        'dispatched': 'info'\n      };\n      return statusMap[status] || 'info';\n    },\n    \n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待审批',\n        'approved': '已审批',\n        'rejected': '已拒绝',\n        'dispatched': '已调度'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};\n</script>\n"]}]}