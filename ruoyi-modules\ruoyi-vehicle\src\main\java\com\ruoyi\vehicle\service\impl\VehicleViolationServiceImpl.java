package com.ruoyi.vehicle.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vehicle.mapper.VehicleViolationMapper;
import com.ruoyi.vehicle.domain.VehicleViolation;
import com.ruoyi.vehicle.service.IVehicleViolationService;

/**
 * 车辆违章记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class VehicleViolationServiceImpl implements IVehicleViolationService 
{
    @Autowired
    private VehicleViolationMapper vehicleViolationMapper;

    /**
     * 查询车辆违章记录
     * 
     * @param violationId 车辆违章记录主键
     * @return 车辆违章记录
     */
    @Override
    public VehicleViolation selectVehicleViolationByViolationId(Long violationId)
    {
        return vehicleViolationMapper.selectVehicleViolationByViolationId(violationId);
    }

    /**
     * 查询车辆违章记录列表
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 车辆违章记录
     */
    @Override
    public List<VehicleViolation> selectVehicleViolationList(VehicleViolation vehicleViolation)
    {
        return vehicleViolationMapper.selectVehicleViolationList(vehicleViolation);
    }

    /**
     * 新增车辆违章记录
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 结果
     */
    @Override
    public int insertVehicleViolation(VehicleViolation vehicleViolation)
    {
        vehicleViolation.setCreateTime(DateUtils.getNowDate());
        return vehicleViolationMapper.insertVehicleViolation(vehicleViolation);
    }

    /**
     * 修改车辆违章记录
     * 
     * @param vehicleViolation 车辆违章记录
     * @return 结果
     */
    @Override
    public int updateVehicleViolation(VehicleViolation vehicleViolation)
    {
        vehicleViolation.setUpdateTime(DateUtils.getNowDate());
        return vehicleViolationMapper.updateVehicleViolation(vehicleViolation);
    }

    /**
     * 批量删除车辆违章记录
     * 
     * @param violationIds 需要删除的车辆违章记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleViolationByViolationIds(Long[] violationIds)
    {
        return vehicleViolationMapper.deleteVehicleViolationByViolationIds(violationIds);
    }

    /**
     * 删除车辆违章记录信息
     * 
     * @param violationId 车辆违章记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleViolationByViolationId(Long violationId)
    {
        return vehicleViolationMapper.deleteVehicleViolationByViolationId(violationId);
    }

    /**
     * 根据车辆ID查询违章记录列表
     *
     * @param vehicleId 车辆ID
     * @return 违章记录集合
     */
    @Override
    public List<VehicleViolation> selectVehicleViolationByVehicleId(Long vehicleId)
    {
        VehicleViolation query = new VehicleViolation();
        query.setVehicleId(vehicleId);
        return vehicleViolationMapper.selectVehicleViolationList(query);
    }

    /**
     * 根据处理状态查询违章记录列表
     *
     * @param status 处理状态
     * @return 违章记录集合
     */
    @Override
    public List<VehicleViolation> selectVehicleViolationByStatus(String status)
    {
        VehicleViolation query = new VehicleViolation();
        query.setStatus(status);
        return vehicleViolationMapper.selectVehicleViolationList(query);
    }

    /**
     * 处理违章记录
     *
     * @param violationId 违章记录ID
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int processViolation(Long violationId, String operName)
    {
        VehicleViolation violation = new VehicleViolation();
        violation.setViolationId(violationId);
        violation.setStatus("1"); // 1表示已处理
        violation.setUpdateBy(operName);
        violation.setUpdateTime(DateUtils.getNowDate());

        return vehicleViolationMapper.updateVehicleViolation(violation);
    }

    /**
     * 批量处理违章记录
     *
     * @param violationIds 违章记录ID数组
     * @param operName 操作人
     * @return 结果
     */
    @Override
    public int batchProcessViolation(Long[] violationIds, String operName)
    {
        int result = 0;
        for (Long violationId : violationIds) {
            result += processViolation(violationId, operName);
        }
        return result;
    }

}
